#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢组学差异代谢物注释和富集分析
作者: AI Assistant
日期: 2025-08-01
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import requests
import json
import time
from urllib.parse import quote
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MetabolomicsAnalyzer:
    def __init__(self, csv_file):
        """
        初始化代谢组学分析器
        
        Parameters:
        csv_file (str): 差异代谢物CSV文件路径
        """
        self.csv_file = csv_file
        self.data = None
        self.differential_metabolites = None
        self.annotations = {}
        
    def load_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.csv_file, encoding='utf-8')
        
        # 提取差异代谢物信息
        self.differential_metabolites = pd.DataFrame({
            'CPD_ID': self.data['CPD_ID'],
            'CPD_Name': self.data['CPD_Name'],
            'FC': self.data['FC(HFsnEF_vs_HFnEF)'],
            'pValue': self.data['pValue(HFsnEF_vs_HFnEF)'],
            'FDR': self.data['FDR(HFsnEF_vs_HFnEF)'],
            'Regulation': self.data['Sig(HFsnEF_vs_HFnEF)'].apply(lambda x: 'Up' if x == 1 else 'Down')
        })
        
        print(f"成功加载 {len(self.differential_metabolites)} 个差异代谢物")
        print(f"上调代谢物: {sum(self.differential_metabolites['Regulation'] == 'Up')} 个")
        print(f"下调代谢物: {sum(self.differential_metabolites['Regulation'] == 'Down')} 个")
        
        return self.differential_metabolites
    
    def search_hmdb(self, compound_name, max_retries=3):
        """
        在HMDB数据库中搜索化合物信息
        
        Parameters:
        compound_name (str): 化合物名称
        max_retries (int): 最大重试次数
        
        Returns:
        dict: HMDB注释信息
        """
        for attempt in range(max_retries):
            try:
                # 使用HMDB API搜索
                base_url = "http://www.hmdb.ca/metabolites.json"
                params = {
                    'query': compound_name,
                    'format': 'json'
                }
                
                response = requests.get(base_url, params=params, timeout=100)
                if response.status_code == 200:
                    data = response.json()
                    if data and len(data) > 0:
                        metabolite = data[0]  # 取第一个匹配结果
                        return {
                            'HMDB_ID': metabolite.get('accession', 'N/A'),
                            'Name': metabolite.get('name', 'N/A'),
                            'Chemical_Formula': metabolite.get('chemical_formula', 'N/A'),
                            'Molecular_Weight': metabolite.get('monisotopic_molecular_weight', 'N/A'),
                            'Classification': metabolite.get('taxonomy', {}).get('super_class', 'N/A'),
                            'Biological_Role': metabolite.get('biological_properties', {}).get('general_function', 'N/A')
                        }
                
                time.sleep(1)  # 避免请求过于频繁
                
            except Exception as e:
                print(f"HMDB搜索失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                time.sleep(2)
        
        return {
            'HMDB_ID': 'N/A',
            'Name': compound_name,
            'Chemical_Formula': 'N/A',
            'Molecular_Weight': 'N/A',
            'Classification': 'N/A',
            'Biological_Role': 'N/A'
        }
    
    def search_kegg(self, compound_name, max_retries=3):
        """
        在KEGG数据库中搜索化合物信息
        
        Parameters:
        compound_name (str): 化合物名称
        max_retries (int): 最大重试次数
        
        Returns:
        dict: KEGG注释信息
        """
        for attempt in range(max_retries):
            try:
                # 使用KEGG REST API搜索
                search_url = f"http://rest.kegg.jp/find/compound/{quote(compound_name)}"
                response = requests.get(search_url, timeout=100)
                
                if response.status_code == 200 and response.text.strip():
                    lines = response.text.strip().split('\n')
                    if lines and lines[0]:
                        kegg_id = lines[0].split('\t')[0].replace('cpd:', '')
                        
                        # 获取详细信息
                        detail_url = f"http://rest.kegg.jp/get/cpd:{kegg_id}"
                        detail_response = requests.get(detail_url, timeout=100)
                        
                        if detail_response.status_code == 200:
                            detail_text = detail_response.text
                            
                            # 解析KEGG信息
                            kegg_info = {
                                'KEGG_ID': kegg_id,
                                'Name': compound_name,
                                'Formula': 'N/A',
                                'Pathways': []
                            }
                            
                            # 提取分子式
                            for line in detail_text.split('\n'):
                                if line.startswith('FORMULA'):
                                    kegg_info['Formula'] = line.split()[1] if len(line.split()) > 1 else 'N/A'
                                elif line.startswith('PATHWAY'):
                                    pathway = line.replace('PATHWAY', '').strip()
                                    if pathway:
                                        kegg_info['Pathways'].append(pathway)
                            
                            return kegg_info
                
                time.sleep(1)
                
            except Exception as e:
                print(f"KEGG搜索失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                time.sleep(2)
        
        return {
            'KEGG_ID': 'N/A',
            'Name': compound_name,
            'Formula': 'N/A',
            'Pathways': []
        }
    
    def annotate_metabolites(self):
        """对所有差异代谢物进行数据库注释"""
        print("开始进行代谢物数据库注释...")
        
        annotations = []
        total = len(self.differential_metabolites)
        
        for idx, row in self.differential_metabolites.iterrows():
            print(f"正在注释 {idx + 1}/{total}: {row['CPD_Name']}")
            
            # HMDB注释
            hmdb_info = self.search_hmdb(row['CPD_Name'])
            
            # KEGG注释
            kegg_info = self.search_kegg(row['CPD_Name'])
            
            # 合并注释信息
            annotation = {
                'CPD_ID': row['CPD_ID'],
                'CPD_Name': row['CPD_Name'],
                'FC': row['FC'],
                'pValue': row['pValue'],
                'FDR': row['FDR'],
                'Regulation': row['Regulation'],
                'HMDB_ID': hmdb_info['HMDB_ID'],
                'Chemical_Formula': hmdb_info['Chemical_Formula'],
                'Molecular_Weight': hmdb_info['Molecular_Weight'],
                'Classification': hmdb_info['Classification'],
                'Biological_Role': hmdb_info['Biological_Role'],
                'KEGG_ID': kegg_info['KEGG_ID'],
                'KEGG_Formula': kegg_info['Formula'],
                'KEGG_Pathways': '; '.join(kegg_info['Pathways']) if kegg_info['Pathways'] else 'N/A'
            }
            
            annotations.append(annotation)
            
            # 添加延迟避免API限制
            time.sleep(0.5)
        
        self.annotations = pd.DataFrame(annotations)
        
        # 保存注释结果
        self.annotations.to_csv('metabolite_annotations.csv', index=False, encoding='utf-8-sig')
        print("注释完成！结果已保存到 metabolite_annotations.csv")
        
        return self.annotations
    
    def create_volcano_plot(self):
        """创建火山图"""
        if self.differential_metabolites is None:
            print("请先加载数据")
            return
        
        plt.figure(figsize=(10, 8))
        
        # 计算-log10(p-value)
        neg_log_p = -np.log10(self.differential_metabolites['pValue'])
        log2_fc = np.log2(self.differential_metabolites['FC'])
        
        # 设置颜色
        colors = []
        for reg in self.differential_metabolites['Regulation']:
            if reg == 'Up':
                colors.append('red')
            else:
                colors.append('blue')
        
        # 绘制散点图
        plt.scatter(log2_fc, neg_log_p, c=colors, alpha=0.7, s=50)
        
        # 添加阈值线
        plt.axhline(y=-np.log10(0.05), color='gray', linestyle='--', alpha=0.5)
        plt.axvline(x=1, color='gray', linestyle='--', alpha=0.5)
        plt.axvline(x=-1, color='gray', linestyle='--', alpha=0.5)
        
        plt.xlabel('Log2(Fold Change)', fontsize=12)
        plt.ylabel('-Log10(P-value)', fontsize=12)
        plt.title('差异代谢物火山图', fontsize=14, fontweight='bold')
        
        # 添加图例
        plt.scatter([], [], c='red', alpha=0.7, s=50, label=f'上调 (n={sum(self.differential_metabolites["Regulation"] == "Up")})')
        plt.scatter([], [], c='blue', alpha=0.7, s=50, label=f'下调 (n={sum(self.differential_metabolites["Regulation"] == "Down")})')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('volcano_plot.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("火山图已保存为 volcano_plot.png")

    def perform_enrichment_analysis(self):
        """进行富集分析"""
        if self.annotations is None or self.annotations.empty:
            print("请先完成代谢物注释")
            return

        print("开始进行富集分析...")

        # 化学分类富集分析
        classification_counts = self.annotations['Classification'].value_counts()
        classification_counts = classification_counts[classification_counts.index != 'N/A']

        # 调节方向分析
        regulation_analysis = self.annotations.groupby(['Classification', 'Regulation']).size().unstack(fill_value=0)

        # 保存富集分析结果
        enrichment_results = {
            'chemical_classification': classification_counts.to_dict(),
            'regulation_by_class': regulation_analysis.to_dict()
        }

        # 保存为JSON文件
        with open('enrichment_results.json', 'w', encoding='utf-8') as f:
            json.dump(enrichment_results, f, ensure_ascii=False, indent=2)

        print("富集分析完成！结果已保存到 enrichment_results.json")

        return enrichment_results

    def create_enrichment_plots(self):
        """创建富集分析图表"""
        if self.annotations is None or self.annotations.empty:
            print("请先完成代谢物注释")
            return

        # 1. 化学分类分布图
        plt.figure(figsize=(12, 8))

        classification_counts = self.annotations['Classification'].value_counts()
        classification_counts = classification_counts[classification_counts.index != 'N/A']

        if len(classification_counts) > 0:
            plt.subplot(2, 2, 1)
            classification_counts.head(10).plot(kind='bar')
            plt.title('化学分类分布', fontsize=12, fontweight='bold')
            plt.xlabel('化学分类')
            plt.ylabel('代谢物数量')
            plt.xticks(rotation=45, ha='right')

        # 2. 上调vs下调分布
        plt.subplot(2, 2, 2)
        regulation_counts = self.annotations['Regulation'].value_counts()
        colors = ['red' if x == 'Up' else 'blue' for x in regulation_counts.index]
        regulation_counts.plot(kind='bar', color=colors)
        plt.title('调节方向分布', fontsize=12, fontweight='bold')
        plt.xlabel('调节方向')
        plt.ylabel('代谢物数量')
        plt.xticks(rotation=0)

        # 3. FC值分布直方图
        plt.subplot(2, 2, 3)
        plt.hist(np.log2(self.annotations['FC']), bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('Log2(Fold Change)')
        plt.ylabel('频数')
        plt.title('FC值分布', fontsize=12, fontweight='bold')
        plt.axvline(x=0, color='red', linestyle='--', alpha=0.7)

        # 4. p值分布
        plt.subplot(2, 2, 4)
        plt.hist(-np.log10(self.annotations['pValue']), bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        plt.xlabel('-Log10(P-value)')
        plt.ylabel('频数')
        plt.title('P值分布', fontsize=12, fontweight='bold')
        plt.axvline(x=-np.log10(0.05), color='red', linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.savefig('enrichment_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("富集分析图表已保存为 enrichment_analysis.png")

    def generate_summary_report(self):
        """生成分析总结报告"""
        if self.annotations is None or self.annotations.empty:
            print("请先完成代谢物注释")
            return

        report = []
        report.append("# 代谢组学差异分析报告\n")
        report.append(f"分析日期: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append("=" * 50 + "\n")

        # 基本统计信息
        report.append("## 1. 基本统计信息\n")
        report.append(f"- 总差异代谢物数量: {len(self.annotations)}\n")
        report.append(f"- 上调代谢物: {sum(self.annotations['Regulation'] == 'Up')} 个\n")
        report.append(f"- 下调代谢物: {sum(self.annotations['Regulation'] == 'Down')} 个\n")
        report.append(f"- 成功注释的HMDB条目: {sum(self.annotations['HMDB_ID'] != 'N/A')} 个\n")
        report.append(f"- 成功注释的KEGG条目: {sum(self.annotations['KEGG_ID'] != 'N/A')} 个\n\n")

        # 化学分类统计
        report.append("## 2. 化学分类统计\n")
        classification_counts = self.annotations['Classification'].value_counts()
        classification_counts = classification_counts[classification_counts.index != 'N/A']

        for class_name, count in classification_counts.head(10).items():
            report.append(f"- {class_name}: {count} 个\n")
        report.append("\n")

        # 显著差异代谢物列表
        report.append("## 3. 显著差异代谢物列表\n")
        report.append("### 上调代谢物 (FC > 2, p < 0.05):\n")

        significant_up = self.annotations[
            (self.annotations['Regulation'] == 'Up') &
            (self.annotations['FC'] > 2) &
            (self.annotations['pValue'] < 0.05)
        ]

        for _, row in significant_up.iterrows():
            report.append(f"- {row['CPD_Name']} (FC: {row['FC']:.2f}, p: {row['pValue']:.2e})\n")

        report.append("\n### 下调代谢物 (FC < 0.5, p < 0.05):\n")

        significant_down = self.annotations[
            (self.annotations['Regulation'] == 'Down') &
            (self.annotations['FC'] < 0.5) &
            (self.annotations['pValue'] < 0.05)
        ]

        for _, row in significant_down.iterrows():
            report.append(f"- {row['CPD_Name']} (FC: {row['FC']:.2f}, p: {row['pValue']:.2e})\n")

        # 保存报告
        with open('metabolomics_report.md', 'w', encoding='utf-8') as f:
            f.writelines(report)

        print("分析报告已保存为 metabolomics_report.md")

        return ''.join(report)

def main():
    """主函数"""
    # 初始化分析器
    analyzer = MetabolomicsAnalyzer('差异代谢物.csv')

    # 加载数据
    differential_metabolites = analyzer.load_data()
    print("\n差异代谢物概览:")
    print(differential_metabolites.head())

    # 创建火山图
    analyzer.create_volcano_plot()

    # 进行数据库注释
    annotations = analyzer.annotate_metabolites()

    # 进行富集分析
    enrichment_results = analyzer.perform_enrichment_analysis()

    # 创建富集分析图表
    analyzer.create_enrichment_plots()

    # 生成总结报告
    report = analyzer.generate_summary_report()

    print("\n分析完成！")
    print("生成的文件:")
    print("1. metabolite_annotations.csv - 代谢物注释结果")
    print("2. volcano_plot.png - 差异代谢物火山图")
    print("3. enrichment_results.json - 富集分析结果")
    print("4. enrichment_analysis.png - 富集分析图表")
    print("5. metabolomics_report.md - 分析总结报告")

if __name__ == "__main__":
    main()
