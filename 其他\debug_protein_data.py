#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试蛋白数据问题
"""

import pandas as pd
import numpy as np

def debug_protein_data():
    """调试蛋白数据"""
    print("正在调试蛋白数据...")
    
    # 读取数据
    df = pd.read_csv('merged_protein_clinical_data.csv', low_memory=False)
    print(f"原始数据形状: {df.shape}")
    
    # 去重
    df_unique = df.drop_duplicates(subset=['Record_ID'], keep='first')
    print(f"去重后数据形状: {df_unique.shape}")
    
    # 检查生存变量
    print(f"\n生存变量检查:")
    print(f"OS缺失值: {df_unique['OS'].isnull().sum()}")
    print(f"NonCV_death缺失值: {df_unique['NonCV_death'].isnull().sum()}")
    print(f"OS范围: {df_unique['OS'].min()} - {df_unique['OS'].max()}")
    print(f"NonCV_death分布: {df_unique['NonCV_death'].value_counts()}")
    
    # 检查蛋白数据
    # 找到蛋白列
    clinical_cols = ['Record_ID', 'HFsnEF', 'Plasma_sapmle', 'Sex', 'Age', 'Birthday', 
                    'Nationnality', 'IN_type', 'IN_DATE', 'OUT_DATE', 'IN_DAYS', 'Dyspnea', 
                    'Edema', 'Lung_rales', 'Fatigue', 'SBP ', 'DBP ', 'PR', 'Height', 'Weight', 
                    'BMI', 'BSA', 'HF_type_onset ', 'NYHA_class', 'CHD', 'PCI ', 'CABG', 
                    'Hypertension', 'Atrial_fibrillation', 'Atrial_flutter', 'HF_history', 
                    'HF_duration_month', 'Pacemaker', 'Heart_valve_surgery', 'Stroke', 'Diabetes', 
                    'Hyperlipedemia', 'Anemia', 'CKD', 'COPD', 'Asthma', 'Interstitial_pulmonary_fibrosis', 
                    'Malignancy', 'Thyroid_disease', 'Smoke', ' Alcoholic_drinking', 'Blood_type']
    
    survival_cols = ['Alive', 'CV death', 'OS', 'HF_hospitalization', 'HF hospitalization', 
                    'Time to first HF hospitalization', 'Compostite endpoint of CV death or HF hospitalization',
                    'All_cause_hospitalization', 'NonCV_death', 'Time_to first_all_casue_hospitalization']
    
    exclude_cols = set(clinical_cols + survival_cols)
    protein_cols = [col for col in df_unique.columns if col not in exclude_cols]
    
    print(f"\n蛋白数据检查:")
    print(f"识别到蛋白列数: {len(protein_cols)}")
    print(f"前10个蛋白列: {protein_cols[:10]}")
    
    # 检查前几个蛋白的数据质量
    print(f"\n前10个蛋白的数据质量:")
    for i, protein in enumerate(protein_cols[:10]):
        protein_data = df_unique[protein]
        non_null_count = protein_data.notna().sum()
        non_zero_count = (protein_data > 0).sum()
        std_val = protein_data.std()
        
        print(f"{i+1}. {protein}:")
        print(f"   非空值数量: {non_null_count}/{len(df_unique)} ({non_null_count/len(df_unique)*100:.1f}%)")
        print(f"   非零值数量: {non_zero_count}")
        print(f"   标准差: {std_val:.6f}")
        print(f"   范围: {protein_data.min():.6f} - {protein_data.max():.6f}")
        
        if non_null_count >= 10 and std_val > 0:
            print(f"   ✓ 数据质量良好")
        else:
            print(f"   ✗ 数据质量问题")
        print()
    
    # 检查有多少蛋白有足够的数据进行分析
    good_proteins = []
    for protein in protein_cols:
        protein_data = df_unique[protein]
        non_null_count = protein_data.notna().sum()
        std_val = protein_data.std()
        
        if non_null_count >= 10 and std_val > 0:
            good_proteins.append(protein)
    
    print(f"数据质量良好的蛋白数量: {len(good_proteins)}")
    print(f"数据质量良好的蛋白比例: {len(good_proteins)/len(protein_cols)*100:.1f}%")
    
    if len(good_proteins) > 0:
        print(f"前10个质量良好的蛋白: {good_proteins[:10]}")
        
        # 测试一个蛋白的COX回归
        test_protein = good_proteins[0]
        print(f"\n测试蛋白 {test_protein} 的COX回归:")
        
        # 准备数据
        test_data = df_unique[['OS', 'NonCV_death', test_protein]].copy()
        test_data = test_data.dropna()
        
        print(f"测试数据形状: {test_data.shape}")
        print(f"事件数: {test_data['NonCV_death'].sum()}")
        
        if len(test_data) >= 10 and test_data['NonCV_death'].sum() >= 5:
            try:
                from lifelines import CoxPHFitter
                
                # 标准化
                test_data[test_protein] = (test_data[test_protein] - test_data[test_protein].mean()) / test_data[test_protein].std()
                
                # COX回归
                cph = CoxPHFitter()
                cph.fit(test_data, duration_col='OS', event_col='NonCV_death')
                
                print(f"COX回归成功!")
                print(f"HR: {cph.hazard_ratios_[test_protein]:.4f}")
                print(f"P值: {cph.summary.loc[test_protein, 'p']:.4f}")
                
            except Exception as e:
                print(f"COX回归失败: {str(e)}")
        else:
            print(f"数据不足以进行COX回归")
    
    # 检查是否有真正的蛋白列（Gene Name）
    print(f"\n检查真正的蛋白列:")
    # 查找可能的蛋白列（通常以大写字母开头，包含基因名称模式）
    potential_proteins = []
    for col in df_unique.columns:
        if col not in exclude_cols:
            # 检查是否像基因名称
            if (col.isupper() or 
                any(char.isupper() for char in col) and len(col) > 2 and 
                not col.replace('_', '').replace('-', '').isdigit()):
                potential_proteins.append(col)
    
    print(f"潜在的真正蛋白列数量: {len(potential_proteins)}")
    print(f"前20个潜在蛋白列: {potential_proteins[:20]}")
    
    # 检查这些潜在蛋白的数据质量
    good_real_proteins = []
    for protein in potential_proteins:
        protein_data = df_unique[protein]
        non_null_count = protein_data.notna().sum()
        std_val = protein_data.std()
        
        if non_null_count >= 10 and std_val > 0:
            good_real_proteins.append(protein)
    
    print(f"数据质量良好的真正蛋白数量: {len(good_real_proteins)}")
    if len(good_real_proteins) > 0:
        print(f"前10个质量良好的真正蛋白: {good_real_proteins[:10]}")

if __name__ == "__main__":
    debug_protein_data()
