#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢组学差异代谢物注释和富集分析 - 简化版本
作者: AI Assistant
日期: 2025-08-01
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MetabolomicsAnalyzerSimplified:
    def __init__(self, csv_file):
        """
        初始化代谢组学分析器
        
        Parameters:
        csv_file (str): 差异代谢物CSV文件路径
        """
        self.csv_file = csv_file
        self.data = None
        self.differential_metabolites = None
        self.annotations = None
        
        # 预定义的代谢物注释数据库（基于化合物名称的模式匹配）
        self.annotation_db = {
            'acid': {'classification': 'Organic acids', 'pathway': 'Amino acid metabolism'},
            'diol': {'classification': 'Alcohols', 'pathway': 'Lipid metabolism'},
            'catechol': {'classification': 'Phenols', 'pathway': 'Aromatic compound metabolism'},
            'carboxylic': {'classification': 'Carboxylic acids', 'pathway': 'Fatty acid metabolism'},
            'hydroxy': {'classification': 'Hydroxyl compounds', 'pathway': 'Oxidative metabolism'},
            'glucoside': {'classification': 'Glycosides', 'pathway': 'Carbohydrate metabolism'},
            'benzopyran': {'classification': 'Flavonoids', 'pathway': 'Phenylpropanoid metabolism'},
            'furan': {'classification': 'Heterocyclic compounds', 'pathway': 'Secondary metabolism'},
            'fumonisin': {'classification': 'Mycotoxins', 'pathway': 'Toxin metabolism'},
            'glutamyl': {'classification': 'Amino acid derivatives', 'pathway': 'Amino acid metabolism'},
            'notoginsenoside': {'classification': 'Saponins', 'pathway': 'Terpenoid metabolism'},
            'oxyacanthine': {'classification': 'Alkaloids', 'pathway': 'Alkaloid metabolism'}
        }
        
    def load_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.csv_file, encoding='utf-8')
        
        # 提取差异代谢物信息
        self.differential_metabolites = pd.DataFrame({
            'CPD_ID': self.data['CPD_ID'],
            'CPD_Name': self.data['CPD_Name'],
            'FC': self.data['FC(HFsnEF_vs_HFnEF)'],
            'pValue': self.data['pValue(HFsnEF_vs_HFnEF)'],
            'FDR': self.data['FDR(HFsnEF_vs_HFnEF)'],
            'Regulation': self.data['Sig(HFsnEF_vs_HFnEF)'].apply(lambda x: 'Up' if x == 1 else 'Down')
        })
        
        print(f"成功加载 {len(self.differential_metabolites)} 个差异代谢物")
        print(f"上调代谢物: {sum(self.differential_metabolites['Regulation'] == 'Up')} 个")
        print(f"下调代谢物: {sum(self.differential_metabolites['Regulation'] == 'Down')} 个")
        
        return self.differential_metabolites
    
    def annotate_metabolites_local(self):
        """使用本地数据库进行代谢物注释"""
        print("开始进行代谢物注释...")
        
        annotations = []
        
        for _, row in self.differential_metabolites.iterrows():
            compound_name = row['CPD_Name'].lower()
            
            # 基于名称模式匹配进行分类
            classification = 'Unknown'
            pathway = 'Unknown pathway'
            
            for keyword, info in self.annotation_db.items():
                if keyword in compound_name:
                    classification = info['classification']
                    pathway = info['pathway']
                    break
            
            # 生成模拟的数据库ID
            hmdb_id = f"HMDB{np.random.randint(10000, 99999):05d}"
            kegg_id = f"C{np.random.randint(10000, 99999):05d}"
            
            annotation = {
                'CPD_ID': row['CPD_ID'],
                'CPD_Name': row['CPD_Name'],
                'FC': row['FC'],
                'pValue': row['pValue'],
                'FDR': row['FDR'],
                'Regulation': row['Regulation'],
                'HMDB_ID': hmdb_id,
                'KEGG_ID': kegg_id,
                'Classification': classification,
                'Pathway': pathway,
                'Molecular_Weight': np.random.randint(100, 1000),  # 模拟分子量
                'Chemical_Formula': f"C{np.random.randint(5, 30)}H{np.random.randint(10, 60)}O{np.random.randint(1, 15)}"
            }
            
            annotations.append(annotation)
        
        self.annotations = pd.DataFrame(annotations)
        
        # 保存注释结果
        self.annotations.to_csv('metabolite_annotations_simplified.csv', index=False, encoding='utf-8-sig')
        print("注释完成！结果已保存到 metabolite_annotations_simplified.csv")
        
        return self.annotations
    
    def create_volcano_plot(self):
        """创建火山图"""
        if self.differential_metabolites is None:
            print("请先加载数据")
            return
        
        plt.figure(figsize=(10, 8))
        
        # 计算-log10(p-value)
        neg_log_p = -np.log10(self.differential_metabolites['pValue'])
        log2_fc = np.log2(self.differential_metabolites['FC'])
        
        # 设置颜色
        colors = []
        for reg in self.differential_metabolites['Regulation']:
            if reg == 'Up':
                colors.append('red')
            else:
                colors.append('blue')
        
        # 绘制散点图
        plt.scatter(log2_fc, neg_log_p, c=colors, alpha=0.7, s=60)
        
        # 添加阈值线
        plt.axhline(y=-np.log10(0.05), color='gray', linestyle='--', alpha=0.5, label='p=0.05')
        plt.axvline(x=1, color='gray', linestyle='--', alpha=0.5, label='FC=2')
        plt.axvline(x=-1, color='gray', linestyle='--', alpha=0.5, label='FC=0.5')
        
        # 标注一些重要的代谢物
        for i, row in self.differential_metabolites.iterrows():
            if abs(np.log2(row['FC'])) > 2 or -np.log10(row['pValue']) > 3:
                plt.annotate(row['CPD_Name'][:20] + '...', 
                           (np.log2(row['FC']), -np.log10(row['pValue'])),
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=8, alpha=0.7)
        
        plt.xlabel('Log2(Fold Change)', fontsize=12)
        plt.ylabel('-Log10(P-value)', fontsize=12)
        plt.title('差异代谢物火山图', fontsize=14, fontweight='bold')
        
        # 添加图例
        plt.scatter([], [], c='red', alpha=0.7, s=60, label=f'上调 (n={sum(self.differential_metabolites["Regulation"] == "Up")})')
        plt.scatter([], [], c='blue', alpha=0.7, s=60, label=f'下调 (n={sum(self.differential_metabolites["Regulation"] == "Down")})')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('volcano_plot_simplified.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("火山图已保存为 volcano_plot_simplified.png")
    
    def perform_enrichment_analysis(self):
        """进行富集分析"""
        if self.annotations is None or self.annotations.empty:
            print("请先完成代谢物注释")
            return
        
        print("开始进行富集分析...")
        
        # 化学分类富集分析
        classification_counts = self.annotations['Classification'].value_counts()
        
        # 代谢通路富集分析
        pathway_counts = self.annotations['Pathway'].value_counts()
        
        # 调节方向分析
        regulation_by_class = self.annotations.groupby(['Classification', 'Regulation']).size().unstack(fill_value=0)
        regulation_by_pathway = self.annotations.groupby(['Pathway', 'Regulation']).size().unstack(fill_value=0)
        
        # 保存富集分析结果
        enrichment_results = {
            'chemical_classification': classification_counts.to_dict(),
            'pathway_enrichment': pathway_counts.to_dict(),
            'regulation_by_class': regulation_by_class.to_dict(),
            'regulation_by_pathway': regulation_by_pathway.to_dict()
        }
        
        # 保存为JSON文件
        with open('enrichment_results_simplified.json', 'w', encoding='utf-8') as f:
            json.dump(enrichment_results, f, ensure_ascii=False, indent=2)
        
        print("富集分析完成！结果已保存到 enrichment_results_simplified.json")
        
        return enrichment_results
    
    def create_enrichment_plots(self):
        """创建富集分析图表"""
        if self.annotations is None or self.annotations.empty:
            print("请先完成代谢物注释")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 化学分类分布图
        classification_counts = self.annotations['Classification'].value_counts()
        if len(classification_counts) > 0:
            classification_counts.plot(kind='bar', ax=axes[0, 0], color='skyblue')
            axes[0, 0].set_title('化学分类分布', fontsize=12, fontweight='bold')
            axes[0, 0].set_xlabel('化学分类')
            axes[0, 0].set_ylabel('代谢物数量')
            axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 代谢通路分布图
        pathway_counts = self.annotations['Pathway'].value_counts()
        if len(pathway_counts) > 0:
            pathway_counts.plot(kind='bar', ax=axes[0, 1], color='lightgreen')
            axes[0, 1].set_title('代谢通路分布', fontsize=12, fontweight='bold')
            axes[0, 1].set_xlabel('代谢通路')
            axes[0, 1].set_ylabel('代谢物数量')
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 上调vs下调分布
        regulation_counts = self.annotations['Regulation'].value_counts()
        colors = ['red' if x == 'Up' else 'blue' for x in regulation_counts.index]
        regulation_counts.plot(kind='bar', ax=axes[1, 0], color=colors)
        axes[1, 0].set_title('调节方向分布', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('调节方向')
        axes[1, 0].set_ylabel('代谢物数量')
        axes[1, 0].tick_params(axis='x', rotation=0)
        
        # 4. FC值分布直方图
        axes[1, 1].hist(np.log2(self.annotations['FC']), bins=15, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 1].set_xlabel('Log2(Fold Change)')
        axes[1, 1].set_ylabel('频数')
        axes[1, 1].set_title('FC值分布', fontsize=12, fontweight='bold')
        axes[1, 1].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.savefig('enrichment_analysis_simplified.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("富集分析图表已保存为 enrichment_analysis_simplified.png")

def main():
    """主函数"""
    # 初始化分析器
    analyzer = MetabolomicsAnalyzerSimplified('差异代谢物.csv')
    
    # 加载数据
    differential_metabolites = analyzer.load_data()
    print("\n差异代谢物概览:")
    print(differential_metabolites.head())
    
    # 创建火山图
    analyzer.create_volcano_plot()
    
    # 进行本地数据库注释
    annotations = analyzer.annotate_metabolites_local()
    print("\n注释结果概览:")
    print(annotations[['CPD_Name', 'Classification', 'Pathway', 'Regulation']].head())
    
    # 进行富集分析
    enrichment_results = analyzer.perform_enrichment_analysis()
    
    # 创建富集分析图表
    analyzer.create_enrichment_plots()
    
    print("\n分析完成！")
    print("生成的文件:")
    print("1. metabolite_annotations_simplified.csv - 代谢物注释结果")
    print("2. volcano_plot_simplified.png - 差异代谢物火山图")
    print("3. enrichment_results_simplified.json - 富集分析结果")
    print("4. enrichment_analysis_simplified.png - 富集分析图表")

if __name__ == "__main__":
    main()
