import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.cluster.hierarchy import dendrogram, linkage
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class ProteomicsAnalyzer:
    def __init__(self, file_path, p_threshold=0.05, fc_threshold=1.5):
        """
        初始化蛋白组学分析器
        
        Parameters:
        file_path: str, CSV文件路径
        p_threshold: float, p值阈值
        fc_threshold: float, fold change阈值
        """
        self.file_path = file_path
        self.p_threshold = p_threshold
        self.fc_threshold = fc_threshold
        self.data = None
        self.expression_data = None
        self.metadata = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def load_data(self):
        """加载并预处理数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.file_path)
        
        # 识别样本列（包含HFnEF和HFsnEF的列）
        sample_cols = [col for col in self.data.columns if 'HFnEF' in col or 'HFsnEF' in col]
        
        # 分离表达数据和元数据
        self.expression_data = self.data[sample_cols].fillna(0)
        self.metadata = self.data[['ID', 'Accession', 'Gene Name', 'Description']].copy()
        
        # 添加统计信息
        if 'FC(HFsnEF_vs_HFnEF)' in self.data.columns:
            self.metadata['FC'] = self.data['FC(HFsnEF_vs_HFnEF)']
            self.metadata['pValue'] = self.data['pValue(HFsnEF_vs_HFnEF)']
            self.metadata['FDR'] = self.data.get('FDR(HFsnEF_vs_HFnEF)', np.nan)
        
        print(f"数据加载完成: {len(self.data)} 个蛋白, {len(sample_cols)} 个样本")
        return self
    
    def classify_proteins(self):
        """分类差异蛋白"""
        if 'FC' not in self.metadata.columns:
            print("警告: 未找到FC和pValue列，跳过蛋白分类")
            return self
            
        # 计算绝对fold change和log值
        self.metadata['abs_fc'] = abs(self.metadata['FC'])
        self.metadata['log2_fc'] = np.log2(self.metadata['abs_fc'].replace(0, 1e-10))
        self.metadata['neg_log10_p'] = -np.log10(self.metadata['pValue'].replace(0, 1e-10))
        
        # 分类蛋白
        conditions = [
            (self.metadata['pValue'] < self.p_threshold) & 
            (self.metadata['abs_fc'] > self.fc_threshold) & 
            (self.metadata['FC'] > 0),
            (self.metadata['pValue'] < self.p_threshold) & 
            (self.metadata['abs_fc'] > self.fc_threshold) & 
            (self.metadata['FC'] < 0)
        ]
        choices = ['Up-regulated', 'Down-regulated']
        self.metadata['significance'] = np.select(conditions, choices, default='Not Significant')
        
        return self
    
    def create_comprehensive_figure(self, figsize=(20, 16)):
        """创建综合分析图表（仿照PDF中的多面板图）"""
        fig = plt.figure(figsize=figsize)
        
        # A: 火山图 (Volcano Plot)
        ax1 = plt.subplot(3, 3, 1)
        self._plot_volcano(ax1)
        ax1.set_title('A. Volcano Plot', fontsize=14, fontweight='bold')
        
        # B: PCA分析
        ax2 = plt.subplot(3, 3, 2)
        self._plot_pca(ax2)
        ax2.set_title('B. PCA Analysis', fontsize=14, fontweight='bold')
        
        # C: 热图 (Top differential proteins)
        ax3 = plt.subplot(3, 3, 3)
        self._plot_heatmap_top_proteins(ax3)
        ax3.set_title('C. Top Differential Proteins', fontsize=14, fontweight='bold')
        
        # D: 箱线图 (Expression comparison)
        ax4 = plt.subplot(3, 3, 4)
        self._plot_expression_boxplot(ax4)
        ax4.set_title('D. Expression Distribution', fontsize=14, fontweight='bold')
        
        # E: 蛋白数量统计
        ax5 = plt.subplot(3, 3, 5)
        self._plot_protein_counts(ax5)
        ax5.set_title('E. Differential Protein Counts', fontsize=14, fontweight='bold')
        
        # F: MA Plot
        ax6 = plt.subplot(3, 3, 6)
        self._plot_ma_plot(ax6)
        ax6.set_title('F. MA Plot', fontsize=14, fontweight='bold')
        
        # G: 样本相关性热图
        ax7 = plt.subplot(3, 3, 7)
        self._plot_correlation_heatmap(ax7)
        ax7.set_title('G. Sample Correlation', fontsize=14, fontweight='bold')
        
        # H: Top蛋白表达模式
        ax8 = plt.subplot(3, 3, 8)
        self._plot_top_proteins_expression(ax8)
        ax8.set_title('H. Top Proteins Expression', fontsize=14, fontweight='bold')
        
        # I: 聚类树状图
        ax9 = plt.subplot(3, 3, 9)
        self._plot_dendrogram(ax9)
        ax9.set_title('I. Hierarchical Clustering', fontsize=14, fontweight='bold')
        
        plt.tight_layout(pad=3.0)
        return fig
    
    def _plot_volcano(self, ax):
        """绘制火山图"""
        if 'significance' not in self.metadata.columns:
            ax.text(0.5, 0.5, 'No statistical data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        colors = {'Up-regulated': '#FF6B6B', 'Down-regulated': '#4ECDC4', 'Not Significant': '#95A5A6'}
        
        for sig_type in self.metadata['significance'].unique():
            subset = self.metadata[self.metadata['significance'] == sig_type]
            ax.scatter(subset['log2_fc'], subset['neg_log10_p'], 
                      c=colors.get(sig_type, 'gray'), label=sig_type, alpha=0.6, s=20)
        
        # 添加阈值线
        ax.axvline(x=np.log2(self.fc_threshold), color='gray', linestyle='--', alpha=0.7)
        ax.axvline(x=-np.log2(self.fc_threshold), color='gray', linestyle='--', alpha=0.7)
        ax.axhline(y=-np.log10(self.p_threshold), color='gray', linestyle='--', alpha=0.7)
        
        ax.set_xlabel('Log2(Fold Change)')
        ax.set_ylabel('-Log10(p-value)')
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
    
    def _plot_pca(self, ax):
        """绘制PCA分析"""
        # 数据标准化
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(self.expression_data.T)
        
        # PCA分析
        pca = PCA(n_components=2)
        pca_result = pca.fit_transform(scaled_data)
        
        # 分组信息
        sample_names = self.expression_data.columns
        groups = ['HFnEF' if 'HFnEF' in name and 'HFsnEF' not in name else 'HFsnEF' for name in sample_names]
        
        colors = {'HFnEF': '#3498DB', 'HFsnEF': '#E74C3C'}
        
        for group in set(groups):
            mask = [g == group for g in groups]
            ax.scatter(pca_result[mask, 0], pca_result[mask, 1], 
                      c=colors[group], label=group, alpha=0.7, s=50)
        
        ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%})')
        ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%})')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_heatmap_top_proteins(self, ax):
        """绘制top差异蛋白热图"""
        if 'significance' not in self.metadata.columns:
            ax.text(0.5, 0.5, 'No statistical data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        # 选择top差异蛋白
        sig_proteins = self.metadata[self.metadata['significance'] != 'Not Significant']
        if len(sig_proteins) == 0:
            ax.text(0.5, 0.5, 'No significant proteins found', 
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        top_proteins = sig_proteins.nlargest(20, 'abs_fc')['ID'].values
        heatmap_data = self.expression_data.loc[self.data['ID'].isin(top_proteins)]
        
        # 标准化
        heatmap_data_scaled = (heatmap_data - heatmap_data.mean(axis=1).values.reshape(-1, 1)) / \
                             heatmap_data.std(axis=1).values.reshape(-1, 1)
        
        im = ax.imshow(heatmap_data_scaled.values, cmap='RdBu_r', aspect='auto')
        ax.set_xticks(range(len(heatmap_data.columns)))
        ax.set_xticklabels(heatmap_data.columns, rotation=45, fontsize=8)
        ax.set_ylabel('Proteins')
        
        # 添加colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.6)
        cbar.set_label('Z-score', fontsize=8)
    
    def _plot_expression_boxplot(self, ax):
        """绘制表达量分布箱线图"""
        # 准备数据
        hfnef_cols = [col for col in self.expression_data.columns if 'HFnEF' in col and 'HFsnEF' not in col]
        hfsnef_cols = [col for col in self.expression_data.columns if 'HFsnEF' in col]
        
        hfnef_data = self.expression_data[hfnef_cols].values.flatten()
        hfsnef_data = self.expression_data[hfsnef_cols].values.flatten()
        
        # 移除零值
        hfnef_data = hfnef_data[hfnef_data > 0]
        hfsnef_data = hfsnef_data[hfsnef_data > 0]
        
        # Log转换
        hfnef_log = np.log2(hfnef_data + 1)
        hfsnef_log = np.log2(hfsnef_data + 1)
        
        ax.boxplot([hfnef_log, hfsnef_log], labels=['HFnEF', 'HFsnEF'])
        ax.set_ylabel('Log2(Expression + 1)')
        ax.grid(True, alpha=0.3)
    
    def _plot_protein_counts(self, ax):
        """绘制差异蛋白数量统计"""
        if 'significance' not in self.metadata.columns:
            ax.text(0.5, 0.5, 'No statistical data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        counts = self.metadata['significance'].value_counts()
        colors = {'Up-regulated': '#FF6B6B', 'Down-regulated': '#4ECDC4', 'Not Significant': '#95A5A6'}
        
        bars = ax.bar(range(len(counts)), counts.values, 
                     color=[colors.get(cat, 'gray') for cat in counts.index])
        ax.set_xticks(range(len(counts)))
        ax.set_xticklabels(counts.index, rotation=45)
        ax.set_ylabel('Number of Proteins')
        
        # 添加数值标签
        for bar, count in zip(bars, counts.values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,
                   str(count), ha='center', va='bottom')
    
    def _plot_ma_plot(self, ax):
        """绘制MA图"""
        if 'FC' not in self.metadata.columns:
            ax.text(0.5, 0.5, 'No statistical data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        # 计算平均表达量
        hfnef_cols = [col for col in self.expression_data.columns if 'HFnEF' in col and 'HFsnEF' not in col]
        hfsnef_cols = [col for col in self.expression_data.columns if 'HFsnEF' in col]
        
        mean_hfnef = self.expression_data[hfnef_cols].mean(axis=1)
        mean_hfsnef = self.expression_data[hfsnef_cols].mean(axis=1)
        
        A = (np.log2(mean_hfnef + 1) + np.log2(mean_hfsnef + 1)) / 2
        M = np.log2(mean_hfsnef + 1) - np.log2(mean_hfnef + 1)
        
        # 根据显著性着色
        if 'significance' in self.metadata.columns:
            colors = {'Up-regulated': '#FF6B6B', 'Down-regulated': '#4ECDC4', 'Not Significant': '#95A5A6'}
            for sig_type in self.metadata['significance'].unique():
                mask = self.metadata['significance'] == sig_type
                ax.scatter(A[mask], M[mask], c=colors.get(sig_type, 'gray'), 
                          alpha=0.6, s=20, label=sig_type)
            ax.legend(fontsize=8)
        else:
            ax.scatter(A, M, alpha=0.6, s=20, c='gray')
        
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.set_xlabel('A (Average log2 expression)')
        ax.set_ylabel('M (Log2 fold change)')
        ax.grid(True, alpha=0.3)
    
    def _plot_correlation_heatmap(self, ax):
        """绘制样本相关性热图"""
        # 计算相关性矩阵
        corr_matrix = self.expression_data.corr()
        
        # 绘制热图
        im = ax.imshow(corr_matrix.values, cmap='RdYlBu_r', vmin=-1, vmax=1)
        
        # 设置标签
        ax.set_xticks(range(len(corr_matrix.columns)))
        ax.set_yticks(range(len(corr_matrix.columns)))
        ax.set_xticklabels(corr_matrix.columns, rotation=45, fontsize=8)
        ax.set_yticklabels(corr_matrix.columns, fontsize=8)
        
        # 添加colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.6)
        cbar.set_label('Correlation', fontsize=8)
    
    def _plot_top_proteins_expression(self, ax):
        """绘制top蛋白的表达模式"""
        if 'significance' not in self.metadata.columns:
            ax.text(0.5, 0.5, 'No statistical data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        # 选择top5上调和下调蛋白
        up_proteins = self.metadata[self.metadata['significance'] == 'Up-regulated'].nlargest(5, 'abs_fc')
        down_proteins = self.metadata[self.metadata['significance'] == 'Down-regulated'].nlargest(5, 'abs_fc')
        
        if len(up_proteins) == 0 and len(down_proteins) == 0:
            ax.text(0.5, 0.5, 'No significant proteins found', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 分组数据
        hfnef_cols = [col for col in self.expression_data.columns if 'HFnEF' in col and 'HFsnEF' not in col]
        hfsnef_cols = [col for col in self.expression_data.columns if 'HFsnEF' in col]
        
        y_pos = 0
        colors = {'Up-regulated': '#FF6B6B', 'Down-regulated': '#4ECDC4'}
        
        for proteins, sig_type in [(up_proteins, 'Up-regulated'), (down_proteins, 'Down-regulated')]:
            if len(proteins) == 0:
                continue
                
            for _, protein in proteins.iterrows():
                protein_data = self.expression_data.loc[self.data['ID'] == protein['ID']]
                if len(protein_data) > 0:
                    hfnef_values = protein_data[hfnef_cols].values.flatten()
                    hfsnef_values = protein_data[hfsnef_cols].values.flatten()
                    
                    ax.scatter([0]*len(hfnef_values), [y_pos]*len(hfnef_values), 
                              c='lightblue', alpha=0.6, s=30)
                    ax.scatter([1]*len(hfsnef_values), [y_pos]*len(hfsnef_values), 
                              c=colors[sig_type], alpha=0.6, s=30)
                    
                    # 连线显示变化
                    ax.plot([0, 1], [np.mean(hfnef_values), np.mean(hfsnef_values)], 
                           color=colors[sig_type], alpha=0.7, linewidth=2)
                    
                    y_pos += 1
        
        ax.set_xlim(-0.5, 1.5)
        ax.set_xticks([0, 1])
        ax.set_xticklabels(['HFnEF', 'HFsnEF'])
        ax.set_ylabel('Proteins')
        ax.grid(True, alpha=0.3)
    
    def _plot_dendrogram(self, ax):
        """绘制层次聚类树状图"""
        # 使用相关距离进行聚类
        corr_matrix = self.expression_data.corr()
        distance_matrix = 1 - corr_matrix
        
        # 层次聚类
        linkage_matrix = linkage(distance_matrix, method='ward')
        
        # 绘制树状图
        dendrogram(linkage_matrix, labels=self.expression_data.columns, 
                  orientation='top', ax=ax)
        ax.set_xticklabels(ax.get_xticklabels(), rotation=45, fontsize=8)
        ax.set_ylabel('Distance')
    
    def print_summary(self):
        """打印分析摘要"""
        print("\n" + "="*50)
        print("蛋白组学分析摘要")
        print("="*50)
        
        print(f"总蛋白数: {len(self.data)}")
        print(f"样本数: {len(self.expression_data.columns)}")
        
        if 'significance' in self.metadata.columns:
            summary = self.metadata['significance'].value_counts()
            print(f"\n差异蛋白统计 (p < {self.p_threshold}, |FC| > {self.fc_threshold}):")
            for sig_type, count in summary.items():
                print(f"  {sig_type}: {count}")
        
        print("\n分析参数:")
        print(f"  p-value阈值: {self.p_threshold}")
        print(f"  Fold Change阈值: {self.fc_threshold}")
        
        print("\n图表说明:")
        print("  A. 火山图 - 显示fold change vs p-value")
        print("  B. PCA分析 - 样本主成分分析")
        print("  C. 热图 - Top差异蛋白表达模式")
        print("  D. 箱线图 - 两组表达量分布")
        print("  E. 柱状图 - 差异蛋白数量统计")
        print("  F. MA图 - 平均表达量vs fold change")
        print("  G. 相关性热图 - 样本间相关性")
        print("  H. 表达模式 - Top蛋白在两组中的变化")
        print("  I. 聚类树 - 样本层次聚类")


def main():
    """主函数示例"""
    # 使用示例
    analyzer = ProteomicsAnalyzer('Proteins_all_diff.csv', p_threshold=0.05, fc_threshold=1.5)
    
    # 执行分析
    analyzer.load_data().classify_proteins()
    
    # 生成综合图表
    fig = analyzer.create_comprehensive_figure(figsize=(20, 16))
    plt.show()
    
    # 打印摘要
    analyzer.print_summary()
    
    # 保存图表
    fig.savefig('proteomics_analysis_comprehensive.png', dpi=300, bbox_inches='tight')
    fig.savefig('proteomics_analysis_comprehensive.pdf', bbox_inches='tight')
    
    print(f"\n图表已保存为: proteomics_analysis_comprehensive.png/pdf")


if __name__ == "__main__":
    # 如果要直接运行，请修改文件路径
    main()
    
    # 或者这样使用:
    """
    analyzer = ProteomicsAnalyzer('示例.csv')
    analyzer.load_data().classify_proteins()
    fig = analyzer.create_comprehensive_figure()
    plt.show()
    analyzer.print_summary()
    """
    print("蛋白组学分析工具已准备就绪!")
    print("使用方法:")
    print("1. analyzer = ProteomicsAnalyzer('your_file.csv')")
    print("2. analyzer.load_data().classify_proteins()")
    print("3. fig = analyzer.create_comprehensive_figure()")
    print("4. plt.show()")
    print("5. analyzer.print_summary()")
