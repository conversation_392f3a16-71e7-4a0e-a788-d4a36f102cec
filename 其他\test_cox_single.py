#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个蛋白的COX回归
"""

import pandas as pd
import numpy as np
from lifelines import CoxPHFitter

def test_single_cox():
    """测试单个蛋白的COX回归"""
    # 读取数据
    df = pd.read_csv('merged_protein_clinical_data.csv', low_memory=False)
    df_unique = df.drop_duplicates(subset=['Record_ID'], keep='first')
    
    # 准备测试数据
    test_protein = 'WBC'
    test_data = df_unique[['OS', 'NonCV_death', test_protein]].copy()
    test_data = test_data.dropna()
    
    print(f"测试数据形状: {test_data.shape}")
    print(f"事件数: {test_data['NonCV_death'].sum()}")
    
    # 标准化
    test_data[test_protein] = (test_data[test_protein] - test_data[test_protein].mean()) / test_data[test_protein].std()
    
    # COX回归
    cph = CoxPHFitter()
    cph.fit(test_data, duration_col='OS', event_col='NonCV_death')
    
    print(f"\nCOX回归结果:")
    print(cph.summary)
    
    print(f"\n置信区间:")
    print(cph.confidence_intervals_)
    
    print(f"\n置信区间列名:")
    print(list(cph.confidence_intervals_.columns))
    
    print(f"\nHR:")
    print(cph.hazard_ratios_)

if __name__ == "__main__":
    test_single_cox()
