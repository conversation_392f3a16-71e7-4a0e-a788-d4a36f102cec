#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢组学与非心血管死亡关联分析
根据LVEF值分组分析HFpEF患者的代谢物与非心血管死亡的关系
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
from lifelines.statistics import logrank_test
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_preprocess_data(file_path):
    """
    加载和预处理数据
    """
    print("正在加载数据...")
    df = pd.read_csv(file_path, sep='\t', low_memory=False)
    
    print(f"数据形状: {df.shape}")
    print(f"样本数量: {len(df)}")
    
    # 检查关键变量
    key_vars = ['HFsnEF', 'LVEF', 'NonCV_death', 'OS']
    missing_vars = [var for var in key_vars if var not in df.columns]
    if missing_vars:
        print(f"警告：缺少关键变量: {missing_vars}")
    
    # 显示HFsnEF分组情况
    if 'HFsnEF' in df.columns:
        print(f"\nHFsnEF分组情况:")
        print(df['HFsnEF'].value_counts())
        print(f"HFnEF (HFsnEF=0): {sum(df['HFsnEF']==0)} 例")
        print(f"HFsnEF (HFsnEF=1): {sum(df['HFsnEF']==1)} 例")
    
    # 显示LVEF分布
    if 'LVEF' in df.columns:
        print(f"\nLVEF分布:")
        print(f"LVEF均值: {df['LVEF'].mean():.2f} ± {df['LVEF'].std():.2f}")
        print(f"LVEF范围: {df['LVEF'].min():.1f} - {df['LVEF'].max():.1f}")
        print(f"LVEF>65的患者: {sum(df['LVEF']>65)} 例")
    
    # 显示非心血管死亡情况
    if 'NonCV_death' in df.columns:
        print(f"\n非心血管死亡情况:")
        print(df['NonCV_death'].value_counts())
    
    # 显示生存时间
    if 'OS' in df.columns:
        print(f"\n总生存时间 (OS):")
        print(f"OS均值: {df['OS'].mean():.2f} ± {df['OS'].std():.2f}")
        print(f"OS范围: {df['OS'].min():.1f} - {df['OS'].max():.1f}")
    
    return df

def identify_metabolites(df):
    """
    识别代谢物列
    """
    # 代谢物列通常以特定模式结尾（_POS或_NEG）
    metabolite_cols = [col for col in df.columns if col.endswith('_POS') or col.endswith('_NEG')]
    print(f"\n识别到 {len(metabolite_cols)} 个代谢物")
    
    # 检查代谢物数据质量
    metabolite_data = df[metabolite_cols]
    
    # 计算缺失值比例
    missing_ratio = metabolite_data.isnull().sum() / len(df)
    print(f"代谢物缺失值比例范围: {missing_ratio.min():.3f} - {missing_ratio.max():.3f}")
    
    # 过滤掉缺失值过多的代谢物（>50%）
    valid_metabolites = missing_ratio[missing_ratio <= 0.5].index.tolist()
    print(f"有效代谢物数量（缺失值≤50%）: {len(valid_metabolites)}")
    
    return valid_metabolites

def analyze_metabolites_vs_noncv_death(df, metabolites, group_name="整体人群"):
    """
    分析代谢物与非心血管死亡的关联
    """
    print(f"\n=== {group_name}中代谢物与非心血管死亡的关联分析 ===")

    results = []

    for metabolite in metabolites:
        # 获取有效数据
        valid_idx = ~(df[metabolite].isnull() | df['NonCV_death'].isnull())
        if valid_idx.sum() < 10:  # 至少需要10个有效样本
            continue

        metabolite_values = df.loc[valid_idx, metabolite]
        noncv_death = df.loc[valid_idx, 'NonCV_death']

        # 进行t检验或Mann-Whitney U检验
        death_group = metabolite_values[noncv_death == 1]
        alive_group = metabolite_values[noncv_death == 0]

        if len(death_group) > 0 and len(alive_group) > 0:
            # 检验正态性
            _, p_normal_death = stats.shapiro(death_group) if len(death_group) > 3 else (None, 0)
            _, p_normal_alive = stats.shapiro(alive_group) if len(alive_group) > 3 else (None, 0)

            # 选择合适的检验方法
            if p_normal_death > 0.05 and p_normal_alive > 0.05 and len(death_group) > 5 and len(alive_group) > 5:
                # 使用t检验
                stat, p_value = stats.ttest_ind(death_group, alive_group)
                test_method = "t-test"
            else:
                # 使用Mann-Whitney U检验
                stat, p_value = stats.mannwhitneyu(death_group, alive_group, alternative='two-sided')
                test_method = "Mann-Whitney U"

            # 计算效应量（Cohen's d或r）
            if test_method == "t-test":
                pooled_std = np.sqrt(((len(death_group)-1)*death_group.var() + (len(alive_group)-1)*alive_group.var()) /
                                   (len(death_group) + len(alive_group) - 2))
                effect_size = (death_group.mean() - alive_group.mean()) / pooled_std
                effect_name = "Cohen's d"
            else:
                effect_size = stat / (len(death_group) * len(alive_group))
                effect_name = "r"

            results.append({
                'metabolite': metabolite,
                'n_death': len(death_group),
                'n_alive': len(alive_group),
                'mean_death': death_group.mean(),
                'mean_alive': alive_group.mean(),
                'median_death': death_group.median(),
                'median_alive': alive_group.median(),
                'p_value': p_value,
                'test_method': test_method,
                'effect_size': effect_size,
                'effect_name': effect_name
            })

    # 转换为DataFrame并排序
    results_df = pd.DataFrame(results)
    if len(results_df) > 0:
        results_df = results_df.sort_values('p_value')

        # 多重比较校正（Benjamini-Hochberg）
        from statsmodels.stats.multitest import multipletests
        _, results_df['p_adjusted'], _, _ = multipletests(results_df['p_value'], method='fdr_bh')

        # 显示显著结果（FDR < 0.05）
        significant_results = results_df[results_df['p_adjusted'] < 0.05]
        print(f"显著相关的代谢物数量 (FDR < 0.05): {len(significant_results)}")

        # 显示趋势性显著结果（p < 0.05，未校正）
        trend_results = results_df[results_df['p_value'] < 0.05]
        print(f"趋势性显著的代谢物数量 (p < 0.05, 未校正): {len(trend_results)}")

        # 显示前10个最显著的结果
        print(f"\n前10个最显著的代谢物:")
        display_cols = ['metabolite', 'n_death', 'n_alive', 'mean_death', 'mean_alive',
                      'p_value', 'p_adjusted', 'effect_size']
        print(results_df[display_cols].head(10).to_string(index=False))

        if len(significant_results) > 0:
            print("\n严格显著相关的代谢物 (FDR < 0.05):")
            print(significant_results[display_cols].to_string(index=False))

        return results_df, significant_results, trend_results
    else:
        print("未找到有效的分析结果")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

def analyze_metabolite_lvef_relationship(df, significant_metabolites):
    """
    分析显著代谢物与LVEF的关系，检查U型曲线
    """
    print(f"\n=== 分析显著代谢物与LVEF的关系 ===")
    
    if len(significant_metabolites) == 0:
        print("没有显著的代谢物需要分析")
        return
    
    # 选择前5个最显著的代谢物进行详细分析
    top_metabolites = significant_metabolites.head(5)['metabolite'].tolist()
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, metabolite in enumerate(top_metabolites):
        if i >= 6:
            break
            
        # 获取有效数据
        valid_idx = ~(df[metabolite].isnull() | df['LVEF'].isnull())
        metabolite_values = df.loc[valid_idx, metabolite]
        lvef_values = df.loc[valid_idx, 'LVEF']
        
        # 散点图
        axes[i].scatter(lvef_values, metabolite_values, alpha=0.6)
        axes[i].set_xlabel('LVEF (%)')
        axes[i].set_ylabel('代谢物浓度')
        axes[i].set_title(f'{metabolite[:30]}...' if len(metabolite) > 30 else metabolite)
        
        # 拟合线性和二次关系
        try:
            # 线性拟合
            linear_coef = np.polyfit(lvef_values, metabolite_values, 1)
            linear_poly = np.poly1d(linear_coef)
            
            # 二次拟合
            quad_coef = np.polyfit(lvef_values, metabolite_values, 2)
            quad_poly = np.poly1d(quad_coef)
            
            x_smooth = np.linspace(lvef_values.min(), lvef_values.max(), 100)
            axes[i].plot(x_smooth, linear_poly(x_smooth), 'r--', label='线性拟合')
            axes[i].plot(x_smooth, quad_poly(x_smooth), 'g-', label='二次拟合')
            axes[i].legend()
            
            # 计算相关系数
            linear_r, linear_p = stats.pearsonr(lvef_values, metabolite_values)
            print(f"{metabolite}: 线性相关 r={linear_r:.3f}, p={linear_p:.3f}")
            
        except:
            pass
    
    plt.tight_layout()
    plt.savefig('metabolite_lvef_relationships.png', dpi=300, bbox_inches='tight')
    plt.show()

def perform_survival_analysis(df, significant_metabolites, group_name="整体人群"):
    """
    进行生存分析
    """
    print(f"\n=== {group_name}的生存分析 ===")
    
    if len(significant_metabolites) == 0:
        print("没有显著的代谢物进行生存分析")
        return
    
    # 选择前3个最显著的代谢物
    top_metabolites = significant_metabolites.head(3)['metabolite'].tolist()
    
    for metabolite in top_metabolites:
        print(f"\n--- {metabolite} 的生存分析 ---")
        
        # 准备生存分析数据
        survival_data = df[['OS', 'NonCV_death', metabolite]].copy()
        survival_data = survival_data.dropna()
        
        if len(survival_data) < 10:
            print("有效样本数量不足，跳过此代谢物")
            continue
        
        # 将代谢物按中位数分组
        median_value = survival_data[metabolite].median()
        survival_data['metabolite_group'] = (survival_data[metabolite] > median_value).astype(int)
        
        # Cox回归分析
        try:
            cph = CoxPHFitter()
            cph.fit(survival_data[['OS', 'NonCV_death', metabolite]], 
                   duration_col='OS', event_col='NonCV_death')
            
            print(f"Cox回归结果:")
            print(cph.summary[['coef', 'exp(coef)', 'p']])
            
            # Log-rank检验
            high_group = survival_data[survival_data['metabolite_group'] == 1]
            low_group = survival_data[survival_data['metabolite_group'] == 0]
            
            if len(high_group) > 0 and len(low_group) > 0:
                results = logrank_test(high_group['OS'], low_group['OS'], 
                                     high_group['NonCV_death'], low_group['NonCV_death'])
                print(f"Log-rank检验 p值: {results.p_value:.4f}")
        
        except Exception as e:
            print(f"生存分析出错: {e}")

def main():
    """
    主分析函数
    """
    print("=== 代谢组学与非心血管死亡关联分析 ===")
    
    # 1. 加载数据
    df = load_and_preprocess_data('merged_metabolomics_clinical_data.tsv')
    
    # 2. 识别代谢物
    metabolites = identify_metabolites(df)
    
    # 3. 整体人群分析
    print("\n" + "="*60)
    overall_results, overall_significant, overall_trend = analyze_metabolites_vs_noncv_death(
        df, metabolites, "整体人群")

    # 4. 分析显著代谢物与LVEF的关系
    # 如果没有严格显著的，使用趋势性显著的
    metabolites_for_lvef = overall_significant if len(overall_significant) > 0 else overall_trend
    if len(metabolites_for_lvef) > 0:
        analyze_metabolite_lvef_relationship(df, metabolites_for_lvef)

    # 5. 分组分析
    print("\n" + "="*60)

    # HFnEF组分析 (HFsnEF = 0)
    hfnef_data = df[df['HFsnEF'] == 0]
    if len(hfnef_data) > 10:
        hfnef_results, hfnef_significant, hfnef_trend = analyze_metabolites_vs_noncv_death(
            hfnef_data, metabolites, "HFnEF组")
    else:
        print("HFnEF组样本数量不足")
        hfnef_significant = pd.DataFrame()
        hfnef_trend = pd.DataFrame()

    # HFsnEF组分析 (HFsnEF = 1)
    hfsnef_data = df[df['HFsnEF'] == 1]
    if len(hfsnef_data) > 10:
        hfsnef_results, hfsnef_significant, hfsnef_trend = analyze_metabolites_vs_noncv_death(
            hfsnef_data, metabolites, "HFsnEF组")
    else:
        print("HFsnEF组样本数量不足")
        hfsnef_significant = pd.DataFrame()
        hfsnef_trend = pd.DataFrame()
    
    # 6. 生存分析
    print("\n" + "="*60)
    if len(overall_significant) > 0:
        perform_survival_analysis(df, overall_significant, "整体人群")
    
    if len(hfnef_significant) > 0:
        perform_survival_analysis(hfnef_data, hfnef_significant, "HFnEF组")
    
    if len(hfsnef_significant) > 0:
        perform_survival_analysis(hfsnef_data, hfsnef_significant, "HFsnEF组")
    
    # 7. 保存结果
    print("\n=== 保存分析结果 ===")
    if len(overall_significant) > 0:
        overall_significant.to_csv('overall_significant_metabolites.csv', index=False)
        print("整体人群显著代谢物结果已保存到: overall_significant_metabolites.csv")
    
    if len(hfnef_significant) > 0:
        hfnef_significant.to_csv('hfnef_significant_metabolites.csv', index=False)
        print("HFnEF组显著代谢物结果已保存到: hfnef_significant_metabolites.csv")
    
    if len(hfsnef_significant) > 0:
        hfsnef_significant.to_csv('hfsnef_significant_metabolites.csv', index=False)
        print("HFsnEF组显著代谢物结果已保存到: hfsnef_significant_metabolites.csv")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
