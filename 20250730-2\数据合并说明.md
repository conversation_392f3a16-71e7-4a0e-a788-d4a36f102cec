# 代谢组数据与临床数据合并说明

## 概述
本项目成功将代谢组数据(`Metabolism_POS_mix.csv`)与临床数据(`203HFsnEF_800d.xlsx`)按照`Record_ID`进行合并，生成了统一的数据集。

## 数据文件说明

### 1. 代谢组数据 (Metabolism_POS_mix.csv)
- **文件格式**: CSV文件
- **编码**: GBK
- **数据结构**:
  - 第1行: 列标题，第1列为"Record_ID"，第2列为空，从第3列开始为样本ID
  - 第1列: 代谢物编号 (CPD_0001, CPD_0002, ...)
  - 第2列: 代谢物名称
  - 第3列及以后: 每列代表一个样本的代谢物定量数据
- **原始维度**: 275行 × 210列
- **样本数**: 208个
- **代谢物数**: 274个

### 2. 临床数据 (203HFsnEF_800d.xlsx)
- **文件格式**: Excel文件
- **数据结构**:
  - 每行代表一个样本
  - 每列代表一个临床变量
  - 第1列为`Record_ID`
- **维度**: 203行 × 181列
- **样本数**: 203个
- **临床变量数**: 180个

## 数据处理过程

### 1. 代谢组数据预处理
1. **编码处理**: 自动检测并使用GBK编码读取CSV文件
2. **数据转置**: 
   - 提取样本ID（从列名第3列开始）
   - 提取代谢物名称（第2列数据）
   - 提取代谢物定量数据（从第2行第3列开始的数值矩阵）
   - 转置数据矩阵，使样本成为行，代谢物成为列
3. **数据重构**: 创建新的DataFrame，以样本ID为行索引，代谢物名称为列名

### 2. 临床数据预处理
1. **文件读取**: 使用pandas读取Excel文件
2. **数据类型统一**: 将`Record_ID`转换为字符串类型

### 3. 数据合并
1. **合并方式**: 使用内连接(inner join)按`Record_ID`合并
2. **合并结果**: 只保留两个数据集中都存在的样本
3. **数据验证**: 确保合并后数据的完整性和一致性

## 最终结果

### 合并后数据集特征
- **文件名**: `merged_metabolism_clinical_data.csv`
- **编码**: UTF-8 with BOM
- **维度**: 203行 × 455列
- **样本数**: 203个
- **变量构成**:
  - 1个样本标识符 (`Record_ID`)
  - 180个临床变量
  - 274个代谢物变量

### 数据结构
```
列顺序: Record_ID | 临床变量(180个) | 代谢物变量(274个)
行数: 203个样本
```

### 样本匹配情况
- 代谢组数据原有样本: 208个
- 临床数据原有样本: 203个
- 成功匹配样本: 203个
- 匹配率: 100% (基于临床数据)

## 使用的脚本

### 1. 完整版脚本 (`merge_datasets.py`)
- 包含详细的数据检查和错误处理
- 提供详细的处理过程输出
- 适合调试和数据探索

### 2. 简化版脚本 (`merge_data_simple.py`)
- 精简的合并流程
- 适合日常使用
- 包含基本的错误处理

## 运行方法

### 环境要求
```bash
pip install pandas openpyxl
```

### 执行命令
```bash
# 使用简化版脚本
python merge_data_simple.py

# 或使用完整版脚本
python merge_datasets.py
```

## 注意事项

1. **文件位置**: 确保数据文件与脚本在同一目录下
2. **编码问题**: 代谢组数据使用GBK编码，脚本会自动处理
3. **内存使用**: 合并后的数据集较大，确保有足够内存
4. **数据完整性**: 合并过程中会自动处理数据类型不一致的问题

## 数据质量检查

合并后的数据已通过以下检查:
- ✅ 无重复的Record_ID
- ✅ 无缺失的Record_ID
- ✅ 数据类型一致性
- ✅ 列名完整性
- ✅ 数据维度正确性

## 后续使用建议

1. **数据探索**: 可以使用pandas进行基本的统计分析
2. **缺失值处理**: 检查并处理可能存在的缺失值
3. **数据标准化**: 根据分析需求对代谢物数据进行标准化
4. **特征选择**: 根据研究目标选择相关的临床变量和代谢物

合并后的数据集为后续的代谢组学分析和临床关联研究提供了完整的数据基础。
