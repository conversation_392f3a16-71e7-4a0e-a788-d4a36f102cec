#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cox分析结果可视化脚本

作者: AI Assistant
日期: 2025-07-30
描述: 为Cox分析结果生成可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import glob

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def find_latest_results_file():
    """
    查找最新的Cox分析结果文件
    """
    pattern = "cox_analysis_results_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到Cox分析结果文件")
    
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def create_visualizations(results_file):
    """
    创建可视化图表
    """
    print(f"正在读取结果文件：{results_file}")
    
    # 读取结果
    results = pd.read_csv(results_file)
    
    # 过滤成功的分析结果
    successful_results = results[results['Status'] == '成功'].copy()
    
    # 创建图表
    fig = plt.figure(figsize=(20, 16))
    
    # 1. p值分布直方图
    plt.subplot(3, 3, 1)
    p_values = successful_results['P_value'].dropna()
    plt.hist(p_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(x=0.05, color='red', linestyle='--', label='p=0.05')
    plt.axvline(x=0.01, color='orange', linestyle='--', label='p=0.01')
    plt.axvline(x=0.001, color='purple', linestyle='--', label='p=0.001')
    plt.xlabel('p值')
    plt.ylabel('频数')
    plt.title('p值分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. HR值分布（log scale）
    plt.subplot(3, 3, 2)
    hr_values = successful_results['HR'].dropna()
    hr_values = hr_values[(hr_values > 0) & (hr_values < 100)]  # 过滤极端值
    plt.hist(np.log10(hr_values), bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    plt.axvline(x=0, color='red', linestyle='--', label='HR=1')
    plt.xlabel('log10(HR)')
    plt.ylabel('频数')
    plt.title('风险比(HR)分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. 显著变量按类型分布
    plt.subplot(3, 3, 3)
    significant_results = successful_results[successful_results['P_value'] < 0.05]
    type_counts = significant_results['Variable_Type'].value_counts()
    colors = ['lightcoral', 'lightblue']
    plt.pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%', 
            colors=colors, startangle=90)
    plt.title('显著变量类型分布')
    
    # 4. 火山图 (Volcano Plot)
    plt.subplot(3, 3, 4)
    x = np.log10(successful_results['HR'].replace([np.inf, -np.inf], np.nan).dropna())
    y = -np.log10(successful_results['P_value'].replace([np.inf, -np.inf], np.nan).dropna())
    
    # 确保x和y长度一致
    min_len = min(len(x), len(y))
    x = x.iloc[:min_len]
    y = y.iloc[:min_len]
    
    plt.scatter(x, y, alpha=0.6, s=20)
    plt.axhline(y=-np.log10(0.05), color='red', linestyle='--', label='p=0.05')
    plt.axvline(x=0, color='gray', linestyle='-', alpha=0.5)
    plt.xlabel('log10(HR)')
    plt.ylabel('-log10(p值)')
    plt.title('火山图')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 5. 前20个最显著变量的HR值
    plt.subplot(3, 3, 5)
    top_significant = significant_results.head(20)
    y_pos = np.arange(len(top_significant))
    
    # 截断变量名以便显示
    var_names = [name[:30] + '...' if len(name) > 30 else name 
                 for name in top_significant['Variable']]
    
    colors = ['red' if hr > 1 else 'blue' for hr in top_significant['HR']]
    plt.barh(y_pos, top_significant['HR'], color=colors, alpha=0.7)
    plt.yticks(y_pos, var_names)
    plt.xlabel('风险比(HR)')
    plt.title('前20个最显著变量的HR值')
    plt.axvline(x=1, color='black', linestyle='--', alpha=0.5)
    plt.grid(True, alpha=0.3)
    
    # 6. 保护性因子 vs 危险因子
    plt.subplot(3, 3, 6)
    protective = len(significant_results[significant_results['HR'] < 1])
    risk = len(significant_results[significant_results['HR'] > 1])
    
    labels = ['保护性因子\n(HR<1)', '危险因子\n(HR>1)']
    sizes = [protective, risk]
    colors = ['lightblue', 'lightcoral']
    
    plt.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    plt.title('保护性因子 vs 危险因子')
    
    # 7. 按p值区间的变量数量
    plt.subplot(3, 3, 7)
    p_ranges = ['p≥0.05', '0.01≤p<0.05', '0.001≤p<0.01', 'p<0.001']
    counts = [
        len(successful_results[successful_results['P_value'] >= 0.05]),
        len(successful_results[(successful_results['P_value'] >= 0.01) & 
                              (successful_results['P_value'] < 0.05)]),
        len(successful_results[(successful_results['P_value'] >= 0.001) & 
                              (successful_results['P_value'] < 0.01)]),
        len(successful_results[successful_results['P_value'] < 0.001])
    ]
    
    colors = ['lightgray', 'yellow', 'orange', 'red']
    plt.bar(p_ranges, counts, color=colors, alpha=0.7, edgecolor='black')
    plt.xlabel('p值区间')
    plt.ylabel('变量数量')
    plt.title('不同显著性水平的变量分布')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 8. 代谢物 vs 临床变量的显著性比较
    plt.subplot(3, 3, 8)
    metabolite_data = successful_results[successful_results['Variable_Type'] == '代谢物']
    clinical_data = successful_results[successful_results['Variable_Type'] == '临床']
    
    metabolite_sig = len(metabolite_data[metabolite_data['P_value'] < 0.05])
    metabolite_total = len(metabolite_data)
    clinical_sig = len(clinical_data[clinical_data['P_value'] < 0.05])
    clinical_total = len(clinical_data)
    
    categories = ['代谢物', '临床变量']
    significant = [metabolite_sig, clinical_sig]
    non_significant = [metabolite_total - metabolite_sig, clinical_total - clinical_sig]
    
    x = np.arange(len(categories))
    width = 0.35
    
    plt.bar(x, significant, width, label='显著(p<0.05)', color='lightcoral', alpha=0.7)
    plt.bar(x, non_significant, width, bottom=significant, label='不显著(p≥0.05)', 
            color='lightblue', alpha=0.7)
    
    plt.xlabel('变量类型')
    plt.ylabel('变量数量')
    plt.title('代谢物 vs 临床变量显著性比较')
    plt.xticks(x, categories)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 9. 分析状态统计
    plt.subplot(3, 3, 9)
    status_counts = results['Status'].value_counts()
    colors = plt.cm.Set3(np.linspace(0, 1, len(status_counts)))
    
    plt.pie(status_counts.values, labels=status_counts.index, autopct='%1.1f%%', 
            colors=colors, startangle=90)
    plt.title('分析状态分布')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"cox_analysis_visualization_{timestamp}.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    print(f"可视化图表已保存到：{output_file}")
    
    # 显示图表
    plt.show()
    
    return output_file

def create_summary_table():
    """
    创建显著变量摘要表
    """
    results_file = find_latest_results_file()
    results = pd.read_csv(results_file)
    
    # 筛选显著变量
    significant_results = results[
        (results['P_value'] < 0.05) & 
        (results['Status'] == '成功')
    ].copy()
    
    # 创建简化的摘要表
    summary_table = significant_results[[
        'Variable', 'Variable_Type', 'N', 'Events', 
        'HR', 'HR_95CI', 'P_value_formatted', 'Significance'
    ]].copy()
    
    # 重命名列
    summary_table.columns = [
        '变量名称', '变量类型', '样本数', '事件数', 
        '风险比', '95%置信区间', 'p值', '显著性'
    ]
    
    # 保存摘要表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"significant_variables_summary_{timestamp}.csv"
    summary_table.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"显著变量摘要表已保存到：{output_file}")
    
    return output_file

def main():
    """
    主函数
    """
    try:
        print("=" * 60)
        print("Cox分析结果可视化")
        print("=" * 60)
        
        # 查找最新的结果文件
        results_file = find_latest_results_file()
        
        # 创建可视化图表
        viz_file = create_visualizations(results_file)
        
        # 创建显著变量摘要表
        summary_file = create_summary_table()
        
        print("\n可视化完成！")
        print(f"图表文件：{viz_file}")
        print(f"摘要表文件：{summary_file}")
        
    except Exception as e:
        print(f"可视化过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
