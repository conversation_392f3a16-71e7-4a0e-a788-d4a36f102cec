import pandas as pd
import numpy as np
import networkx as nx
from community import community_louvain
from scipy.stats import pearsonr
from pathlib import Path

"""
使用方法示例：
python cluster_hfsnef_specific_proteins.py \
    --expression_matrix merged_protein_clinical_data.csv \
    --specific_protein_list hfsnef_specific_significant_proteins.csv \
    --sample_prefix HFsnEF_ 

脚本功能：
1. 读取蛋白表达矩阵（行=样本，列=蛋白）
2. 可选地根据 sample_prefix 过滤 HFsnEF 样本
3. 读取 HFsnEF 特异显著蛋白列表，只保留这些蛋白进行分析
4. 计算蛋白两两 Pearson 相关系数，构建加权共表达网络（绝对相关系数作为权重）
5. 基于 Louvain 社区发现划分模块（近似 WGCNA 模块）
6. 计算模块内连接度，识别 Hub 蛋白
7. 输出：
   - protein_module_assignment.csv：每个蛋白对应模块及连接度
   - module_summary.csv：模块大小、Hub 蛋白
   - network.gexf：网络文件，可在 Cytoscape/Gephi 中可视化
"""

import argparse

def calculate_correlation_matrix(df: pd.DataFrame) -> pd.DataFrame:
    """计算相关矩阵，返回 DataFrame"""
    proteins = df.columns
    corr_matrix = pd.DataFrame(np.zeros((len(proteins), len(proteins))), index=proteins, columns=proteins)
    for i, p1 in enumerate(proteins):
        for j in range(i + 1, len(proteins)):
            p2 = proteins[j]
            r, _ = pearsonr(df[p1], df[p2])
            corr_matrix.loc[p1, p2] = corr_matrix.loc[p2, p1] = r
    return corr_matrix

def build_network(corr_matrix: pd.DataFrame, threshold: float = 0.3) -> nx.Graph:
    """根据相关矩阵构建网络（阈值过滤弱相关）"""
    G = nx.Graph()
    proteins = corr_matrix.columns
    G.add_nodes_from(proteins)
    for i, p1 in enumerate(proteins):
        for j in range(i + 1, len(proteins)):
            p2 = proteins[j]
            weight = abs(corr_matrix.loc[p1, p2])
            if weight >= threshold:
                G.add_edge(p1, p2, weight=weight)
    return G

def main():
    parser = argparse.ArgumentParser(description='HFsnEF 特异显著蛋白共表达网络分析')
    parser.add_argument('--expression_matrix', required=True, help='样本×蛋白表达矩阵 CSV 文件')
    parser.add_argument('--specific_protein_list', required=True, help='identify_hfsnef_specific_proteins.py 生成的 CSV')
    parser.add_argument('--sample_prefix', default='HFsnEF_', help='仅保留以该前缀开头的样本（可为空）')
    parser.add_argument('--corr_threshold', type=float, default=0.3, help='相关阈值，低于阈值的边将被丢弃')
    args = parser.parse_args()

    # 读取表达矩阵
    expr = pd.read_csv(args.expression_matrix)
    # 如果 Record_ID 在第一列，设置索引
    if expr.columns[0] == 'Record_ID':
        expr.set_index('Record_ID', inplace=True)

    # 根据前缀过滤 HFsnEF 样本
    if args.sample_prefix:
        expr = expr[[idx.startswith(args.sample_prefix) for idx in expr.index]]

    # 转置为 行=样本，列=蛋白 已满足条件

    # 读取特异蛋白列表
    spec_df = pd.read_csv(args.specific_protein_list)
    spec_proteins = spec_df['protein'].unique().tolist()

    # 过滤表达矩阵列
    missing = set(spec_proteins) - set(expr.columns)
    if missing:
        print(f'警告：{len(missing)} 个特异蛋白在表达矩阵中不存在，将被忽略')
        spec_proteins = [p for p in spec_proteins if p in expr.columns]

    expr_spec = expr[spec_proteins].astype(float)

    # 计算相关矩阵
    corr_matrix = calculate_correlation_matrix(expr_spec)

    # 构建网络
    G = build_network(corr_matrix, threshold=args.corr_threshold)

    # Louvain 社区发现
    partition = community_louvain.best_partition(G, weight='weight')

    # 计算连接度并整理输出
    degrees = dict(G.degree(weight='weight'))
    module_assignment = []
    for protein, module in partition.items():
        module_assignment.append({'protein': protein,
                                  'module': module,
                                  'degree': degrees.get(protein, 0)})
    module_df = pd.DataFrame(module_assignment)
    module_df.to_csv('protein_module_assignment.csv', index=False)

    # 汇总模块信息
    summary = module_df.groupby('module').apply(
        lambda df: pd.Series({'size': len(df),
                              'hub_protein': df.sort_values('degree', ascending=False).iloc[0]['protein'],
                              'max_degree': df['degree'].max()})).reset_index()
    summary.to_csv('module_summary.csv', index=False)

    # 导出网络
    nx.write_gexf(G, 'protein_network.gexf')

    print('分析完成：')
    print(summary)

if __name__ == '__main__':
    main() 