#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山图和森林图绘制
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import warnings
from matplotlib_venn import venn2, venn2_circles
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_volcano_plot(data, title, filename, group_name="Overall"):
    """创建火山图"""
    
    print(f"正在创建{title}...")
    
    # 准备数据
    plot_data = data.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    plot_data['neg_log10_fdr'] = -np.log10(plot_data['fdr_value'])
    
    # 定义阈值
    p_threshold = 0.05
    fdr_threshold = 0.05
    
    # 分类点 - 简化为三类
    plot_data['significance'] = 'Non-significant'

    # P值显著
    p_significant = plot_data['p_value'] < p_threshold
    # FDR显著
    fdr_significant = plot_data['fdr_value'] < fdr_threshold

    # 简化分类：只分为保护性、危险性、非显著
    sig_protective = p_significant & (plot_data['hazard_ratio'] < 1)
    sig_risk = p_significant & (plot_data['hazard_ratio'] > 1)

    plot_data.loc[sig_protective, 'significance'] = 'Significant_Protective'
    plot_data.loc[sig_risk, 'significance'] = 'Significant_Risk'
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    ax.set_facecolor('white')
    
    # 定义颜色和大小 - 简化
    colors = {
        'Non-significant': '#CCCCCC',        # 灰色
        'Significant_Protective': '#1E88E5', # 蓝色
        'Significant_Risk': '#D32F2F'        # 红色
    }

    sizes = {
        'Non-significant': 35,  # 增大非显著点
        'Significant_Protective': 55,  # 增大保护性点
        'Significant_Risk': 55  # 增大危险性点
    }
    
    # 绘制散点图
    for category in ['Non-significant', 'Significant_Protective', 'Significant_Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.6 if category == 'Non-significant' else 0.8
            zorder = 1 if category == 'Non-significant' else 3

            ax.scatter(plot_data.loc[mask, 'log2_hr'],
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category],
                      alpha=alpha,
                      s=sizes[category],
                      zorder=zorder,
                      edgecolors='white' if category != 'Non-significant' else 'none',
                      linewidth=0.5)
    
    # 添加阈值线 - 修正FDR线位置
    ax.axhline(y=-np.log10(p_threshold), color='black', linestyle='--',
               alpha=0.8, linewidth=1.5, zorder=2, label='P = 0.05')

    # FDR线应该在更高的位置（更严格的阈值对应更高的-log10值）
    fdr_y_position = max(-np.log10(fdr_threshold), -np.log10(p_threshold) + 0.5)
    ax.axhline(y=fdr_y_position, color='red', linestyle='--',
               alpha=0.8, linewidth=1.5, zorder=2, label='FDR = 0.05')
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.6, linewidth=1, zorder=2)
    
    # 设置坐标轴范围 - 缩窄两侧空白
    ax.set_xlim(-4.2, 4.2)
    
    y_max = plot_data['neg_log10_p'].max() * 1.05
    ax.set_ylim(0, max(y_max, 3))
    
    # 设置刻度
    ax.set_xticks([-4, -2, 0, 2, 4])
    ax.set_xticklabels(['-4', '-2', '0', '2', '4'], fontsize=11)
    ax.tick_params(axis='y', labelsize=11)
    
    # 设置标签和标题
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=12, fontweight='bold')
    ax.set_ylabel('-log₁₀(P value)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=15)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 添加标注
    ax.text(-3.8, -np.log10(p_threshold) + 0.1, 'P = 0.05',
            fontsize=10, color='black', fontweight='bold')
    ax.text(-3.8, fdr_y_position + 0.1, 'FDR = 0.05',
            fontsize=10, color='red', fontweight='bold')
    
    # 添加保护性/危险性标注
    ax.text(-3.8, 0.2, 'Protective', fontsize=11, color='#1E88E5', 
            fontweight='bold', ha='left')
    ax.text(3.5, 0.2, 'Risk', fontsize=11, color='#D32F2F', 
            fontweight='bold', ha='right')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)
    
    # 设置刻度参数
    ax.tick_params(axis='both', which='major', labelsize=11, 
                   length=6, width=1.5, direction='out')
    
    # 添加统计信息
    stats_text = f"Total: {len(plot_data)}\n"
    stats_text += f"P < 0.05: {p_significant.sum()}\n"
    stats_text += f"FDR < 0.05: {fdr_significant.sum()}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', fontsize=9,
            bbox=dict(boxstyle='round,pad=0.4', facecolor='white', 
                     alpha=0.9, edgecolor='gray'))
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def create_forest_plot(data, title, filename, top_n=10):
    """创建森林图"""
    
    print(f"正在创建{title}...")
    
    # 选择前N个最显著的结果
    plot_data = data.head(top_n).copy()
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, max(8, len(plot_data) * 0.8)))
    
    # 设置y轴位置
    y_positions = range(len(plot_data))
    
    # 绘制森林图
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i  # 倒序排列，最显著的在顶部
        
        # 绘制置信区间线 - 黑色
        ax.plot([row['ci_lower'], row['ci_upper']], [y, y], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制置信区间端点 - 黑色
        ax.plot([row['ci_lower'], row['ci_lower']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        ax.plot([row['ci_upper'], row['ci_upper']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制HR点 - 蓝色方块
        ax.scatter(row['hazard_ratio'], y, s=150, color='#1E88E5', alpha=0.8, 
                  zorder=3, marker='s', edgecolors='black', linewidth=1)
    
    # 添加HR=1的参考线 - 虚线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.8, linewidth=2, zorder=1)
    
    # 设置y轴标签 - 自动检测列名（代谢物或蛋白质名称）
    # 检测数据中是使用 'metabolite' 还是 'protein' 列
    if 'metabolite' in plot_data.columns:
        name_column = 'metabolite'
    elif 'protein' in plot_data.columns:
        name_column = 'protein'
    else:
        raise ValueError("数据中既没有 'metabolite' 列也没有 'protein' 列")

    item_names = []
    for _, row in plot_data.iterrows():
        name = row[name_column].split('_')[0]
        # 截断过长的名称
        if len(name) > 45:
            name = name[:42] + '...'
        item_names.append(name)

    ax.set_yticks(range(len(plot_data)))
    ax.set_yticklabels(reversed(item_names), fontsize=10)
    
    # 设置x轴 - 使用对数刻度
    ax.set_xscale('log')

    # 设置x轴范围 - 处理极端值和无穷大
    import numpy as np

    # 收集所有有限的数值
    all_values = []
    for col in ['hazard_ratio', 'ci_lower', 'ci_upper']:
        values = plot_data[col].values
        # 只保留有限的正数值
        finite_values = values[np.isfinite(values) & (values > 0)]
        all_values.extend(finite_values)

    if len(all_values) == 0:
        # 如果没有有效值，使用默认范围
        x_min, x_max = 0.1, 10
    else:
        # 计算合理的范围
        x_min = max(0.01, min(all_values) * 0.5)
        x_max = min(max(all_values) * 2, 1000)  # 限制最大值避免过大

        # 确保最小范围
        if x_max / x_min < 2:
            x_max = x_min * 10

    ax.set_xlim(x_min, x_max)

    # 设置x轴刻度为小数和整数，完全避免指数显示
    import matplotlib.ticker as ticker

    def format_hr_axis(x, p):
        """自定义格式化函数，确保不显示指数"""
        if x >= 1000:
            return f'{x:.0f}'
        elif x >= 100:
            return f'{x:.0f}'
        elif x >= 10:
            return f'{x:.0f}'
        elif x >= 1:
            return f'{x:.1f}' if x != int(x) else f'{int(x)}'
        elif x >= 0.1:
            return f'{x:.1f}'
        elif x >= 0.01:
            return f'{x:.2f}'
        else:
            return f'{x:.3f}'

    # 设置合理的刻度位置
    if x_max > 100:
        ticks = [0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50, 100, 200, 500]
    elif x_max > 10:
        ticks = [0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50]
    else:
        ticks = [0.1, 0.2, 0.5, 1, 2, 5]

    # 只保留在范围内的刻度
    ticks = [t for t in ticks if x_min <= t <= x_max]
    ax.set_xticks(ticks)

    # 手动设置刻度标签，确保不显示指数
    tick_labels = []
    for tick in ticks:
        if tick >= 1000:
            tick_labels.append(f'{tick:.0f}')
        elif tick >= 100:
            tick_labels.append(f'{tick:.0f}')
        elif tick >= 10:
            tick_labels.append(f'{tick:.0f}')
        elif tick >= 1:
            if tick == int(tick):
                tick_labels.append(f'{int(tick)}')
            else:
                tick_labels.append(f'{tick:.1f}')
        elif tick >= 0.1:
            tick_labels.append(f'{tick:.1f}')
        elif tick >= 0.01:
            tick_labels.append(f'{tick:.2f}')
        else:
            tick_labels.append(f'{tick:.3f}')

    ax.set_xticklabels(tick_labels)
    
    # 设置x轴标签
    ax.set_xlabel('Hazard Ratio (95% CI)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    # 在右侧添加HR值、置信区间和P值
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i
        
        # HR和置信区间文本
        hr_text = f"{row['hazard_ratio']:.3f}"
        ci_text = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
        p_text = f"P={row['p_value']:.4f}"
        fdr_text = f"FDR={row['fdr_value']:.4f}"
        
        # 在图的右侧添加文本
        ax.text(x_max * 0.95, y + 0.1, f"{hr_text} {ci_text}", 
                ha='right', va='center', fontsize=9, fontweight='bold')
        ax.text(x_max * 0.95, y - 0.1, f"{p_text}, {fdr_text}", 
                ha='right', va='center', fontsize=8, style='italic', color='gray')
    
    # 添加保护性/危险性标注
    ax.text(x_min * 1.1, len(plot_data) + 0.5, 'Protective', 
            fontsize=11, color='#1E88E5', fontweight='bold')
    ax.text(x_max * 0.9, len(plot_data) + 0.5, 'Risk', 
            fontsize=11, color='#D32F2F', fontweight='bold')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_linewidth(1.5)
    ax.spines['left'].set_linewidth(1.5)
    
    # 调整布局
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def create_venn_diagram(data1, data2, group1_name, group2_name, title, filename, p_threshold=0.05):
    """
    创建两组差异蛋白的韦恩图

    参数:
    data1: 第一组数据 (DataFrame)
    data2: 第二组数据 (DataFrame)
    group1_name: 第一组名称
    group2_name: 第二组名称
    title: 图表标题
    filename: 保存文件名
    p_threshold: p值阈值，默认0.05
    """

    print(f"正在创建{title}...")

    # 检测数据中使用的列名
    if 'metabolite' in data1.columns:
        name_column = 'metabolite'
    elif 'protein' in data1.columns:
        name_column = 'protein'
    else:
        raise ValueError("数据中既没有 'metabolite' 列也没有 'protein' 列")

    # 筛选显著差异的蛋白质/代谢物
    significant_1 = set(data1[data1['p_value'] < p_threshold][name_column].tolist())
    significant_2 = set(data2[data2['p_value'] < p_threshold][name_column].tolist())

    print(f"{group1_name}组显著差异项目数: {len(significant_1)}")
    print(f"{group2_name}组显著差异项目数: {len(significant_2)}")
    print(f"共同显著差异项目数: {len(significant_1 & significant_2)}")

    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 8))

    # 绘制韦恩图
    venn = venn2(subsets=(significant_1, significant_2),
                 set_labels=(group1_name, group2_name),
                 ax=ax)

    # 设置颜色
    if venn.get_patch_by_id('10'):  # 仅在group1中
        venn.get_patch_by_id('10').set_facecolor('#FF6B6B')
        venn.get_patch_by_id('10').set_alpha(0.7)

    if venn.get_patch_by_id('01'):  # 仅在group2中
        venn.get_patch_by_id('01').set_facecolor('#4ECDC4')
        venn.get_patch_by_id('01').set_alpha(0.7)

    if venn.get_patch_by_id('11'):  # 两组共同
        venn.get_patch_by_id('11').set_facecolor('#45B7D1')
        venn.get_patch_by_id('11').set_alpha(0.7)

    # 绘制圆圈边框
    venn_circles = venn2_circles(subsets=(significant_1, significant_2), ax=ax)
    if venn_circles[0]:
        venn_circles[0].set_edgecolor('black')
        venn_circles[0].set_linewidth(2)
    if venn_circles[1]:
        venn_circles[1].set_edgecolor('black')
        venn_circles[1].set_linewidth(2)

    # 设置标题和标签
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

    # 添加统计信息文本
    stats_text = f"P值阈值: {p_threshold}\n"
    stats_text += f"仅{group1_name}: {len(significant_1 - significant_2)}\n"
    stats_text += f"仅{group2_name}: {len(significant_2 - significant_1)}\n"
    stats_text += f"共同: {len(significant_1 & significant_2)}\n"
    stats_text += f"总计: {len(significant_1 | significant_2)}"

    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
            verticalalignment='top', fontsize=10,
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 调整布局并保存
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    print(f"{title}已保存到: {filename}")

    # 返回交集和并集信息，便于后续分析
    intersection = significant_1 & significant_2
    union = significant_1 | significant_2
    only_group1 = significant_1 - significant_2
    only_group2 = significant_2 - significant_1

    return {
        'intersection': intersection,
        'union': union,
        'only_group1': only_group1,
        'only_group2': only_group2,
        'group1_total': len(significant_1),
        'group2_total': len(significant_2)
    }

def main():
    """主函数"""
    
    print("=" * 80)
    print("火山图和森林图绘制")
    print("=" * 80)
    
    # 1. 整体人群分析
    print("\n1. 整体人群分析")
    print("-" * 50)
    
    # overall_data = pd.read_csv('overall_cox_comprehensive_results.csv')
    overall_data = pd.read_csv('overall_protein_cox_results.csv')

    # 整体人群火山图
    create_volcano_plot(overall_data, 
                       'Overall Population - Cox Regression Volcano Plot',
                       'overall_volcano_plot_comprehensive.png', 
                       'Overall')
    
    # 整体人群森林图 - 前10个最显著的
    overall_significant = overall_data.sort_values('p_value')
    create_forest_plot(overall_significant, 
                      'Top 20 Most Significant Proteins - Overall Population',
                      'overall_forest_plot_top20.png', 20)
    
    # 2. HFnEF组分析
    print("\n2. HFnEF组分析")
    print("-" * 50)
    
    try:
        hfnef_data = pd.read_csv('hfnef_protein_cox_results.csv')
        
        # HFnEF组火山图
        create_volcano_plot(hfnef_data, 
                           'HFnEF Group - Cox Regression Volcano Plot',
                           'hfnef_volcano_plot_comprehensive.png', 
                           'HFnEF')
        
        print(f"HFnEF组分析完成")
        
    except FileNotFoundError:
        print("HFnEF组数据文件未找到")
    
    # 3. HFsnEF组分析
    print("\n3. HFsnEF组分析")
    print("-" * 50)
    
    try:
        hfsnef_data = pd.read_csv('hfsnef_protein_cox_results.csv')
        
        # HFsnEF组火山图
        create_volcano_plot(hfsnef_data, 
                           'HFsnEF Group - Cox Regression Volcano Plot',
                           'hfsnef_volcano_plot_comprehensive.png', 
                           'HFsnEF')
        
        # HFsnEF组森林图 - 前3个最显著的代谢物
        hfsnef_significant = hfsnef_data.sort_values('p_value')

        create_forest_plot(hfsnef_significant,
                          'Top 10 Most Significant Proteins - HFsnEF Group',
                          'hfsnef_forest_plot_top10.png', 10)
        
        print(f"HFsnEF组分析完成")

    except FileNotFoundError:
        print("HFsnEF组数据文件未找到")

    # # 4. 韦恩图分析 - 比较两组差异蛋白
    # print("\n4. 韦恩图分析")
    # print("-" * 50)

    # try:
    #     # 加载两组数据进行比较
    #     hfnef_data = pd.read_csv('hfnef_protein_cox_results.csv')
    #     hfsnef_data = pd.read_csv('hfsnef_protein_cox_results.csv')
    #
    #     # 创建HFnEF vs HFsnEF韦恩图
    #     venn_results = create_venn_diagram(
    #         hfnef_data, hfsnef_data,
    #         'HFnEF', 'HFsnEF',
    #         'Significant Proteins Comparison: HFnEF vs HFsnEF',
    #         'hfnef_vs_hfsnef_venn_diagram.png',
    #         p_threshold=0.05
    #     )
    #
    #     print(f"韦恩图分析完成")
    #     print(f"共同显著蛋白质数量: {len(venn_results['intersection'])}")
    #     print(f"仅HFnEF显著: {len(venn_results['only_group1'])}")
    #     print(f"仅HFsnEF显著: {len(venn_results['only_group2'])}")
    #
    #     # 如果有共同的显著蛋白质，打印前几个
    #     if len(venn_results['intersection']) > 0:
    #         print(f"\n前5个共同显著蛋白质:")
    #         common_proteins = list(venn_results['intersection'])[:5]
    #         for i, protein in enumerate(common_proteins, 1):
    #             print(f"  {i}. {protein}")
    #
    # except FileNotFoundError as e:
    #     print(f"韦恩图分析跳过 - 数据文件未找到: {e}")
    # except Exception as e:
    #     print(f"韦恩图分析出错: {e}")

    print(f"\n" + "="*80)
    print("所有图表生成完成！")
    print("生成的文件:")
    print("• overall_volcano_plot_comprehensive.png - 整体人群火山图")
    print("• overall_forest_plot_top20.png - 整体人群前20个蛋白质森林图")
    print("• hfnef_volcano_plot_comprehensive.png - HFnEF组火山图")
    print("• hfsnef_volcano_plot_comprehensive.png - HFsnEF组火山图")
    print("• hfsnef_forest_plot_top10.png - HFsnEF组前10个最显著蛋白质森林图")
    # print("• hfnef_vs_hfsnef_venn_diagram.png - HFnEF vs HFsnEF 韦恩图")
    print("="*80)

if __name__ == "__main__":
    main()
