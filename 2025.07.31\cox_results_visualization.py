#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cox回归结果可视化脚本
创建火山图和其他相关图表来展示蛋白质Cox回归分析结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data(file_path):
    """加载Cox回归结果数据"""
    try:
        df = pd.read_csv(file_path)
        print(f"成功加载数据，共 {len(df)} 个蛋白质")
        print(f"数据列: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def create_volcano_plot(df, output_path='volcano_plot.png'):
    """创建火山图"""
    # 计算log2(HR)和-log10(p_value)
    df['log2_hr'] = np.log2(df['hazard_ratio'])
    df['neg_log10_p'] = -np.log10(df['p_value'])
    
    # 设置显著性阈值
    p_threshold = 0.05
    hr_threshold = 1.5  # HR > 1.5 或 HR < 1/1.5
    
    # 分类蛋白质
    df['significance'] = 'Not Significant'
    df.loc[(df['p_value'] < p_threshold) & (df['hazard_ratio'] > hr_threshold), 'significance'] = 'High Risk'
    df.loc[(df['p_value'] < p_threshold) & (df['hazard_ratio'] < 1/hr_threshold), 'significance'] = 'Low Risk'
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 绘制散点图
    colors = {'Not Significant': 'lightgray', 'High Risk': 'red', 'Low Risk': 'blue'}
    for sig_type in colors.keys():
        subset = df[df['significance'] == sig_type]
        plt.scatter(subset['log2_hr'], subset['neg_log10_p'], 
                   c=colors[sig_type], alpha=0.6, s=30, label=sig_type)
    
    # 添加阈值线
    plt.axhline(y=-np.log10(p_threshold), color='black', linestyle='--', alpha=0.5, label=f'p = {p_threshold}')
    plt.axvline(x=np.log2(hr_threshold), color='black', linestyle='--', alpha=0.5)
    plt.axvline(x=-np.log2(hr_threshold), color='black', linestyle='--', alpha=0.5)
    
    # 标注显著的蛋白质
    significant_proteins = df[(df['p_value'] < p_threshold) & 
                             ((df['hazard_ratio'] > hr_threshold) | (df['hazard_ratio'] < 1/hr_threshold))]
    
    # 只标注前10个最显著的蛋白质
    top_proteins = significant_proteins.nsmallest(10, 'p_value')
    
    for idx, row in top_proteins.iterrows():
        plt.annotate(row['protein'], 
                    (row['log2_hr'], row['neg_log10_p']),
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, alpha=0.8)
    
    plt.xlabel('log₂(Hazard Ratio)', fontsize=12)
    plt.ylabel('-log₁₀(P-value)', fontsize=12)
    plt.title('蛋白质Cox回归分析火山图', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    n_high_risk = len(df[df['significance'] == 'High Risk'])
    n_low_risk = len(df[df['significance'] == 'Low Risk'])
    plt.text(0.02, 0.98, f'高风险蛋白质: {n_high_risk}\n低风险蛋白质: {n_low_risk}', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"火山图已保存至: {output_path}")
    plt.show()

def create_hr_distribution_plot(df, output_path='hr_distribution.png'):
    """创建风险比分布图"""
    plt.figure(figsize=(12, 6))
    
    # 子图1: 风险比直方图
    plt.subplot(1, 2, 1)
    plt.hist(df['hazard_ratio'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(x=1, color='red', linestyle='--', label='HR = 1')
    plt.xlabel('Hazard Ratio')
    plt.ylabel('频数')
    plt.title('风险比分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: log2(HR)分布
    plt.subplot(1, 2, 2)
    log2_hr = np.log2(df['hazard_ratio'])
    plt.hist(log2_hr, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    plt.axvline(x=0, color='red', linestyle='--', label='log₂(HR) = 0')
    plt.xlabel('log₂(Hazard Ratio)')
    plt.ylabel('频数')
    plt.title('log₂(风险比)分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"风险比分布图已保存至: {output_path}")
    plt.show()

def create_top_proteins_plot(df, n_top=20, output_path='top_proteins.png'):
    """创建显著蛋白质条形图"""
    # 选择最显著的蛋白质
    top_proteins = df.nsmallest(n_top, 'p_value')
    
    plt.figure(figsize=(12, 8))
    
    # 创建颜色映射
    colors = ['red' if hr > 1 else 'blue' for hr in top_proteins['hazard_ratio']]
    
    # 创建条形图
    bars = plt.barh(range(len(top_proteins)), top_proteins['hazard_ratio'], color=colors, alpha=0.7)
    
    # 设置y轴标签
    plt.yticks(range(len(top_proteins)), top_proteins['protein'])
    plt.xlabel('Hazard Ratio')
    plt.title(f'前{n_top}个最显著蛋白质的风险比', fontsize=14, fontweight='bold')
    
    # 添加HR=1的参考线
    plt.axvline(x=1, color='black', linestyle='--', alpha=0.5, label='HR = 1')
    
    # 添加数值标签
    for i, (bar, hr, p_val) in enumerate(zip(bars, top_proteins['hazard_ratio'], top_proteins['p_value'])):
        plt.text(hr + 0.1 if hr > 1 else hr - 0.1, i, 
                f'HR={hr:.2f}\np={p_val:.2e}', 
                va='center', ha='left' if hr > 1 else 'right', fontsize=8)
    
    plt.legend()
    plt.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"显著蛋白质条形图已保存至: {output_path}")
    plt.show()

def create_confidence_interval_plot(df, n_top=15, output_path='confidence_intervals.png'):
    """创建置信区间图"""
    # 选择最显著的蛋白质
    top_proteins = df.nsmallest(n_top, 'p_value').copy()
    top_proteins = top_proteins.sort_values('hazard_ratio')
    
    plt.figure(figsize=(10, 8))
    
    y_pos = range(len(top_proteins))
    
    # 绘制置信区间
    for i, (idx, row) in enumerate(top_proteins.iterrows()):
        color = 'red' if row['hazard_ratio'] > 1 else 'blue'
        plt.errorbar(row['hazard_ratio'], i, 
                    xerr=[[row['hazard_ratio'] - row['ci_lower']], 
                          [row['ci_upper'] - row['hazard_ratio']]], 
                    fmt='o', color=color, capsize=5, capthick=2, alpha=0.7)
    
    # 设置y轴标签
    plt.yticks(y_pos, top_proteins['protein'])
    plt.xlabel('Hazard Ratio (95% CI)')
    plt.title(f'前{n_top}个最显著蛋白质的风险比及95%置信区间', fontsize=12, fontweight='bold')
    
    # 添加HR=1的参考线
    plt.axvline(x=1, color='black', linestyle='--', alpha=0.5, label='HR = 1')
    
    plt.legend()
    plt.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"置信区间图已保存至: {output_path}")
    plt.show()

def generate_summary_report(df):
    """生成分析摘要报告"""
    print("\n" + "="*60)
    print("Cox回归分析结果摘要报告")
    print("="*60)
    
    print(f"总蛋白质数量: {len(df)}")
    print(f"样本数量: {df['n_samples'].iloc[0]}")
    print(f"事件数量: {df['n_events'].iloc[0]}")
    
    # 显著性分析
    p_005 = len(df[df['p_value'] < 0.05])
    p_001 = len(df[df['p_value'] < 0.01])
    p_0001 = len(df[df['p_value'] < 0.001])
    
    print(f"\n显著性统计:")
    print(f"p < 0.05: {p_005} ({p_005/len(df)*100:.1f}%)")
    print(f"p < 0.01: {p_001} ({p_001/len(df)*100:.1f}%)")
    print(f"p < 0.001: {p_0001} ({p_0001/len(df)*100:.1f}%)")
    
    # FDR分析
    fdr_005 = len(df[df['fdr_value'] < 0.05])
    fdr_001 = len(df[df['fdr_value'] < 0.01])
    
    print(f"\nFDR校正后统计:")
    print(f"FDR < 0.05: {fdr_005} ({fdr_005/len(df)*100:.1f}%)")
    print(f"FDR < 0.01: {fdr_001} ({fdr_001/len(df)*100:.1f}%)")
    
    # 风险比分析
    hr_high = len(df[df['hazard_ratio'] > 1.5])
    hr_low = len(df[df['hazard_ratio'] < 1/1.5])
    
    print(f"\n风险比统计:")
    print(f"HR > 1.5 (高风险): {hr_high} ({hr_high/len(df)*100:.1f}%)")
    print(f"HR < 0.67 (低风险): {hr_low} ({hr_low/len(df)*100:.1f}%)")
    
    # 最显著的蛋白质
    print(f"\n前10个最显著的蛋白质:")
    top_10 = df.nsmallest(10, 'p_value')
    for i, (idx, row) in enumerate(top_10.iterrows(), 1):
        print(f"{i:2d}. {row['protein']:20s} HR={row['hazard_ratio']:.3f} p={row['p_value']:.2e}")

def main():
    """主函数"""
    print("Cox回归结果可视化分析")
    print("-" * 40)
    
    # 加载数据
    df = load_data('hfsnef_protein_cox_results.csv')
    if df is None:
        return
    
    # 生成摘要报告
    generate_summary_report(df)
    
    # 创建各种图表
    print("\n开始生成图表...")
    
    # 1. 火山图
    create_volcano_plot(df)
    
    # 2. 风险比分布图
    create_hr_distribution_plot(df)
    
    # 3. 显著蛋白质条形图
    create_top_proteins_plot(df)
    
    # 4. 置信区间图
    create_confidence_interval_plot(df)
    
    print("\n所有图表生成完成！")

if __name__ == "__main__":
    main()
