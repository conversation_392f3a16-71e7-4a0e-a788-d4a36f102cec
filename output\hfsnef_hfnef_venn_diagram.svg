<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="356.349498pt" height="364.19625pt" viewBox="0 0 356.349498 364.19625" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-31T23:35:58.475269</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.3, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 364.19625 
L 356.349498 364.19625 
L 356.349498 -0 
L 0 -0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 235.673723 317.18338 
C 215.283468 324.721512 193.369791 327.219372 171.80615 324.463411 
C 150.242509 321.707451 129.661438 313.778511 111.822468 301.354458 
C 93.983498 288.930405 79.409906 272.375679 69.347627 253.105586 
C 59.285347 233.835494 54.029541 212.415292 54.029541 190.67625 
C 54.029541 168.937208 59.285347 147.517006 69.347627 128.246914 
C 79.409906 108.976821 93.983498 92.422095 111.822468 79.998042 
C 129.661438 67.573989 150.242509 59.645049 171.80615 56.889089 
C 193.369791 54.133128 215.283468 56.630988 235.673723 64.16912 
C 214.185925 53.612033 190.358513 48.70167 166.447543 49.902958 
C 142.536573 51.104246 119.321295 58.378037 98.999802 71.035624 
C 78.678309 83.693211 61.912873 101.322089 50.290897 122.2531 
C 38.66892 143.184112 32.56916 166.735123 32.56916 190.67625 
C 32.56916 214.617377 38.66892 238.168388 50.290897 259.0994 
C 61.912873 280.030411 78.678309 297.659289 98.999802 310.316876 
C 119.321295 322.974463 142.536573 330.248254 166.447543 331.449542 
C 190.358513 332.65083 214.185925 327.740467 235.673723 317.18338 
" clip-path="url(#p300f87d97a)" style="fill: #add8e6; opacity: 0.4; stroke: #add8e6; stroke-linejoin: miter"/>
   </g>
   <g id="patch_3">
    <path d="M 235.673723 64.16912 
C 259.315376 75.784408 279.234291 93.797612 293.160626 116.156052 
C 307.086961 138.514492 314.47084 164.335357 314.47084 190.67625 
C 314.47084 217.017143 307.086961 242.838008 293.160626 265.196448 
C 279.234291 287.554888 259.315376 305.568092 235.673723 317.18338 
C 261.523088 307.627055 283.832787 290.373742 299.583148 267.758721 
C 315.333508 245.143701 323.780338 218.235514 323.780338 190.67625 
C 323.780338 163.116986 315.333508 136.208799 299.583148 113.593779 
C 283.832787 90.978758 261.523088 73.725445 235.673723 64.16912 
" clip-path="url(#p300f87d97a)" style="fill: #90ee90; opacity: 0.4; stroke: #90ee90; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 235.673723 317.18338 
C 259.315376 305.568092 279.234291 287.554888 293.160626 265.196448 
C 307.086961 242.838008 314.47084 217.017143 314.47084 190.67625 
C 314.47084 164.335357 307.086961 138.514492 293.160626 116.156052 
C 279.234291 93.797612 259.315376 75.784408 235.673723 64.16912 
C 215.283468 56.630988 193.369791 54.133128 171.80615 56.889089 
C 150.242509 59.645049 129.661438 67.573989 111.822468 79.998042 
C 93.983498 92.422095 79.409906 108.976821 69.347627 128.246914 
C 59.285347 147.517006 54.029541 168.937208 54.029541 190.67625 
C 54.029541 212.415292 59.285347 233.835494 69.347627 253.105586 
C 79.409906 272.375679 93.983498 288.930405 111.822468 301.354458 
C 129.661438 313.778511 150.242509 321.707451 171.80615 324.463411 
C 193.369791 327.219372 215.283468 324.721512 235.673723 317.18338 
" clip-path="url(#p300f87d97a)" style="fill: #d3d3d3; opacity: 0.4; stroke: #d3d3d3; stroke-linejoin: miter"/>
   </g>
   <g id="text_1">
    <!-- 493 -->
    <g transform="translate(34.29935 194.05125) scale(0.12 -0.12)">
     <defs>
      <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-39" d="M 800 75 
Q 1350 1000 1700 1750 
Q 1075 1650 662 1912 
Q 250 2175 162 2625 
Q 75 3075 175 3437 
Q 275 3800 487 4037 
Q 700 4275 1000 4375 
Q 1300 4475 1537 4475 
Q 1775 4475 2025 4375 
Q 2275 4275 2475 4075 
Q 2675 3875 2750 3662 
Q 2825 3450 2825 3112 
Q 2825 2775 2575 2250 
Q 2325 1725 1350 75 
L 800 75 
z
M 662 2875 
Q 700 2550 937 2337 
Q 1175 2125 1450 2150 
Q 1725 2175 1887 2300 
Q 2050 2425 2200 2725 
Q 2300 3100 2250 3362 
Q 2200 3625 1987 3800 
Q 1775 3975 1525 3975 
Q 1375 4000 1137 3900 
Q 900 3800 762 3500 
Q 625 3200 662 2875 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-33" d="M 250 1225 
L 700 1300 
Q 800 975 1025 762 
Q 1250 550 1587 562 
Q 1925 575 2125 837 
Q 2325 1100 2300 1437 
Q 2275 1775 2037 1962 
Q 1800 2150 1275 2225 
L 1275 2550 
Q 1800 2600 2037 2825 
Q 2275 3050 2250 3412 
Q 2225 3775 1925 3937 
Q 1625 4100 1287 3975 
Q 950 3850 750 3275 
L 300 3350 
Q 450 3800 712 4100 
Q 975 4400 1425 4450 
Q 1875 4500 2212 4337 
Q 2550 4175 2687 3837 
Q 2825 3500 2725 3100 
Q 2625 2700 2150 2400 
Q 2500 2250 2687 1950 
Q 2875 1650 2812 1162 
Q 2750 675 2375 375 
Q 2000 75 1525 87 
Q 1050 100 700 387 
Q 350 675 250 1225 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-34"/>
     <use xlink:href="#SimHei-39" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-33" transform="translate(100 0)"/>
    </g>
   </g>
   <g id="text_2">
    <!-- 133 -->
    <g transform="translate(310.125589 194.05125) scale(0.12 -0.12)">
     <defs>
      <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-31"/>
     <use xlink:href="#SimHei-33" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-33" transform="translate(100 0)"/>
    </g>
   </g>
   <g id="text_3">
    <!-- 3775 -->
    <g transform="translate(172.250191 194.05125) scale(0.12 -0.12)">
     <defs>
      <path id="SimHei-37" d="M 850 150 
Q 1300 2050 2425 3925 
L 275 3925 
L 275 4375 
L 2950 4375 
L 2950 3950 
Q 1775 2050 1400 150 
L 850 150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-33"/>
     <use xlink:href="#SimHei-37" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-37" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-35" transform="translate(150 0)"/>
    </g>
   </g>
   <g id="text_4">
    <!-- HFnEF -->
    <g transform="translate(143.52 353.668402) scale(0.12 -0.12)">
     <defs>
      <path id="SimHei-48" d="M 2850 125 
L 2275 125 
L 2275 2125 
L 850 2125 
L 850 125 
L 275 125 
L 275 4400 
L 850 4400 
L 850 2600 
L 2275 2600 
L 2275 4400 
L 2850 4400 
L 2850 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-46" d="M 2925 3925 
L 875 3925 
L 875 2525 
L 2425 2525 
L 2425 2050 
L 875 2050 
L 875 125 
L 300 125 
L 300 4400 
L 2925 4400 
L 2925 3925 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6e" d="M 2800 125 
L 2300 125 
L 2300 1925 
Q 2300 2225 2150 2400 
Q 2000 2575 1750 2575 
Q 1425 2575 1137 2237 
Q 850 1900 850 1400 
L 850 125 
L 350 125 
L 350 2925 
L 850 2925 
L 850 2400 
Q 1050 2675 1287 2825 
Q 1525 2975 1900 2975 
Q 2350 2975 2575 2725 
Q 2800 2475 2800 2100 
L 2800 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-45" d="M 2900 125 
L 350 125 
L 350 4400 
L 2775 4400 
L 2775 3925 
L 925 3925 
L 925 2600 
L 2625 2600 
L 2625 2125 
L 925 2125 
L 925 600 
L 2900 600 
L 2900 125 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-48"/>
     <use xlink:href="#SimHei-46" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-6e" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-45" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-46" transform="translate(200 0)"/>
    </g>
   </g>
   <g id="text_5">
    <!-- HFsnEF -->
    <g transform="translate(188.90494 347.592961) scale(0.12 -0.12)">
     <defs>
      <path id="SimHei-73" d="M 2750 900 
Q 2750 500 2437 287 
Q 2125 75 1650 75 
Q 1050 75 725 312 
Q 400 550 400 1000 
L 900 1000 
Q 900 700 1112 600 
Q 1325 500 1625 500 
Q 1925 500 2075 612 
Q 2225 725 2225 900 
Q 2225 1025 2100 1150 
Q 1975 1275 1475 1350 
Q 900 1425 687 1637 
Q 475 1850 475 2200 
Q 475 2500 762 2737 
Q 1050 2975 1600 2975 
Q 2100 2975 2387 2750 
Q 2675 2525 2675 2150 
L 2175 2150 
Q 2175 2375 2012 2462 
Q 1850 2550 1600 2550 
Q 1275 2550 1137 2437 
Q 1000 2325 1000 2175 
Q 1000 2000 1125 1900 
Q 1250 1800 1650 1750 
Q 2300 1650 2525 1437 
Q 2750 1225 2750 900 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-48"/>
     <use xlink:href="#SimHei-46" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-73" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-6e" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-45" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-46" transform="translate(250 0)"/>
    </g>
   </g>
   <g id="text_6">
    <!-- HFsnEF组与HFnEF组蛋白质表达韦恩图 -->
    <g transform="translate(62.674749 18.35625) scale(0.14 -0.14)">
     <defs>
      <path id="SimHei-7ec4" d="M 6125 -425 
Q 5750 -400 5225 -400 
L 3275 -400 
Q 2775 -400 2400 -425 
L 2400 50 
Q 2725 25 2950 25 
L 2950 3775 
Q 2950 4375 2900 4625 
L 5525 4625 
Q 5500 4400 5500 3775 
L 5500 25 
Q 5825 25 6125 50 
L 6125 -425 
z
M 1725 4825 
Q 1500 4550 1287 4137 
Q 1075 3725 725 3050 
Q 950 3050 1550 3075 
Q 1800 3550 1900 3975 
Q 2075 3825 2450 3675 
Q 2225 3350 1937 2875 
Q 1650 2400 1150 1625 
Q 1950 1725 2425 1775 
L 2425 1350 
Q 2100 1325 1575 1250 
Q 1050 1175 575 1050 
Q 550 1300 400 1575 
Q 675 1675 912 1975 
Q 1150 2275 1375 2725 
Q 1225 2725 950 2687 
Q 675 2650 425 2525 
Q 325 2825 225 3050 
Q 450 3300 712 3862 
Q 975 4425 1150 5075 
Q 1425 4925 1725 4825 
z
M 5025 25 
L 5025 1275 
L 3425 1275 
L 3425 25 
L 5025 25 
z
M 5025 3125 
L 5025 4225 
L 3425 4225 
L 3425 3125 
L 5025 3125 
z
M 5025 1700 
L 5025 2725 
L 3425 2725 
L 3425 1700 
L 5025 1700 
z
M 2375 175 
Q 1875 100 1375 -12 
Q 875 -125 450 -300 
Q 375 -75 250 250 
Q 925 325 1162 375 
Q 1400 425 2375 600 
L 2375 175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-4e0e" d="M 5350 100 
Q 5275 -300 4925 -425 
Q 4575 -550 3900 -600 
Q 3875 -375 3650 -25 
Q 4375 -50 4575 0 
Q 4775 50 4837 225 
Q 4900 400 5100 2350 
L 1125 2350 
Q 1450 4350 1475 5200 
Q 1800 5150 2075 5125 
Q 1975 4825 1900 4275 
L 4900 4275 
Q 5400 4275 5800 4300 
L 5800 3825 
Q 5400 3850 4950 3850 
L 1850 3850 
L 1700 2750 
L 5675 2750 
Q 5625 2450 5575 2050 
L 5350 100 
z
M 3675 1350 
Q 4125 1350 4500 1375 
L 4500 875 
Q 4125 900 3850 900 
L 1450 900 
Q 1000 900 500 875 
L 500 1375 
Q 1000 1350 1450 1350 
L 3675 1350 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-86cb" d="M 3450 2500 
Q 3425 2250 3425 2050 
L 5400 2050 
Q 5375 1750 5375 1375 
Q 5375 1025 5400 750 
L 3425 750 
L 3425 125 
Q 3800 125 4775 150 
Q 4575 325 4450 425 
Q 4700 550 4875 675 
Q 5175 425 5437 187 
Q 5700 -50 5950 -300 
Q 5775 -400 5475 -600 
Q 5275 -375 5100 -175 
Q 3925 -250 2700 -312 
Q 1475 -375 625 -475 
Q 575 -150 500 25 
Q 1025 0 1650 37 
Q 2275 75 2950 75 
L 2950 750 
L 1550 750 
L 1550 500 
L 1075 500 
Q 1075 750 1100 1050 
L 1100 1500 
Q 1100 1800 1075 2050 
L 2950 2050 
Q 2950 2200 2925 2500 
L 3450 2500 
z
M 1950 3575 
Q 2275 3275 2950 3100 
L 2950 4575 
L 1450 4575 
Q 1050 4575 575 4550 
L 575 4975 
Q 1050 4950 1450 4950 
L 5925 4950 
Q 5650 4400 5475 4125 
Q 5225 4225 4975 4250 
Q 5075 4400 5150 4575 
L 3425 4575 
L 3425 3975 
L 4400 3975 
Q 4800 3975 5275 4000 
L 5275 3575 
Q 4800 3600 4400 3600 
L 3425 3600 
L 3425 3000 
Q 3800 2950 4575 2962 
Q 5350 2975 6150 3075 
Q 6050 2800 6025 2525 
Q 5000 2550 4325 2550 
Q 3675 2550 3200 2637 
Q 2725 2725 2400 2862 
Q 2075 3000 1725 3275 
Q 1475 2925 1225 2712 
Q 975 2500 625 2200 
Q 475 2475 250 2650 
Q 800 2900 1175 3325 
Q 1550 3750 1675 4300 
Q 1900 4175 2250 4075 
Q 2050 3900 1950 3575 
z
M 4925 1125 
L 4925 1725 
L 3425 1725 
L 3425 1125 
L 4925 1125 
z
M 2950 1125 
L 2950 1725 
L 1550 1725 
L 1550 1125 
L 2950 1125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-767d" d="M 3175 4975 
Q 3050 4850 2925 4637 
Q 2800 4425 2675 4175 
L 5550 4175 
Q 5525 3675 5525 2950 
L 5525 725 
Q 5525 175 5550 -500 
L 5025 -500 
L 5025 125 
L 1475 125 
L 1475 -550 
L 950 -550 
Q 975 200 975 700 
L 975 2950 
Q 975 3625 950 4175 
L 2200 4175 
Q 2450 4725 2575 5250 
Q 2850 5100 3175 4975 
z
M 5025 575 
L 5025 1975 
L 1475 1975 
L 1475 575 
L 5025 575 
z
M 5025 2425 
L 5025 3750 
L 1475 3750 
L 1475 2425 
L 5025 2425 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8d28" d="M 1700 3700 
Q 2125 3675 2550 3675 
L 3400 3675 
L 3400 4325 
Q 1875 4250 1575 4250 
L 1575 3675 
Q 1550 2525 1525 1962 
Q 1500 1400 1387 837 
Q 1275 275 875 -450 
Q 650 -300 300 -225 
Q 700 300 850 862 
Q 1000 1425 1037 2325 
Q 1075 3225 1075 3725 
Q 1075 4250 1050 4650 
Q 1775 4650 2437 4675 
Q 3100 4700 3900 4775 
Q 4700 4850 5275 5075 
Q 5325 4825 5600 4500 
Q 5300 4500 4912 4462 
Q 4525 4425 3875 4375 
L 3875 3675 
L 5125 3675 
Q 5475 3675 5900 3700 
L 5900 3225 
Q 5525 3250 5125 3250 
L 3875 3250 
L 3875 2600 
L 5425 2600 
Q 5400 2175 5400 1750 
Q 5400 1325 5425 900 
L 4900 900 
L 4900 2200 
L 2575 2200 
L 2575 750 
L 2075 750 
Q 2100 1225 2100 1700 
Q 2100 2175 2075 2600 
L 3400 2600 
L 3400 3250 
L 2550 3250 
Q 2125 3250 1700 3225 
L 1700 3700 
z
M 4000 1800 
Q 3900 1400 3837 912 
Q 3775 425 3262 25 
Q 2750 -375 1875 -650 
Q 1825 -450 1525 -250 
Q 2225 -75 2637 150 
Q 3050 375 3225 687 
Q 3400 1000 3425 1350 
Q 3450 1700 3425 1875 
Q 3725 1850 4000 1800 
z
M 4250 850 
Q 4650 650 5137 375 
Q 5625 100 6000 -175 
Q 5850 -300 5675 -575 
Q 5125 -125 4000 425 
Q 4150 675 4250 850 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8868" d="M 2950 4475 
Q 2950 4800 2925 5200 
L 3500 5200 
Q 3450 4900 3450 4475 
L 4975 4475 
Q 5325 4475 5725 4500 
L 5725 4025 
Q 5325 4075 5000 4075 
L 3450 4075 
L 3450 3600 
L 4675 3600 
Q 5075 3600 5450 3625 
L 5450 3175 
Q 5100 3200 4675 3200 
L 3450 3200 
L 3450 2650 
L 5225 2650 
Q 5675 2650 6075 2675 
L 6075 2200 
Q 5700 2250 5250 2250 
L 3075 2250 
Q 2900 2025 2525 1625 
L 2525 175 
Q 2975 425 3525 800 
Q 3575 500 3700 275 
Q 3225 50 2837 -200 
Q 2450 -450 2250 -625 
Q 2050 -375 1900 -175 
Q 2075 -50 2050 400 
L 2050 1200 
Q 1750 950 1425 737 
Q 1100 525 750 325 
Q 600 550 375 750 
Q 975 1000 1562 1425 
Q 2150 1850 2450 2250 
L 1275 2250 
Q 800 2250 350 2200 
L 350 2675 
Q 650 2650 1350 2650 
L 2950 2650 
L 2950 3200 
L 1825 3200 
Q 1400 3200 1125 3175 
L 1125 3625 
Q 1525 3600 1825 3600 
L 2950 3600 
L 2950 4075 
L 1700 4075 
Q 1225 4075 825 4025 
L 825 4525 
Q 1200 4475 1825 4475 
L 2950 4475 
z
M 3550 2125 
Q 3725 1600 4150 1050 
Q 4650 1450 5050 2000 
Q 5275 1775 5575 1575 
Q 5350 1500 5075 1262 
Q 4800 1025 4475 725 
Q 4725 475 5225 337 
Q 5725 200 6150 175 
Q 5925 -100 5875 -425 
Q 4600 -125 4012 500 
Q 3425 1125 3150 1950 
L 3550 2125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8fbe" d="M 1700 2975 
Q 1675 2500 1675 2225 
L 1675 775 
Q 2025 350 2600 150 
Q 3175 -50 4375 -50 
Q 5600 -50 6150 75 
Q 5975 -325 5975 -550 
Q 4925 -575 4212 -550 
Q 3500 -525 3025 -437 
Q 2550 -350 2287 -225 
Q 2025 -100 1737 137 
Q 1450 375 1212 125 
Q 975 -125 700 -475 
Q 525 -300 275 -100 
Q 625 200 1175 700 
L 1175 2550 
Q 825 2550 375 2525 
L 375 3000 
Q 900 2975 1700 2975 
z
M 3700 3700 
Q 3700 4800 3675 5225 
Q 4100 5175 4275 5150 
Q 4200 4825 4175 3700 
Q 5475 3700 5875 3725 
L 5875 3225 
Q 5475 3250 4150 3250 
Q 4075 2400 3687 1700 
Q 3300 1000 2500 375 
Q 2400 575 2125 775 
Q 2775 1225 3162 1812 
Q 3550 2400 3650 3250 
Q 2625 3250 2150 3225 
L 2150 3725 
Q 2625 3700 3700 3700 
z
M 4475 2450 
Q 5600 1225 5925 800 
Q 5675 650 5450 425 
Q 5075 1000 4100 2100 
Q 4275 2225 4475 2450 
z
M 1100 5125 
Q 1575 4575 1950 4075 
Q 1725 3900 1525 3700 
Q 1125 4325 700 4800 
Q 900 4925 1100 5125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-97e6" d="M 2725 4225 
Q 2725 4800 2675 5175 
L 3225 5175 
Q 3200 4825 3200 4225 
L 4825 4225 
Q 5250 4225 5750 4250 
L 5750 3775 
Q 5250 3800 4950 3800 
L 3200 3800 
L 3200 3025 
L 4625 3025 
Q 5025 3025 5475 3050 
L 5475 2575 
Q 5050 2600 4625 2600 
L 3200 2600 
L 3200 1825 
L 5800 1825 
Q 5775 1475 5737 962 
Q 5700 450 5637 225 
Q 5575 0 5262 -62 
Q 4950 -125 4450 -175 
Q 4425 75 4225 400 
Q 4825 375 4987 400 
Q 5150 425 5200 675 
L 5250 1400 
L 3200 1400 
L 3200 400 
Q 3200 0 3225 -550 
L 2675 -550 
Q 2725 -25 2725 400 
L 2725 1400 
L 1450 1400 
Q 950 1400 475 1375 
L 475 1850 
Q 925 1825 1450 1825 
L 2725 1825 
L 2725 2600 
L 1925 2600 
Q 1475 2600 1000 2575 
L 1000 3050 
Q 1550 3025 1925 3025 
L 2725 3025 
L 2725 3800 
L 1675 3800 
Q 1075 3800 625 3775 
L 625 4250 
Q 1100 4225 1675 4225 
L 2725 4225 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6069" d="M 5300 5050 
Q 5275 4375 5275 3425 
Q 5275 2500 5300 1875 
L 1100 1875 
Q 1125 2750 1125 3600 
Q 1125 4450 1100 5050 
L 5300 5050 
z
M 4800 2275 
L 4800 4625 
L 1600 4625 
L 1600 2275 
L 4800 2275 
z
M 2925 4525 
Q 3175 4500 3475 4450 
Q 3400 4200 3400 3975 
Q 3875 3975 4400 4000 
L 4400 3550 
Q 3925 3575 3350 3575 
L 3300 3425 
Q 3950 3150 4525 2775 
Q 4325 2600 4150 2375 
Q 3675 2750 3125 3050 
Q 2675 2600 2125 2300 
Q 1975 2550 1775 2725 
Q 2125 2825 2437 3050 
Q 2750 3275 2875 3575 
Q 2400 3575 1950 3550 
L 1950 4000 
Q 2375 3975 2925 3975 
L 2925 4525 
z
M 2375 275 
Q 2375 -25 2750 -25 
L 3850 -25 
Q 4175 -25 4250 50 
Q 4325 125 4375 575 
Q 4625 375 4900 325 
Q 4725 -200 4587 -325 
Q 4450 -450 4125 -450 
L 2425 -450 
Q 1900 -450 1900 50 
L 1900 500 
Q 1900 800 1875 1275 
L 2400 1275 
Q 2375 850 2375 600 
L 2375 275 
z
M 5675 0 
Q 5350 500 4650 1175 
Q 4925 1375 5100 1575 
Q 5775 825 6100 400 
Q 5875 225 5675 0 
z
M 1525 1075 
Q 1375 850 1200 500 
Q 1025 150 750 -300 
Q 525 -75 275 25 
Q 700 575 1000 1350 
Q 1275 1200 1525 1075 
z
M 3525 675 
Q 3400 900 2925 1375 
Q 3150 1575 3325 1750 
Q 3725 1325 3975 1000 
Q 3700 850 3525 675 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-56fe" d="M 5800 4975 
Q 5775 4350 5775 3600 
L 5775 925 
Q 5775 150 5800 -600 
L 5300 -600 
L 5300 -275 
L 1125 -275 
L 1125 -650 
L 625 -650 
Q 650 125 650 950 
L 650 3600 
Q 650 4325 625 4975 
L 5800 4975 
z
M 5300 150 
L 5300 4575 
L 1125 4575 
L 1125 150 
L 5300 150 
z
M 3225 4250 
Q 3075 4100 2950 3925 
L 4625 3925 
Q 4525 3700 4300 3375 
Q 4075 3050 3600 2600 
Q 4350 2225 5075 2150 
Q 4900 1925 4775 1675 
Q 3850 1950 3225 2350 
Q 2500 1875 1675 1650 
Q 1600 1875 1400 2075 
Q 2200 2200 2875 2600 
Q 2525 2925 2375 3150 
Q 2150 2900 1900 2650 
Q 1725 2850 1525 2950 
Q 1950 3300 2250 3737 
Q 2550 4175 2675 4475 
Q 2925 4350 3225 4250 
z
M 2450 1275 
Q 3550 950 4100 725 
Q 3975 525 3850 300 
Q 2825 725 2275 850 
Q 2375 1050 2450 1275 
z
M 2925 2050 
Q 3775 1700 4075 1600 
Q 3950 1400 3850 1150 
Q 3000 1550 2700 1650 
Q 2825 1825 2925 2050 
z
M 2625 3450 
Q 3000 3025 3275 2850 
Q 3600 3100 3875 3525 
L 2675 3525 
L 2625 3450 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-48"/>
     <use xlink:href="#SimHei-46" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-73" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-6e" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-45" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-46" transform="translate(250 0)"/>
     <use xlink:href="#SimHei-7ec4" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-4e0e" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-48" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-46" transform="translate(550 0)"/>
     <use xlink:href="#SimHei-6e" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-45" transform="translate(650 0)"/>
     <use xlink:href="#SimHei-46" transform="translate(700 0)"/>
     <use xlink:href="#SimHei-7ec4" transform="translate(750 0)"/>
     <use xlink:href="#SimHei-86cb" transform="translate(850 0)"/>
     <use xlink:href="#SimHei-767d" transform="translate(950 0)"/>
     <use xlink:href="#SimHei-8d28" transform="translate(1050 0)"/>
     <use xlink:href="#SimHei-8868" transform="translate(1150 0)"/>
     <use xlink:href="#SimHei-8fbe" transform="translate(1250 0)"/>
     <use xlink:href="#SimHei-97e6" transform="translate(1350 0)"/>
     <use xlink:href="#SimHei-6069" transform="translate(1450 0)"/>
     <use xlink:href="#SimHei-56fe" transform="translate(1550 0)"/>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p300f87d97a">
   <rect x="7.2" y="24.35625" width="341.949498" height="332.64"/>
  </clipPath>
 </defs>
</svg>
