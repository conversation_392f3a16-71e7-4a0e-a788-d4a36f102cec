#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的Cox比例风险回归分析 - 所有代谢物
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from lifelines import CoxPHFitter
from lifelines.statistics import logrank_test
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def perform_comprehensive_cox_analysis():
    """对所有代谢物进行Cox回归分析"""
    
    print("=" * 80)
    print("全面Cox比例风险回归分析 - 所有代谢物")
    print("=" * 80)
    
    # 读取数据
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    
    # 获取所有代谢物列
    metabolite_cols = [col for col in df.columns if '_POS' in col or '_NEG' in col]
    
    print(f"数据概况:")
    print(f"总样本数: {len(df)}")
    print(f"代谢物总数: {len(metabolite_cols)}")
    
    # 清理数据
    analysis_df = df.dropna(subset=['NonCV_death', 'OS'])
    print(f"有效样本数: {len(analysis_df)}")
    print(f"非心血管死亡事件数: {int(analysis_df['NonCV_death'].sum())}")
    print()
    
    # 进行Cox回归分析
    cox_results = []
    failed_metabolites = []
    
    print("正在进行全面Cox回归分析...")
    print("进度: ", end="", flush=True)
    
    for i, metabolite in enumerate(metabolite_cols):
        # 显示进度
        if i % 50 == 0:
            print(f"{i}/{len(metabolite_cols)}", end=" ", flush=True)
        
        try:
            # 准备数据
            mask = ~(analysis_df[metabolite].isna() | analysis_df['OS'].isna() | analysis_df['NonCV_death'].isna())
            if mask.sum() < 20:  # 样本量太小跳过
                failed_metabolites.append((metabolite, "样本量不足"))
                continue
                
            cox_df = analysis_df.loc[mask, [metabolite, 'OS', 'NonCV_death']].copy()
            
            # 检查代谢物是否有变异
            if cox_df[metabolite].std() == 0:
                failed_metabolites.append((metabolite, "无变异"))
                continue
            
            # 标准化代谢物浓度（使用Z-score）
            cox_df[f'{metabolite}_std'] = (cox_df[metabolite] - cox_df[metabolite].mean()) / cox_df[metabolite].std()
            
            # 准备Cox回归数据
            cox_data = cox_df[['OS', 'NonCV_death', f'{metabolite}_std']].copy()
            cox_data.columns = ['duration', 'event', 'metabolite_std']
            
            # 检查事件数
            if cox_data['event'].sum() < 3:
                failed_metabolites.append((metabolite, "事件数不足"))
                continue
            
            # 拟合Cox模型
            cph = CoxPHFitter()
            cph.fit(cox_data, duration_col='duration', event_col='event')
            
            # 提取结果
            coef = cph.params_['metabolite_std']
            hr = np.exp(coef)
            se = cph.standard_errors_['metabolite_std']
            ci_lower = np.exp(coef - 1.96 * se)
            ci_upper = np.exp(coef + 1.96 * se)
            p_value = cph.summary.loc['metabolite_std', 'p']
            z_score = cph.summary.loc['metabolite_std', 'z']
            
            # 计算C-index
            c_index = cph.concordance_index_
            
            # Log-rank检验（将代谢物分为高低两组）
            median_value = cox_df[metabolite].median()
            cox_df['metabolite_high'] = (cox_df[metabolite] > median_value).astype(int)
            
            high_group = cox_df[cox_df['metabolite_high'] == 1]
            low_group = cox_df[cox_df['metabolite_high'] == 0]
            
            if len(high_group) > 0 and len(low_group) > 0 and high_group['NonCV_death'].sum() > 0 and low_group['NonCV_death'].sum() > 0:
                logrank_result = logrank_test(
                    high_group['OS'], low_group['OS'],
                    high_group['NonCV_death'], low_group['NonCV_death']
                )
                logrank_p = logrank_result.p_value
            else:
                logrank_p = np.nan
            
            cox_results.append({
                'metabolite': metabolite,
                'coefficient': coef,
                'hazard_ratio': hr,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'p_value': p_value,
                'z_score': z_score,
                'c_index': c_index,
                'logrank_p': logrank_p,
                'n_samples': len(cox_data),
                'n_events': int(cox_data['event'].sum()),
                'mean_metabolite': cox_df[metabolite].mean(),
                'std_metabolite': cox_df[metabolite].std(),
                'analysis_status': 'success'
            })
            
        except Exception as e:
            failed_metabolites.append((metabolite, f"分析错误: {str(e)}"))
            continue
    
    print(f"\n完成!")
    
    # 转换为DataFrame
    cox_results_df = pd.DataFrame(cox_results)
    
    print(f"\n分析结果:")
    print(f"成功分析的代谢物: {len(cox_results_df)}")
    print(f"失败的代谢物: {len(failed_metabolites)}")
    
    if len(cox_results_df) > 0:
        # 按p值排序
        cox_results_df = cox_results_df.sort_values('p_value')
        
        # 显著性统计
        significant_cox = cox_results_df[cox_results_df['p_value'] < 0.05]
        print(f"显著相关的代谢物 (P < 0.05): {len(significant_cox)}")
        
        # 保存结果
        cox_results_df.to_csv('all_metabolites_cox_results.csv', index=False)
        print(f"所有代谢物Cox分析结果已保存到: all_metabolites_cox_results.csv")
        
        # 保存失败的代谢物信息
        if failed_metabolites:
            failed_df = pd.DataFrame(failed_metabolites, columns=['metabolite', 'failure_reason'])
            failed_df.to_csv('failed_metabolites_cox.csv', index=False)
            print(f"失败的代谢物信息已保存到: failed_metabolites_cox.csv")
        
        # 显示前20个最显著的结果
        print(f"\n前20个最显著的代谢物:")
        print("=" * 120)
        print(f"{'代谢物名称':<45} {'HR':<8} {'95%CI':<20} {'P值':<10} {'方向':<6}")
        print("-" * 120)
        
        for i, row in cox_results_df.head(20).iterrows():
            metabolite_name = row['metabolite'].split('_')[0][:40]
            hr_str = f"{row['hazard_ratio']:.3f}"
            ci_str = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
            p_str = f"{row['p_value']:.4f}"
            direction = "保护" if row['hazard_ratio'] < 1 else "危险"
            
            print(f"{metabolite_name:<45} {hr_str:<8} {ci_str:<20} {p_str:<10} {direction:<6}")
        
        # 创建火山图
        create_cox_volcano_plot(cox_results_df)
        
        return cox_results_df
    
    else:
        print("未能完成任何代谢物的Cox回归分析")
        return pd.DataFrame()

def create_cox_volcano_plot(cox_results_df):
    """创建Cox回归结果的火山图"""
    
    print(f"\n正在创建Cox回归火山图...")
    
    # 准备数据
    plot_data = cox_results_df.copy()
    
    # 计算log2(HR)和-log10(P值)
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义显著性阈值
    p_threshold = 0.05
    hr_threshold_low = 0.67  # HR < 0.67 (保护性)
    hr_threshold_high = 1.5  # HR > 1.5 (危险性)
    
    # 分类点
    plot_data['significance'] = 'Non-significant'
    
    # 显著且有生物学意义的点
    significant_protective = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] < hr_threshold_low)
    significant_risk = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] > hr_threshold_high)
    significant_mild = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] >= hr_threshold_low) & (plot_data['hazard_ratio'] <= hr_threshold_high)
    
    plot_data.loc[significant_protective, 'significance'] = 'Significant Protective'
    plot_data.loc[significant_risk, 'significance'] = 'Significant Risk'
    plot_data.loc[significant_mild, 'significance'] = 'Significant Mild'
    
    # 创建图形
    plt.figure(figsize=(14, 10))
    
    # 定义颜色
    colors = {
        'Non-significant': 'lightgray',
        'Significant Mild': 'orange', 
        'Significant Protective': 'blue',
        'Significant Risk': 'red'
    }
    
    # 绘制散点图
    for category in colors.keys():
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            plt.scatter(plot_data.loc[mask, 'log2_hr'], 
                       plot_data.loc[mask, 'neg_log10_p'],
                       c=colors[category], 
                       alpha=0.7 if category == 'Non-significant' else 0.8,
                       s=30 if category == 'Non-significant' else 50,
                       label=f'{category} (n={mask.sum()})',
                       edgecolors='black' if category != 'Non-significant' else 'none',
                       linewidth=0.5)
    
    # 添加阈值线
    plt.axhline(y=-np.log10(p_threshold), color='gray', linestyle='--', alpha=0.7, label=f'P = {p_threshold}')
    plt.axvline(x=np.log2(hr_threshold_low), color='gray', linestyle='--', alpha=0.7, label=f'HR = {hr_threshold_low}')
    plt.axvline(x=np.log2(hr_threshold_high), color='gray', linestyle='--', alpha=0.7, label=f'HR = {hr_threshold_high}')
    plt.axvline(x=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
    
    # 标注最显著的点
    top_significant = plot_data[plot_data['p_value'] < 0.01].head(10)
    for i, row in top_significant.iterrows():
        metabolite_name = row['metabolite'].split('_')[0][:20]
        plt.annotate(metabolite_name, 
                    (row['log2_hr'], row['neg_log10_p']),
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, alpha=0.8,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
    
    # 设置标签和标题
    plt.xlabel('log₂(Hazard Ratio)', fontsize=12)
    plt.ylabel('-log₁₀(P值)', fontsize=12)
    plt.title('Cox回归分析火山图 - 代谢物与非心血管死亡', fontsize=14, fontweight='bold')
    
    # 添加图例
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f"总代谢物: {len(plot_data)}\n"
    stats_text += f"显著保护性: {significant_protective.sum()}\n"
    stats_text += f"显著危险性: {significant_risk.sum()}\n"
    stats_text += f"显著轻度: {significant_mild.sum()}"
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('cox_volcano_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Cox回归火山图已保存到: cox_volcano_plot.png")
    
    # 输出统计信息
    print(f"\n火山图统计:")
    print(f"总代谢物数量: {len(plot_data)}")
    print(f"显著保护性 (HR < {hr_threshold_low}, P < {p_threshold}): {significant_protective.sum()}")
    print(f"显著危险性 (HR > {hr_threshold_high}, P < {p_threshold}): {significant_risk.sum()}")
    print(f"显著轻度效应: {significant_mild.sum()}")
    print(f"非显著: {(plot_data['significance'] == 'Non-significant').sum()}")

def create_summary_table(cox_results_df):
    """创建结果总结表"""
    
    print(f"\n正在创建结果总结表...")
    
    # 显著结果
    significant_results = cox_results_df[cox_results_df['p_value'] < 0.05].copy()
    
    if len(significant_results) > 0:
        # 按效应大小分类
        strong_protective = significant_results[significant_results['hazard_ratio'] < 0.5]
        moderate_protective = significant_results[(significant_results['hazard_ratio'] >= 0.5) & (significant_results['hazard_ratio'] < 0.8)]
        mild_protective = significant_results[(significant_results['hazard_ratio'] >= 0.8) & (significant_results['hazard_ratio'] < 1.0)]
        mild_risk = significant_results[(significant_results['hazard_ratio'] >= 1.0) & (significant_results['hazard_ratio'] < 1.25)]
        moderate_risk = significant_results[(significant_results['hazard_ratio'] >= 1.25) & (significant_results['hazard_ratio'] < 2.0)]
        strong_risk = significant_results[significant_results['hazard_ratio'] >= 2.0]
        
        print(f"效应大小分类:")
        print(f"强保护性 (HR < 0.5): {len(strong_protective)}")
        print(f"中等保护性 (0.5 ≤ HR < 0.8): {len(moderate_protective)}")
        print(f"轻度保护性 (0.8 ≤ HR < 1.0): {len(mild_protective)}")
        print(f"轻度危险性 (1.0 ≤ HR < 1.25): {len(mild_risk)}")
        print(f"中等危险性 (1.25 ≤ HR < 2.0): {len(moderate_risk)}")
        print(f"强危险性 (HR ≥ 2.0): {len(strong_risk)}")
        
        # 保存分类结果
        summary_stats = {
            'category': ['强保护性', '中等保护性', '轻度保护性', '轻度危险性', '中等危险性', '强危险性'],
            'hr_range': ['< 0.5', '0.5-0.8', '0.8-1.0', '1.0-1.25', '1.25-2.0', '≥ 2.0'],
            'count': [len(strong_protective), len(moderate_protective), len(mild_protective), 
                     len(mild_risk), len(moderate_risk), len(strong_risk)]
        }
        
        summary_df = pd.DataFrame(summary_stats)
        summary_df.to_csv('cox_results_summary.csv', index=False)
        print(f"结果总结已保存到: cox_results_summary.csv")

def main():
    """主函数"""
    print("开始全面Cox比例风险回归分析...")
    
    # 进行全面Cox回归分析
    cox_results = perform_comprehensive_cox_analysis()
    
    if len(cox_results) > 0:
        # 创建总结表
        create_summary_table(cox_results)
        
        print(f"\n=== 全面分析完成 ===")
        print(f"所有结果文件:")
        print(f"• all_metabolites_cox_results.csv - 所有代谢物Cox分析结果")
        print(f"• cox_volcano_plot.png - Cox回归火山图")
        print(f"• cox_results_summary.csv - 结果总结")
        print(f"• failed_metabolites_cox.csv - 失败的代谢物信息")
    
    print(f"\n全面Cox回归分析完成!")

if __name__ == "__main__":
    main()
