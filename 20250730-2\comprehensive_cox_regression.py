#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面Cox回归分析 - 整体人群和分组分析
包括HR、置信区间、P值和FDR校正
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from lifelines import CoxPHFitter
from statsmodels.stats.multitest import multipletests
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载和预处理数据"""
    
    print("正在加载数据...")
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    
    # 获取代谢物列
    metabolite_cols = [col for col in df.columns if '_POS' in col or '_NEG' in col]
    
    # 清理数据
    df_clean = df.dropna(subset=['NonCV_death', 'OS', 'HFsnEF'])
    
    print(f"原始样本数: {len(df)}")
    print(f"清理后样本数: {len(df_clean)}")
    print(f"代谢物数量: {len(metabolite_cols)}")
    print(f"非心血管死亡事件: {int(df_clean['NonCV_death'].sum())}")
    
    # 分组统计
    hfnef_count = len(df_clean[df_clean['HFsnEF'] == 0])
    hfsnef_count = len(df_clean[df_clean['HFsnEF'] == 1])
    hfnef_events = int(df_clean[df_clean['HFsnEF'] == 0]['NonCV_death'].sum())
    hfsnef_events = int(df_clean[df_clean['HFsnEF'] == 1]['NonCV_death'].sum())
    
    print(f"HFnEF组 (HFsnEF=0): {hfnef_count}例, 事件{hfnef_events}个")
    print(f"HFsnEF组 (HFsnEF=1): {hfsnef_count}例, 事件{hfsnef_events}个")
    print()
    
    return df_clean, metabolite_cols

def perform_cox_analysis(df, metabolite_cols, group_name="Overall", group_filter=None):
    """执行Cox回归分析"""
    
    print(f"正在进行{group_name}组Cox回归分析...")
    
    # 应用分组过滤
    if group_filter is not None:
        analysis_df = df[group_filter].copy()
    else:
        analysis_df = df.copy()
    
    print(f"分析样本数: {len(analysis_df)}")
    print(f"事件数: {int(analysis_df['NonCV_death'].sum())}")
    
    cox_results = []
    failed_metabolites = []
    
    print("进度: ", end="", flush=True)
    
    for i, metabolite in enumerate(metabolite_cols):
        # 显示进度
        if i % 50 == 0:
            print(f"{i}/{len(metabolite_cols)}", end=" ", flush=True)
        
        try:
            # 准备数据
            mask = ~(analysis_df[metabolite].isna() | analysis_df['OS'].isna() | analysis_df['NonCV_death'].isna())
            if mask.sum() < 10:  # 样本量太小
                failed_metabolites.append((metabolite, "样本量不足"))
                continue
                
            cox_df = analysis_df.loc[mask, [metabolite, 'OS', 'NonCV_death']].copy()
            
            # 检查代谢物变异
            if cox_df[metabolite].std() == 0:
                failed_metabolites.append((metabolite, "无变异"))
                continue
            
            # 检查事件数
            if cox_df['NonCV_death'].sum() < 2:
                failed_metabolites.append((metabolite, "事件数不足"))
                continue
            
            # 标准化代谢物浓度
            cox_df[f'{metabolite}_std'] = (cox_df[metabolite] - cox_df[metabolite].mean()) / cox_df[metabolite].std()
            
            # 准备Cox回归数据
            cox_data = cox_df[['OS', 'NonCV_death', f'{metabolite}_std']].copy()
            cox_data.columns = ['duration', 'event', 'metabolite_std']
            
            # 拟合Cox模型
            cph = CoxPHFitter()
            cph.fit(cox_data, duration_col='duration', event_col='event')
            
            # 提取结果
            coef = cph.params_['metabolite_std']
            hr = np.exp(coef)
            se = cph.standard_errors_['metabolite_std']
            ci_lower = np.exp(coef - 1.96 * se)
            ci_upper = np.exp(coef + 1.96 * se)
            p_value = cph.summary.loc['metabolite_std', 'p']
            z_score = cph.summary.loc['metabolite_std', 'z']
            
            # 计算C-index
            c_index = cph.concordance_index_
            
            cox_results.append({
                'metabolite': metabolite,
                'group': group_name,
                'coefficient': coef,
                'hazard_ratio': hr,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'p_value': p_value,
                'z_score': z_score,
                'c_index': c_index,
                'n_samples': len(cox_data),
                'n_events': int(cox_data['event'].sum()),
                'mean_metabolite': cox_df[metabolite].mean(),
                'std_metabolite': cox_df[metabolite].std()
            })
            
        except Exception as e:
            failed_metabolites.append((metabolite, f"分析错误: {str(e)}"))
            continue
    
    print(f"\n完成!")
    
    # 转换为DataFrame
    cox_results_df = pd.DataFrame(cox_results)
    
    if len(cox_results_df) > 0:
        # 按p值排序
        cox_results_df = cox_results_df.sort_values('p_value')
        
        # FDR校正
        if len(cox_results_df) > 1:
            _, fdr_values, _, _ = multipletests(cox_results_df['p_value'], method='fdr_bh')
            cox_results_df['fdr_value'] = fdr_values
        else:
            cox_results_df['fdr_value'] = cox_results_df['p_value']
        
        # 显著性统计
        significant_p = cox_results_df[cox_results_df['p_value'] < 0.05]
        significant_fdr = cox_results_df[cox_results_df['fdr_value'] < 0.05]
        
        print(f"\n{group_name}组分析结果:")
        print(f"成功分析的代谢物: {len(cox_results_df)}")
        print(f"失败的代谢物: {len(failed_metabolites)}")
        print(f"名义显著 (P < 0.05): {len(significant_p)}")
        print(f"FDR显著 (FDR < 0.05): {len(significant_fdr)}")
        
        # 保存结果
        filename = f'{group_name.lower()}_cox_comprehensive_results.csv'
        cox_results_df.to_csv(filename, index=False)
        print(f"结果已保存到: {filename}")
        
        # 保存失败信息
        if failed_metabolites:
            failed_filename = f'{group_name.lower()}_failed_metabolites.csv'
            failed_df = pd.DataFrame(failed_metabolites, columns=['metabolite', 'failure_reason'])
            failed_df.to_csv(failed_filename, index=False)
        
        # 显示前10个最显著的结果
        print(f"\n{group_name}组前10个最显著的代谢物:")
        print("=" * 120)
        print(f"{'代谢物名称':<40} {'HR':<8} {'95%CI':<20} {'P值':<10} {'FDR':<10} {'方向':<6}")
        print("-" * 120)
        
        for i, row in cox_results_df.head(10).iterrows():
            metabolite_name = row['metabolite'].split('_')[0][:35]
            hr_str = f"{row['hazard_ratio']:.3f}"
            ci_str = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
            p_str = f"{row['p_value']:.4f}"
            fdr_str = f"{row['fdr_value']:.4f}"
            direction = "保护" if row['hazard_ratio'] < 1 else "危险"
            
            print(f"{metabolite_name:<40} {hr_str:<8} {ci_str:<20} {p_str:<10} {fdr_str:<10} {direction:<6}")
        
        return cox_results_df
    
    else:
        print(f"未能完成{group_name}组任何代谢物的Cox回归分析")
        return pd.DataFrame()

def create_comprehensive_summary(overall_results, hfnef_results, hfsnef_results):
    """创建综合分析总结"""
    
    print(f"\n正在创建综合分析总结...")
    
    # 统计信息
    summary_stats = []
    
    for name, results in [("Overall", overall_results), ("HFnEF", hfnef_results), ("HFsnEF", hfsnef_results)]:
        if len(results) > 0:
            total = len(results)
            sig_p = len(results[results['p_value'] < 0.05])
            sig_fdr = len(results[results['fdr_value'] < 0.05])
            protective_p = len(results[(results['p_value'] < 0.05) & (results['hazard_ratio'] < 1)])
            risk_p = len(results[(results['p_value'] < 0.05) & (results['hazard_ratio'] > 1)])
            protective_fdr = len(results[(results['fdr_value'] < 0.05) & (results['hazard_ratio'] < 1)])
            risk_fdr = len(results[(results['fdr_value'] < 0.05) & (results['hazard_ratio'] > 1)])
            
            summary_stats.append({
                'Group': name,
                'Total_Metabolites': total,
                'Significant_P005': sig_p,
                'Significant_FDR005': sig_fdr,
                'Protective_P005': protective_p,
                'Risk_P005': risk_p,
                'Protective_FDR005': protective_fdr,
                'Risk_FDR005': risk_fdr,
                'Significance_Rate_P': f"{sig_p/total*100:.1f}%",
                'Significance_Rate_FDR': f"{sig_fdr/total*100:.1f}%"
            })
    
    summary_df = pd.DataFrame(summary_stats)
    summary_df.to_csv('comprehensive_cox_summary.csv', index=False)
    
    print("综合分析总结已保存到: comprehensive_cox_summary.csv")
    print("\n综合统计结果:")
    print(summary_df.to_string(index=False))
    
    return summary_df

def create_comparison_analysis(overall_results, hfnef_results, hfsnef_results):
    """创建比较分析"""
    
    print(f"\n正在进行比较分析...")
    
    # 获取各组显著代谢物
    overall_sig = set(overall_results[overall_results['p_value'] < 0.05]['metabolite'])
    hfnef_sig = set(hfnef_results[hfnef_results['p_value'] < 0.05]['metabolite'])
    hfsnef_sig = set(hfsnef_results[hfsnef_results['p_value'] < 0.05]['metabolite'])
    
    # 交集和差集分析
    common_hf = hfnef_sig & hfsnef_sig
    hfnef_specific = hfnef_sig - hfsnef_sig
    hfsnef_specific = hfsnef_sig - hfnef_sig
    
    comparison_stats = {
        'Metric': [
            'HFnEF组显著代谢物',
            'HFsnEF组显著代谢物', 
            '两组共同显著',
            'HFnEF组特异',
            'HFsnEF组特异',
            '整体人群显著',
            '整体vs HFnEF重叠',
            '整体vs HFsnEF重叠'
        ],
        'Count': [
            len(hfnef_sig),
            len(hfsnef_sig),
            len(common_hf),
            len(hfnef_specific),
            len(hfsnef_specific),
            len(overall_sig),
            len(overall_sig & hfnef_sig),
            len(overall_sig & hfsnef_sig)
        ]
    }
    
    comparison_df = pd.DataFrame(comparison_stats)
    comparison_df.to_csv('metabolite_comparison_analysis.csv', index=False)
    
    print("比较分析结果已保存到: metabolite_comparison_analysis.csv")
    print("\n比较分析结果:")
    print(comparison_df.to_string(index=False))
    
    return comparison_df

def create_top_metabolites_summary(overall_results, hfnef_results, hfsnef_results):
    """创建各组前10个代谢物总结"""
    
    print(f"\n正在创建前10个代谢物总结...")
    
    top_metabolites = []
    
    for name, results in [("Overall", overall_results), ("HFnEF", hfnef_results), ("HFsnEF", hfsnef_results)]:
        if len(results) > 0:
            top_10 = results.head(10)
            for i, row in top_10.iterrows():
                metabolite_name = row['metabolite'].split('_')[0][:50]
                top_metabolites.append({
                    'Group': name,
                    'Rank': len(top_metabolites) - sum([1 for x in top_metabolites if x['Group'] == name]) + 1,
                    'Metabolite': metabolite_name,
                    'Full_Name': row['metabolite'],
                    'HR': round(row['hazard_ratio'], 3),
                    'CI_Lower': round(row['ci_lower'], 3),
                    'CI_Upper': round(row['ci_upper'], 3),
                    'P_Value': f"{row['p_value']:.4f}",
                    'FDR_Value': f"{row['fdr_value']:.4f}",
                    'Direction': "保护性" if row['hazard_ratio'] < 1 else "危险性",
                    'N_Samples': row['n_samples'],
                    'N_Events': row['n_events']
                })
    
    top_metabolites_df = pd.DataFrame(top_metabolites)
    top_metabolites_df.to_csv('top_metabolites_all_groups.csv', index=False)
    
    print("前10个代谢物总结已保存到: top_metabolites_all_groups.csv")
    
    return top_metabolites_df

def main():
    """主函数"""
    
    print("=" * 80)
    print("全面Cox回归分析 - 整体人群和分组分析")
    print("=" * 80)
    
    # 1. 加载数据
    df, metabolite_cols = load_and_prepare_data()
    
    # 2. 整体人群分析
    print("\n" + "="*50)
    print("1. 整体人群Cox回归分析")
    print("="*50)
    overall_results = perform_cox_analysis(df, metabolite_cols, "Overall")
    
    # 3. HFnEF组分析
    print("\n" + "="*50)
    print("2. HFnEF组Cox回归分析")
    print("="*50)
    hfnef_filter = df['HFsnEF'] == 0
    hfnef_results = perform_cox_analysis(df, metabolite_cols, "HFnEF", hfnef_filter)
    
    # 4. HFsnEF组分析
    print("\n" + "="*50)
    print("3. HFsnEF组Cox回归分析")
    print("="*50)
    hfsnef_filter = df['HFsnEF'] == 1
    hfsnef_results = perform_cox_analysis(df, metabolite_cols, "HFsnEF", hfsnef_filter)
    
    # 5. 创建综合分析
    print("\n" + "="*50)
    print("4. 综合分析和比较")
    print("="*50)
    
    if len(overall_results) > 0 or len(hfnef_results) > 0 or len(hfsnef_results) > 0:
        # 综合统计
        summary_df = create_comprehensive_summary(overall_results, hfnef_results, hfsnef_results)
        
        # 比较分析
        comparison_df = create_comparison_analysis(overall_results, hfnef_results, hfsnef_results)
        
        # 前10个代谢物总结
        top_metabolites_df = create_top_metabolites_summary(overall_results, hfnef_results, hfsnef_results)
        
        print(f"\n" + "="*80)
        print("分析完成！生成的文件:")
        print("• overall_cox_comprehensive_results.csv - 整体人群完整结果")
        print("• hfnef_cox_comprehensive_results.csv - HFnEF组完整结果")
        print("• hfsnef_cox_comprehensive_results.csv - HFsnEF组完整结果")
        print("• comprehensive_cox_summary.csv - 综合统计总结")
        print("• metabolite_comparison_analysis.csv - 比较分析结果")
        print("• top_metabolites_all_groups.csv - 各组前10个代谢物")
        print("="*80)
    
    else:
        print("所有分析均未成功完成")

if __name__ == "__main__":
    main()
