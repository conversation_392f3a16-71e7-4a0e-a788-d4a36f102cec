import pandas as pd

# 文件路径
HFNEF_RESULT_FILE = 'hfnef_protein_cox_results.csv'
HFSNEF_RESULT_FILE = 'hfsnef_protein_cox_results.csv'
OUTPUT_FILE = 'hfsnef_specific_significant_proteins.csv'

# p值阈值
P_VALUE_THRESHOLD = 0.05


def load_and_filter(file_path: str) -> pd.DataFrame:
    """读取Cox分析结果并按 p<阈值 过滤"""
    df = pd.read_csv(file_path)
    if 'p_value' not in df.columns:
        raise ValueError(f"文件 {file_path} 缺少 'p_value' 列")
    return df[df['p_value'] < P_VALUE_THRESHOLD]


def main():
    # 读取并过滤两组结果
    hfnef_sig = load_and_filter(HFNEF_RESULT_FILE)
    hfsnef_sig = load_and_filter(HFSNEF_RESULT_FILE)

    # 取出蛋白名集合
    hfnef_sig_proteins = set(hfnef_sig['protein'])

    # HFsnEF 组显著但在 HFnef 组不显著的蛋白
    hfsnef_specific = hfsnef_sig[~hfsnef_sig['protein'].isin(hfnef_sig_proteins)].copy()

    # 保存结果
    hfsnef_specific.to_csv(OUTPUT_FILE, index=False)
    print(f"已保存 {len(hfsnef_specific)} 个 HFsnEF 特异显著蛋白至 {OUTPUT_FILE}")

    # 显示前几行预览
    print(hfsnef_specific.head())


if __name__ == '__main__':
    main() 