import pandas as pd
import gseapy as gp
import matplotlib.pyplot as plt
from pathlib import Path
import numpy as np

"""
脚本依赖：
  pip install gseapy matplotlib

使用示例：
python enrich_module_pathways.py --module_assignment protein_module_assignment.csv --organism human

- 读取 protein_module_assignment.csv
- 针对每个模块的蛋白列表执行富集分析（GO BP 默认）
- 输出每个模块的富集结果表 module_<id>_enrichment.csv
- 生成 -log10(FDR) 条形图，可参考论文 Figure2 中 B 部分效果
"""

import argparse


def run_enrichment(genes, module_id, organism='human', outdir=Path('.')):
    enr = gp.enrichr(gene_list=genes,
                     organism=organism,
                     description=f"module_{module_id}",
                     gene_sets=['GO_Biological_Process_2021', 'KEGG_2021_Human'],
                     outdir=outdir / f"module_{module_id}_enrichr",
                     cutoff=0.05)
    # 返回显著条目
    return enr.results[enr.results['Adjusted P-value'] < 0.05]


def main():
    parser = argparse.ArgumentParser(description='模块富集分析')
    parser.add_argument('--module_assignment', default='protein_module_assignment.csv')
    parser.add_argument('--organism', default='human')
    args = parser.parse_args()

    assign_df = pd.read_csv(args.module_assignment)

    for module_id, group in assign_df.groupby('module'):
        genes = group['protein'].tolist()
        if len(genes) < 3:
            continue
        res = run_enrichment(genes, module_id, organism=args.organism)
        if res.empty:
            continue
        out_csv = Path(f'module_{module_id}_enrichment.csv')
        res.to_csv(out_csv, index=False)
        # 绘制 -log10(FDR) 图
        res_sorted = res.sort_values('Adjusted P-value').head(20)
        plt.figure(figsize=(6, 8))
        plt.barh(res_sorted['Term'], -np.log10(res_sorted['Adjusted P-value']))
        plt.xlabel('-log10(FDR)')
        plt.title(f'Module {module_id} Enrichment')
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig(f'module_{module_id}_enrichment_barplot.png')
        plt.close()


if __name__ == '__main__':
    main() 