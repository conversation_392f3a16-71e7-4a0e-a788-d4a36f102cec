#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的Cox回归火山图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.ticker import MultipleLocator, FixedLocator
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def create_optimized_volcano_plot():
    """创建优化的Cox回归火山图"""
    
    print("正在创建优化的Cox回归火山图...")
    
    # 读取数据
    cox_results = pd.read_csv('all_metabolites_cox_results.csv')
    
    # 准备数据
    plot_data = cox_results.copy()
    
    # 计算log2(HR)和-log10(P值)
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义显著性阈值
    p_threshold = 0.05
    
    # 简化分类：只分为保护性、危险性、非显著
    plot_data['significance'] = 'Non-significant'
    
    # 显著且有意义的点
    significant_protective = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] < 1)
    significant_risk = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] > 1)
    
    plot_data.loc[significant_protective, 'significance'] = 'Significant Protective'
    plot_data.loc[significant_risk, 'significance'] = 'Significant Risk'
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置背景色
    ax.set_facecolor('#fafafa')
    
    # 定义颜色 - 更专业的配色
    colors = {
        'Non-significant': '#d3d3d3',      # 浅灰色
        'Significant Protective': '#2E86AB',  # 蓝色
        'Significant Risk': '#F24236'         # 红色
    }
    
    # 绘制散点图 - 先画非显著的，再画显著的
    for category in ['Non-significant', 'Significant Protective', 'Significant Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.4 if category == 'Non-significant' else 0.8
            size = 20 if category == 'Non-significant' else 35
            zorder = 1 if category == 'Non-significant' else 3
            
            ax.scatter(plot_data.loc[mask, 'log2_hr'], 
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category], 
                      alpha=alpha,
                      s=size,
                      zorder=zorder,
                      edgecolors='white' if category != 'Non-significant' else 'none',
                      linewidth=0.5)
    
    # 添加阈值线
    ax.axhline(y=-np.log10(p_threshold), color='#666666', linestyle='--', 
               alpha=0.8, linewidth=1.5, zorder=2, label=f'P = {p_threshold}')
    ax.axvline(x=0, color='#333333', linestyle='-', alpha=0.6, linewidth=1, zorder=2)
    
    # 设置x轴刻度 - 使用对称的非等距刻度
    x_ticks = [-4, -3, -2, -1, 0, 1, 2, 3]
    ax.set_xticks(x_ticks)
    ax.set_xticklabels([f'{tick}' for tick in x_ticks])
    
    # 设置坐标轴范围
    x_min, x_max = plot_data['log2_hr'].min(), plot_data['log2_hr'].max()
    x_range = max(abs(x_min), abs(x_max)) * 1.1
    ax.set_xlim(-x_range, x_range)
    
    y_max = plot_data['neg_log10_p'].max() * 1.05
    ax.set_ylim(0, y_max)
    
    # 设置标签和标题
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=14, fontweight='bold')
    ax.set_ylabel('-log₁₀(P值)', fontsize=14, fontweight='bold')
    ax.set_title('Cox回归分析火山图 - 代谢物与非心血管死亡', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 创建自定义图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor=colors['Significant Protective'], alpha=0.8, 
              label=f'显著保护性 (n={significant_protective.sum()})'),
        Patch(facecolor=colors['Significant Risk'], alpha=0.8, 
              label=f'显著危险性 (n={significant_risk.sum()})'),
        Patch(facecolor=colors['Non-significant'], alpha=0.4, 
              label=f'非显著 (n={(plot_data["significance"] == "Non-significant").sum()})')
    ]
    
    ax.legend(handles=legend_elements, loc='upper right', 
             frameon=True, fancybox=True, shadow=True, 
             fontsize=11, title='代谢物分类', title_fontsize=12)
    
    # 添加统计信息文本框
    stats_text = f"总代谢物: {len(plot_data)}\n"
    stats_text += f"显著保护性: {significant_protective.sum()}\n"
    stats_text += f"显著危险性: {significant_risk.sum()}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', fontsize=10,
            bbox=dict(boxstyle='round,pad=0.5', facecolor='white', 
                     alpha=0.9, edgecolor='gray'))
    
    # 添加HR解释文本
    hr_text = "← 保护性 (HR < 1)     危险性 (HR > 1) →"
    ax.text(0.5, 0.02, hr_text, transform=ax.transAxes, 
            horizontalalignment='center', fontsize=10, 
            style='italic', color='#666666')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1)
    ax.spines['bottom'].set_linewidth(1)
    
    # 设置刻度参数
    ax.tick_params(axis='both', which='major', labelsize=11, 
                   length=5, width=1, direction='out')
    
    plt.tight_layout()
    plt.savefig('optimized_cox_volcano_plot.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    print("优化的Cox回归火山图已保存到: optimized_cox_volcano_plot.png")
    
    # 输出统计信息
    print(f"\n火山图统计:")
    print(f"总代谢物数量: {len(plot_data)}")
    print(f"显著保护性 (HR < 1, P < {p_threshold}): {significant_protective.sum()}")
    print(f"显著危险性 (HR > 1, P < {p_threshold}): {significant_risk.sum()}")
    print(f"非显著: {(plot_data['significance'] == 'Non-significant').sum()}")
    
    return plot_data

def create_enhanced_volcano_plot():
    """创建增强版火山图 - 更加专业的版本"""
    
    print("\n正在创建增强版Cox回归火山图...")
    
    # 读取数据
    cox_results = pd.read_csv('all_metabolites_cox_results.csv')
    
    # 准备数据
    plot_data = cox_results.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义阈值
    p_threshold = 0.05
    
    # 分类
    plot_data['significance'] = 'Non-significant'
    significant_protective = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] < 1)
    significant_risk = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] > 1)
    
    plot_data.loc[significant_protective, 'significance'] = 'Significant Protective'
    plot_data.loc[significant_risk, 'significance'] = 'Significant Risk'
    
    # 使用seaborn样式
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 更精致的配色
    colors = {
        'Non-significant': '#E8E8E8',
        'Significant Protective': '#1f77b4',  # 深蓝色
        'Significant Risk': '#d62728'         # 深红色
    }
    
    # 绘制点
    for category in ['Non-significant', 'Significant Protective', 'Significant Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.5 if category == 'Non-significant' else 0.8
            size = 15 if category == 'Non-significant' else 30
            
            ax.scatter(plot_data.loc[mask, 'log2_hr'], 
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category], 
                      alpha=alpha,
                      s=size,
                      edgecolors='white',
                      linewidth=0.3)
    
    # 添加阈值线
    ax.axhline(y=-np.log10(p_threshold), color='gray', linestyle='--', 
               alpha=0.7, linewidth=1.2)
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5, linewidth=0.8)
    
    # 设置坐标轴
    x_range = max(abs(plot_data['log2_hr'].min()), abs(plot_data['log2_hr'].max())) * 1.1
    ax.set_xlim(-x_range, x_range)
    ax.set_ylim(0, plot_data['neg_log10_p'].max() * 1.05)
    
    # 标签
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=12, fontweight='bold')
    ax.set_ylabel('-log₁₀(P值)', fontsize=12, fontweight='bold')
    ax.set_title('代谢物与非心血管死亡的Cox回归分析', fontsize=14, fontweight='bold')
    
    # 简洁的图例
    legend_labels = [
        f'保护性 (n={significant_protective.sum()})',
        f'危险性 (n={significant_risk.sum()})',
        f'非显著 (n={(plot_data["significance"] == "Non-significant").sum()})'
    ]
    
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor=colors['Significant Protective'], alpha=0.8),
        Patch(facecolor=colors['Significant Risk'], alpha=0.8),
        Patch(facecolor=colors['Non-significant'], alpha=0.5)
    ]
    
    ax.legend(legend_elements, legend_labels, loc='upper right', frameon=True)
    
    plt.tight_layout()
    plt.savefig('enhanced_cox_volcano_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("增强版Cox回归火山图已保存到: enhanced_cox_volcano_plot.png")

def main():
    """主函数"""
    # 创建优化版火山图
    plot_data = create_optimized_volcano_plot()
    
    # 创建增强版火山图
    create_enhanced_volcano_plot()
    
    print(f"\n已生成两个版本的优化火山图:")
    print(f"• optimized_cox_volcano_plot.png - 详细版本")
    print(f"• enhanced_cox_volcano_plot.png - 简洁版本")

if __name__ == "__main__":
    main()
