#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终分析总结报告生成脚本

作者: AI Assistant
日期: 2025-07-30
描述: 整合所有Cox分析结果，生成最终总结报告
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import glob

def find_latest_univariate_results():
    """查找最新的单因素Cox分析结果文件"""
    pattern = "cox_analysis_results_*.csv"
    files = glob.glob(pattern)
    if not files:
        raise FileNotFoundError("未找到单因素Cox分析结果文件")
    return max(files, key=os.path.getmtime)

def parse_hr_ci(hr_ci_str):
    """解析HR_95CI字符串"""
    try:
        hr_ci_str = str(hr_ci_str).strip()
        parts = hr_ci_str.split(' (')
        hr = float(parts[0])
        ci_part = parts[1].rstrip(')')
        ci_bounds = ci_part.split('-')
        hr_lower = float(ci_bounds[0])
        hr_upper = float(ci_bounds[1])
        return hr, hr_lower, hr_upper
    except:
        return np.nan, np.nan, np.nan

def load_and_process_results():
    """加载并处理分析结果"""
    print("正在加载分析结果...")
    
    # 加载单因素分析结果
    results_file = find_latest_univariate_results()
    results = pd.read_csv(results_file)
    
    # 解析HR和置信区间
    hr_data = results['HR_95CI'].apply(parse_hr_ci)
    results['HR_parsed'] = [x[0] for x in hr_data]
    results['HR_95CI_Lower'] = [x[1] for x in hr_data]
    results['HR_95CI_Upper'] = [x[2] for x in hr_data]
    results['HR'] = results['HR'].fillna(results['HR_parsed'])
    
    # 排除指定变量
    exclude_vars = [
        'Death_in_hospital',
        'Inhospital_vasoactive_drug', 
        'Inhospital_mehanical_ventilation',
        'AR'
    ]
    
    results = results[~results['Variable'].isin(exclude_vars)]
    results = results[results['Status'] == '成功']
    results = results.dropna(subset=['HR', 'HR_95CI_Lower', 'HR_95CI_Upper'])
    
    return results, exclude_vars

def generate_final_summary():
    """生成最终总结报告"""
    results, exclude_vars = load_and_process_results()
    
    # 分离变量类型
    clinical_vars = results[results['Variable_Type'] == '临床']
    metabolite_vars = results[results['Variable_Type'] == '代谢物']
    
    # 显著变量
    significant_vars = results[results['P_value'] < 0.05].sort_values('P_value')
    clinical_sig = clinical_vars[clinical_vars['P_value'] < 0.05]
    metabolite_sig = metabolite_vars[metabolite_vars['P_value'] < 0.05]
    
    # 保护性因子和危险因子
    protective_factors = significant_vars[significant_vars['HR'] < 1]
    risk_factors = significant_vars[significant_vars['HR'] > 1]
    
    # 生成报告
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    report_lines = []
    
    report_lines.append("=" * 100)
    report_lines.append("非心血管死亡差异代谢物和临床变量分析 - 最终总结报告")
    report_lines.append("=" * 100)
    report_lines.append(f"报告生成时间：{timestamp}")
    report_lines.append("")
    
    # 研究背景
    report_lines.append("【研究背景】")
    report_lines.append("本研究针对合并数据集merged_metabolomics_clinical_final.csv，分析代谢物和临床变量")
    report_lines.append("与非心血管死亡的关联性，采用单因素Cox比例风险回归模型进行分析。")
    report_lines.append("")
    
    # 方法学
    report_lines.append("【方法学】")
    report_lines.append("1. 研究设计：回顾性队列研究")
    report_lines.append("2. 统计方法：单因素Cox比例风险回归")
    report_lines.append("3. 结局变量：非心血管死亡 (NonCV_death)")
    report_lines.append("4. 时间变量：总生存时间 (OS)")
    report_lines.append("5. 排除变量：院内治疗相关变量以避免混杂")
    for var in exclude_vars:
        report_lines.append(f"   - {var}")
    report_lines.append("")
    
    # 基线特征
    report_lines.append("【基线特征】")
    report_lines.append(f"总样本数：202例")
    report_lines.append(f"非心血管死亡事件：15例 (7.4%)")
    report_lines.append(f"随访时间范围：6-810天")
    report_lines.append("")
    
    # 变量统计
    report_lines.append("【变量统计】")
    report_lines.append(f"总分析变量：{len(results)} 个")
    report_lines.append(f"  - 临床变量：{len(clinical_vars)} 个")
    report_lines.append(f"  - 代谢物变量：{len(metabolite_vars)} 个")
    report_lines.append("")
    report_lines.append(f"显著相关变量 (p<0.05)：{len(significant_vars)} 个 ({len(significant_vars)/len(results)*100:.1f}%)")
    report_lines.append(f"  - 临床变量：{len(clinical_sig)} 个 ({len(clinical_sig)/len(clinical_vars)*100:.1f}%)")
    report_lines.append(f"  - 代谢物变量：{len(metabolite_sig)} 个 ({len(metabolite_sig)/len(metabolite_vars)*100:.1f}%)")
    report_lines.append("")
    
    # 主要发现
    report_lines.append("【主要发现】")
    report_lines.append("")
    
    # 最显著的危险因子
    top_risk = risk_factors.head(10)
    if len(top_risk) > 0:
        report_lines.append("1. 主要危险因子 (HR > 1)：")
        for i, (_, row) in enumerate(top_risk.iterrows(), 1):
            var_name = row['Variable']
            var_type = row['Variable_Type']
            hr = row['HR']
            hr_lower = row['HR_95CI_Lower']
            hr_upper = row['HR_95CI_Upper']
            p_val = row['P_value']
            
            if p_val < 0.001:
                p_str = "p<0.001"
            else:
                p_str = f"p={p_val:.3f}"
            
            report_lines.append(f"   {i:2d}. {var_name} ({var_type})")
            report_lines.append(f"       HR = {hr:.3f} (95%CI: {hr_lower:.3f}-{hr_upper:.3f}), {p_str}")
        report_lines.append("")
    
    # 保护性因子
    top_protective = protective_factors.head(10)
    if len(top_protective) > 0:
        report_lines.append("2. 主要保护性因子 (HR < 1)：")
        for i, (_, row) in enumerate(top_protective.iterrows(), 1):
            var_name = row['Variable']
            var_type = row['Variable_Type']
            hr = row['HR']
            hr_lower = row['HR_95CI_Lower']
            hr_upper = row['HR_95CI_Upper']
            p_val = row['P_value']
            
            if p_val < 0.001:
                p_str = "p<0.001"
            else:
                p_str = f"p={p_val:.3f}"
            
            report_lines.append(f"   {i:2d}. {var_name} ({var_type})")
            report_lines.append(f"       HR = {hr:.3f} (95%CI: {hr_lower:.3f}-{hr_upper:.3f}), {p_str}")
        report_lines.append("")
    
    # 按显著性水平分类
    report_lines.append("3. 按显著性水平分类：")
    highly_sig = len(significant_vars[significant_vars['P_value'] < 0.001])
    moderate_sig = len(significant_vars[(significant_vars['P_value'] >= 0.001) & (significant_vars['P_value'] < 0.01)])
    mild_sig = len(significant_vars[(significant_vars['P_value'] >= 0.01) & (significant_vars['P_value'] < 0.05)])
    
    report_lines.append(f"   - 极显著 (p<0.001)：{highly_sig} 个变量")
    report_lines.append(f"   - 高度显著 (0.001≤p<0.01)：{moderate_sig} 个变量")
    report_lines.append(f"   - 显著 (0.01≤p<0.05)：{mild_sig} 个变量")
    report_lines.append("")
    
    # 临床意义解读
    report_lines.append("【临床意义解读】")
    report_lines.append("")
    
    # 临床变量解读
    if len(clinical_sig) > 0:
        report_lines.append("1. 临床变量：")
        top_clinical = clinical_sig.head(5)
        for _, row in top_clinical.iterrows():
            var_name = row['Variable']
            hr = row['HR']
            
            if var_name == 'Malignancy':
                report_lines.append(f"   - 恶性肿瘤：使非心血管死亡风险增加{(hr-1)*100:.0f}%，是最强的独立危险因子")
            elif var_name == 'Alb':
                report_lines.append(f"   - 白蛋白：每增加1g/L，非心血管死亡风险降低{(1-hr)*100:.1f}%")
            elif var_name == 'Infection_admit':
                report_lines.append(f"   - 入院感染：使非心血管死亡风险增加{(hr-1)*100:.0f}%")
            elif var_name == 'NT_proBNPx':
                report_lines.append(f"   - NT-proBNP：心功能标志物，与死亡风险显著相关")
            else:
                if hr > 1:
                    report_lines.append(f"   - {var_name}：危险因子 (HR={hr:.3f})")
                else:
                    report_lines.append(f"   - {var_name}：保护性因子 (HR={hr:.3f})")
        report_lines.append("")
    
    # 代谢物解读
    if len(metabolite_sig) > 0:
        report_lines.append("2. 代谢物变量：")
        report_lines.append("   发现多个代谢物与非心血管死亡显著相关，主要涉及：")
        
        # 分析代谢物类型
        metabolite_names = metabolite_sig['Variable'].tolist()
        
        if any('tryptophan' in name.lower() for name in metabolite_names):
            report_lines.append("   - 色氨酸代谢通路相关化合物")
        if any('morphine' in name.lower() for name in metabolite_names):
            report_lines.append("   - 吗啡类化合物")
        if any('acid' in name.lower() for name in metabolite_names):
            report_lines.append("   - 有机酸类化合物")
        
        report_lines.append(f"   共识别出{len(metabolite_sig)}个显著相关的代谢物")
        report_lines.append("")
    
    # 多因素分析结果
    report_lines.append("【多因素分析结果】")
    report_lines.append("由于样本量相对较小（202例）和事件数较少（15例），多因素Cox回归分析")
    report_lines.append("出现过拟合现象，未能识别出独立的预测因子。建议：")
    report_lines.append("1. 增加样本量以提高统计效力")
    report_lines.append("2. 采用变量筛选方法减少纳入变量数")
    report_lines.append("3. 考虑使用正则化回归方法")
    report_lines.append("")
    
    # 研究局限性
    report_lines.append("【研究局限性】")
    report_lines.append("1. 样本量相对较小，可能影响统计效力")
    report_lines.append("2. 事件数较少，限制了多因素分析的可行性")
    report_lines.append("3. 单中心研究，外推性有限")
    report_lines.append("4. 缺失值处理可能影响结果")
    report_lines.append("")
    
    # 结论
    report_lines.append("【结论】")
    report_lines.append("1. 恶性肿瘤是非心血管死亡的最强危险因子")
    report_lines.append("2. 营养状态（白蛋白水平）对预后具有重要保护作用")
    report_lines.append("3. 多个代谢物显示出潜在的预测价值，特别是色氨酸代谢相关化合物")
    report_lines.append("4. 感染控制在住院患者管理中至关重要")
    report_lines.append("5. 需要更大样本量的研究来验证这些发现")
    report_lines.append("")
    
    # 临床建议
    report_lines.append("【临床建议】")
    report_lines.append("1. 对恶性肿瘤患者加强非心血管死亡风险评估")
    report_lines.append("2. 重视营养支持，维持适当的白蛋白水平")
    report_lines.append("3. 积极预防和治疗感染")
    report_lines.append("4. 考虑将显著相关的代谢物纳入风险评估模型")
    report_lines.append("5. 开展前瞻性研究验证预测模型")
    report_lines.append("")
    
    # 生成的文件列表
    report_lines.append("【生成的分析文件】")
    report_lines.append("1. cox_analysis_results_*.csv - 完整单因素分析结果")
    report_lines.append("2. clinical_variables_forest_plot_*.png - 临床变量森林图")
    report_lines.append("3. significant_metabolites_forest_plot_*.png - 显著代谢物森林图")
    report_lines.append("4. top_metabolites_forest_plot_*.png - 前40个代谢物森林图")
    report_lines.append("5. univariate_forest_summary_*.txt - 森林图摘要报告")
    report_lines.append("6. multivariate_cox_results_*.csv - 多因素分析结果")
    report_lines.append("7. stepwise_cox_results_*.csv - 逐步回归分析结果")
    report_lines.append("")
    
    report_lines.append("=" * 100)
    report_lines.append("报告完成")
    report_lines.append("=" * 100)
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    try:
        print("正在生成最终分析总结报告...")
        
        # 生成报告
        report_content = generate_final_summary()
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"final_analysis_summary_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 输出到控制台
        print(report_content)
        print(f"\n最终分析总结报告已保存到：{report_file}")
        
    except Exception as e:
        print(f"生成报告时发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
