#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非心血管死亡差异代谢物和临床变量的单因素Cox比例风险分析脚本

作者: AI Assistant
日期: 2025-07-30
描述: 针对合并数据集merged_metabolomics_clinical_final.csv，根据是否发生非心血管死亡，
      寻找差异代谢物和临床变量，进行单因素cox比例分析，并导出结果。
"""

import pandas as pd
import numpy as np
from lifelines import CoxPHFitter
import warnings
import os
from datetime import datetime

# 忽略警告信息
warnings.filterwarnings('ignore')

def load_and_preprocess_data(file_path):
    """
    加载和预处理数据
    
    Parameters:
    file_path (str): 数据文件路径
    
    Returns:
    pd.DataFrame: 预处理后的数据
    """
    print("正在加载数据...")
    
    # 读取数据
    try:
        data = pd.read_csv(file_path, encoding='utf-8')
    except UnicodeDecodeError:
        data = pd.read_csv(file_path, encoding='gbk')
    
    print(f"数据加载完成，共有 {data.shape[0]} 行，{data.shape[1]} 列")
    
    # 检查关键变量是否存在
    if 'NonCV_death' not in data.columns:
        raise ValueError("数据中未找到 'NonCV_death' 列")
    if 'OS' not in data.columns:
        raise ValueError("数据中未找到 'OS' 列")
    
    # 处理生存时间和事件变量
    # 首先处理NonCV_death列的缺失值
    data['event'] = pd.to_numeric(data['NonCV_death'], errors='coerce')
    data['duration'] = pd.to_numeric(data['OS'], errors='coerce')

    # 移除关键变量缺失的记录
    initial_count = len(data)
    data = data.dropna(subset=['event', 'duration'])
    data = data[data['duration'] > 0]

    # 确保event列为整数类型
    data['event'] = data['event'].astype(int)

    removed_count = initial_count - len(data)
    
    if removed_count > 0:
        print(f"移除了 {removed_count} 条生存时间缺失或无效的记录")
    
    print(f"预处理后数据：{data.shape[0]} 行，{data.shape[1]} 列")
    print(f"非心血管死亡事件数：{data['event'].sum()}")
    print(f"生存时间范围：{data['duration'].min():.2f} - {data['duration'].max():.2f}")
    
    return data

def identify_variables(data):
    """
    识别代谢物变量和临床变量
    
    Parameters:
    data (pd.DataFrame): 数据框
    
    Returns:
    tuple: (代谢物变量列表, 临床变量列表)
    """
    print("正在识别变量类型...")
    
    # 代谢物变量（以CPD_开头的列）
    metabolite_vars = [col for col in data.columns if col.startswith('CPD_')]
    
    # 排除的列（ID、事件、时间等）
    exclude_cols = [
        'Record_ID', 'NonCV_death', 'OS', 'event', 'duration',
        'IN_DATE', 'OUT_DATE', 'Birthday',  # 日期列
        'Alive', 'CV death', 'HF_hospitalization', 'HF hospitalization',  # 其他结局变量
        'Time to first HF hospitalization', 'Compostite endpoint of CV death or HF hospitalization',
        'All_cause_hospitalization', 'Time_to first_all_casue_hospitalization'
    ]
    
    # 临床变量（除了代谢物和排除列之外的所有数值列）
    clinical_vars = []
    for col in data.columns:
        if (col not in metabolite_vars and 
            col not in exclude_cols and
            col not in data.select_dtypes(include=['object']).columns):
            # 检查是否为数值型变量
            try:
                pd.to_numeric(data[col], errors='coerce')
                clinical_vars.append(col)
            except:
                continue
    
    print(f"识别到 {len(metabolite_vars)} 个代谢物变量")
    print(f"识别到 {len(clinical_vars)} 个临床变量")
    
    return metabolite_vars, clinical_vars

def perform_univariate_cox_analysis(data, variables, var_type):
    """
    执行单因素Cox回归分析
    
    Parameters:
    data (pd.DataFrame): 数据框
    variables (list): 变量列表
    var_type (str): 变量类型（'代谢物' 或 '临床'）
    
    Returns:
    pd.DataFrame: 分析结果
    """
    print(f"正在进行{var_type}变量的单因素Cox回归分析...")
    
    results = []
    
    for i, var in enumerate(variables):
        if (i + 1) % 50 == 0:
            print(f"已完成 {i + 1}/{len(variables)} 个{var_type}变量的分析")
        
        try:
            # 准备分析数据
            analysis_data = data[['duration', 'event', var]].copy()
            
            # 移除缺失值
            analysis_data = analysis_data.dropna()
            
            # 检查是否有足够的数据
            if len(analysis_data) < 10:
                results.append({
                    'Variable': var,
                    'Variable_Type': var_type,
                    'N': len(analysis_data),
                    'Events': np.nan,
                    'HR': np.nan,
                    'HR_95CI_Lower': np.nan,
                    'HR_95CI_Upper': np.nan,
                    'P_value': np.nan,
                    'Status': '样本量不足'
                })
                continue
            
            # 检查变量的变异性
            if analysis_data[var].nunique() < 2:
                results.append({
                    'Variable': var,
                    'Variable_Type': var_type,
                    'N': len(analysis_data),
                    'Events': analysis_data['event'].sum(),
                    'HR': np.nan,
                    'HR_95CI_Lower': np.nan,
                    'HR_95CI_Upper': np.nan,
                    'P_value': np.nan,
                    'Status': '变量无变异'
                })
                continue
            
            # 执行Cox回归
            cph = CoxPHFitter()
            cph.fit(analysis_data, duration_col='duration', event_col='event')
            
            # 提取结果
            summary = cph.summary
            hr = summary.loc[var, 'exp(coef)']
            hr_lower = summary.loc[var, 'exp(coef) lower 95%']
            hr_upper = summary.loc[var, 'exp(coef) upper 95%']
            p_value = summary.loc[var, 'p']
            
            results.append({
                'Variable': var,
                'Variable_Type': var_type,
                'N': len(analysis_data),
                'Events': analysis_data['event'].sum(),
                'HR': hr,
                'HR_95CI_Lower': hr_lower,
                'HR_95CI_Upper': hr_upper,
                'P_value': p_value,
                'Status': '成功'
            })
            
        except Exception as e:
            results.append({
                'Variable': var,
                'Variable_Type': var_type,
                'N': len(analysis_data) if 'analysis_data' in locals() else 0,
                'Events': np.nan,
                'HR': np.nan,
                'HR_95CI_Lower': np.nan,
                'HR_95CI_Upper': np.nan,
                'P_value': np.nan,
                'Status': f'分析失败: {str(e)[:50]}'
            })
    
    print(f"{var_type}变量分析完成")
    return pd.DataFrame(results)

def format_results(results_df):
    """
    格式化分析结果
    
    Parameters:
    results_df (pd.DataFrame): 原始结果
    
    Returns:
    pd.DataFrame: 格式化后的结果
    """
    print("正在格式化结果...")
    
    # 创建格式化的结果
    formatted_results = results_df.copy()
    
    # 格式化HR和置信区间
    def format_hr_ci(row):
        if pd.isna(row['HR']):
            return 'NA'
        else:
            return f"{row['HR']:.3f} ({row['HR_95CI_Lower']:.3f}-{row['HR_95CI_Upper']:.3f})"
    
    formatted_results['HR_95CI'] = formatted_results.apply(format_hr_ci, axis=1)
    
    # 格式化p值
    def format_pvalue(p):
        if pd.isna(p):
            return 'NA'
        elif p < 0.001:
            return '<0.001'
        else:
            return f"{p:.3f}"
    
    formatted_results['P_value_formatted'] = formatted_results['P_value'].apply(format_pvalue)
    
    # 添加显著性标记
    def significance_mark(p):
        if pd.isna(p):
            return ''
        elif p < 0.001:
            return '***'
        elif p < 0.01:
            return '**'
        elif p < 0.05:
            return '*'
        else:
            return ''
    
    formatted_results['Significance'] = formatted_results['P_value'].apply(significance_mark)
    
    # 重新排列列
    final_columns = [
        'Variable', 'Variable_Type', 'N', 'Events', 
        'HR', 'HR_95CI', 'P_value', 'P_value_formatted', 
        'Significance', 'Status'
    ]
    
    formatted_results = formatted_results[final_columns]
    
    # 按p值排序（显著的在前）
    formatted_results = formatted_results.sort_values('P_value', na_position='last')
    
    return formatted_results

def main():
    """
    主函数
    """
    print("=" * 60)
    print("非心血管死亡差异代谢物和临床变量的单因素Cox分析")
    print("=" * 60)
    
    # 数据文件路径
    data_file = "merged_metabolomics_clinical_final.csv"
    
    # 检查文件是否存在
    if not os.path.exists(data_file):
        print(f"错误：找不到数据文件 {data_file}")
        return
    
    try:
        # 1. 加载和预处理数据
        data = load_and_preprocess_data(data_file)
        
        # 2. 识别变量
        metabolite_vars, clinical_vars = identify_variables(data)
        
        # 3. 执行单因素Cox分析
        print("\n开始Cox回归分析...")
        
        # 分析代谢物变量
        metabolite_results = perform_univariate_cox_analysis(data, metabolite_vars, '代谢物')
        
        # 分析临床变量
        clinical_results = perform_univariate_cox_analysis(data, clinical_vars, '临床')
        
        # 4. 合并结果
        all_results = pd.concat([metabolite_results, clinical_results], ignore_index=True)
        
        # 5. 格式化结果
        formatted_results = format_results(all_results)
        
        # 6. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"cox_analysis_results_{timestamp}.csv"
        
        formatted_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 7. 输出摘要
        print("\n" + "=" * 60)
        print("分析完成！结果摘要：")
        print("=" * 60)
        
        total_vars = len(formatted_results)
        successful_analyses = len(formatted_results[formatted_results['Status'] == '成功'])
        significant_vars = len(formatted_results[
            (formatted_results['P_value'] < 0.05) & 
            (formatted_results['Status'] == '成功')
        ])
        
        print(f"总变量数：{total_vars}")
        print(f"成功分析的变量数：{successful_analyses}")
        print(f"显著相关的变量数（p<0.05）：{significant_vars}")
        
        if significant_vars > 0:
            print(f"\n前10个最显著的变量：")
            top_significant = formatted_results[
                (formatted_results['P_value'] < 0.05) & 
                (formatted_results['Status'] == '成功')
            ].head(10)
            
            for _, row in top_significant.iterrows():
                print(f"  {row['Variable']} ({row['Variable_Type']}): "
                      f"HR={row['HR']:.3f}, p={row['P_value_formatted']}")
        
        print(f"\n详细结果已保存到：{output_file}")
        
    except Exception as e:
        print(f"分析过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
