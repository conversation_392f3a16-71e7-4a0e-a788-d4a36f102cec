#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山图和森林图绘制
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_volcano_plot(data, title, filename, group_name="Overall"):
    """创建火山图"""
    
    print(f"正在创建{title}...")
    
    # 准备数据
    plot_data = data.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    plot_data['neg_log10_fdr'] = -np.log10(plot_data['fdr_value'])
    
    # 定义阈值
    p_threshold = 0.05
    fdr_threshold = 0.05
    
    # 分类点 - 简化为三类
    plot_data['significance'] = 'Non-significant'

    # P值显著
    p_significant = plot_data['p_value'] < p_threshold
    # FDR显著
    fdr_significant = plot_data['fdr_value'] < fdr_threshold

    # 简化分类：只分为保护性、危险性、非显著
    sig_protective = p_significant & (plot_data['hazard_ratio'] < 1)
    sig_risk = p_significant & (plot_data['hazard_ratio'] > 1)

    plot_data.loc[sig_protective, 'significance'] = 'Significant_Protective'
    plot_data.loc[sig_risk, 'significance'] = 'Significant_Risk'
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    ax.set_facecolor('white')
    
    # 定义颜色和大小 - 简化
    colors = {
        'Non-significant': '#CCCCCC',        # 灰色
        'Significant_Protective': '#1E88E5', # 蓝色
        'Significant_Risk': '#D32F2F'        # 红色
    }

    sizes = {
        'Non-significant': 35,  # 增大非显著点
        'Significant_Protective': 55,  # 增大保护性点
        'Significant_Risk': 55  # 增大危险性点
    }
    
    # 绘制散点图
    for category in ['Non-significant', 'Significant_Protective', 'Significant_Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.6 if category == 'Non-significant' else 0.8
            zorder = 1 if category == 'Non-significant' else 3

            ax.scatter(plot_data.loc[mask, 'log2_hr'],
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category],
                      alpha=alpha,
                      s=sizes[category],
                      zorder=zorder,
                      edgecolors='white' if category != 'Non-significant' else 'none',
                      linewidth=0.5)
    
    # 添加阈值线 - 修正FDR线位置
    ax.axhline(y=-np.log10(p_threshold), color='black', linestyle='--',
               alpha=0.8, linewidth=1.5, zorder=2, label='P = 0.05')

    # FDR线应该在更高的位置（更严格的阈值对应更高的-log10值）
    fdr_y_position = max(-np.log10(fdr_threshold), -np.log10(p_threshold) + 0.5)
    ax.axhline(y=fdr_y_position, color='red', linestyle='--',
               alpha=0.8, linewidth=1.5, zorder=2, label='FDR = 0.05')
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.6, linewidth=1, zorder=2)
    
    # 设置坐标轴范围 - 缩窄两侧空白
    ax.set_xlim(-4.2, 4.2)
    
    y_max = plot_data['neg_log10_p'].max() * 1.05
    ax.set_ylim(0, max(y_max, 3))
    
    # 设置刻度
    ax.set_xticks([-4, -2, 0, 2, 4])
    ax.set_xticklabels(['-4', '-2', '0', '2', '4'], fontsize=11)
    ax.tick_params(axis='y', labelsize=11)
    
    # 设置标签和标题
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=12, fontweight='bold')
    ax.set_ylabel('-log₁₀(P value)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=15)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 添加标注
    ax.text(-3.8, -np.log10(p_threshold) + 0.1, 'P = 0.05',
            fontsize=10, color='black', fontweight='bold')
    ax.text(-3.8, fdr_y_position + 0.1, 'FDR = 0.05',
            fontsize=10, color='red', fontweight='bold')
    
    # 添加保护性/危险性标注
    ax.text(-3.8, 0.2, 'Protective', fontsize=11, color='#1E88E5', 
            fontweight='bold', ha='left')
    ax.text(3.5, 0.2, 'Risk', fontsize=11, color='#D32F2F', 
            fontweight='bold', ha='right')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)
    
    # 设置刻度参数
    ax.tick_params(axis='both', which='major', labelsize=11, 
                   length=6, width=1.5, direction='out')
    
    # 添加统计信息
    stats_text = f"Total: {len(plot_data)}\n"
    stats_text += f"P < 0.05: {p_significant.sum()}\n"
    stats_text += f"FDR < 0.05: {fdr_significant.sum()}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', fontsize=9,
            bbox=dict(boxstyle='round,pad=0.4', facecolor='white', 
                     alpha=0.9, edgecolor='gray'))
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def create_forest_plot(data, title, filename, top_n=10):
    """创建森林图"""
    
    print(f"正在创建{title}...")
    
    # 选择前N个最显著的结果
    plot_data = data.head(top_n).copy()
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, max(8, len(plot_data) * 0.8)))
    
    # 设置y轴位置
    y_positions = range(len(plot_data))
    
    # 绘制森林图
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i  # 倒序排列，最显著的在顶部
        
        # 绘制置信区间线 - 黑色
        ax.plot([row['ci_lower'], row['ci_upper']], [y, y], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制置信区间端点 - 黑色
        ax.plot([row['ci_lower'], row['ci_lower']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        ax.plot([row['ci_upper'], row['ci_upper']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制HR点 - 蓝色方块
        ax.scatter(row['hazard_ratio'], y, s=150, color='#1E88E5', alpha=0.8, 
                  zorder=3, marker='s', edgecolors='black', linewidth=1)
    
    # 添加HR=1的参考线 - 虚线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.8, linewidth=2, zorder=1)
    
    # 设置y轴标签 - 代谢物名称
    metabolite_names = []
    for _, row in plot_data.iterrows():
        name = row['metabolite'].split('_')[0]
        # 截断过长的名称
        if len(name) > 45:
            name = name[:42] + '...'
        metabolite_names.append(name)
    
    ax.set_yticks(range(len(plot_data)))
    ax.set_yticklabels(reversed(metabolite_names), fontsize=10)
    
    # 设置x轴 - 使用对数刻度
    ax.set_xscale('log')

    # 设置x轴范围
    all_values = list(plot_data['hazard_ratio']) + list(plot_data['ci_lower']) + list(plot_data['ci_upper'])
    x_min = max(0.01, min(all_values) * 0.5)
    x_max = max(all_values) * 2
    ax.set_xlim(x_min, x_max)

    # 设置x轴刻度为小数和整数，不用指数
    import matplotlib.ticker as ticker

    def format_hr_axis(x, p):
        if x >= 100:
            return f'{x:.0f}'
        elif x >= 10:
            return f'{x:.0f}'
        elif x >= 1:
            return f'{x:.0f}'
        elif x >= 0.1:
            return f'{x:.1f}'
        else:
            return f'{x:.2f}'

    ax.xaxis.set_major_formatter(ticker.FuncFormatter(format_hr_axis))

    # 设置合理的刻度位置
    if x_max > 100:
        ticks = [0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50, 100, 200, 500]
    elif x_max > 10:
        ticks = [0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50]
    else:
        ticks = [0.1, 0.2, 0.5, 1, 2, 5]

    # 只保留在范围内的刻度
    ticks = [t for t in ticks if x_min <= t <= x_max]
    ax.set_xticks(ticks)
    
    # 设置x轴标签
    ax.set_xlabel('Hazard Ratio (95% CI)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    # 在右侧添加HR值、置信区间和P值
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i
        
        # HR和置信区间文本
        hr_text = f"{row['hazard_ratio']:.3f}"
        ci_text = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
        p_text = f"P={row['p_value']:.4f}"
        fdr_text = f"FDR={row['fdr_value']:.4f}"
        
        # 在图的右侧添加文本
        ax.text(x_max * 0.95, y + 0.1, f"{hr_text} {ci_text}", 
                ha='right', va='center', fontsize=9, fontweight='bold')
        ax.text(x_max * 0.95, y - 0.1, f"{p_text}, {fdr_text}", 
                ha='right', va='center', fontsize=8, style='italic', color='gray')
    
    # 添加保护性/危险性标注
    ax.text(x_min * 1.1, len(plot_data) + 0.5, 'Protective', 
            fontsize=11, color='#1E88E5', fontweight='bold')
    ax.text(x_max * 0.9, len(plot_data) + 0.5, 'Risk', 
            fontsize=11, color='#D32F2F', fontweight='bold')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_linewidth(1.5)
    ax.spines['left'].set_linewidth(1.5)
    
    # 调整布局
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def main():
    """主函数"""
    
    print("=" * 80)
    print("火山图和森林图绘制")
    print("=" * 80)
    
    # 1. 整体人群分析
    print("\n1. 整体人群分析")
    print("-" * 50)
    
    overall_data = pd.read_csv('overall_cox_comprehensive_results.csv')
    
    # 整体人群火山图
    create_volcano_plot(overall_data, 
                       'Overall Population - Cox Regression Volcano Plot',
                       'overall_volcano_plot_comprehensive.png', 
                       'Overall')
    
    # 整体人群森林图 - 前10个最显著的
    overall_significant = overall_data.sort_values('p_value')
    create_forest_plot(overall_significant, 
                      'Top 10 Most Significant Metabolites - Overall Population',
                      'overall_forest_plot_top10.png', 10)
    
    # 2. HFnEF组分析
    print("\n2. HFnEF组分析")
    print("-" * 50)
    
    try:
        hfnef_data = pd.read_csv('hfnef_cox_comprehensive_results.csv')
        
        # HFnEF组火山图
        create_volcano_plot(hfnef_data, 
                           'HFnEF Group - Cox Regression Volcano Plot',
                           'hfnef_volcano_plot_comprehensive.png', 
                           'HFnEF')
        
        print(f"HFnEF组分析完成")
        
    except FileNotFoundError:
        print("HFnEF组数据文件未找到")
    
    # 3. HFsnEF组分析
    print("\n3. HFsnEF组分析")
    print("-" * 50)
    
    try:
        hfsnef_data = pd.read_csv('hfsnef_cox_comprehensive_results.csv')
        
        # HFsnEF组火山图
        create_volcano_plot(hfsnef_data, 
                           'HFsnEF Group - Cox Regression Volcano Plot',
                           'hfsnef_volcano_plot_comprehensive.png', 
                           'HFsnEF')
        
        # HFsnEF组森林图 - 前3个最显著的代谢物
        hfsnef_significant = hfsnef_data.sort_values('p_value')

        create_forest_plot(hfsnef_significant,
                          'Top 3 Most Significant Metabolites - HFsnEF Group',
                          'hfsnef_forest_plot_top3.png', 3)
        
        print(f"HFsnEF组分析完成")
        
    except FileNotFoundError:
        print("HFsnEF组数据文件未找到")
    
    print(f"\n" + "="*80)
    print("所有图表生成完成！")
    print("生成的文件:")
    print("• overall_volcano_plot_comprehensive.png - 整体人群火山图")
    print("• overall_forest_plot_top10.png - 整体人群前10个代谢物森林图")
    print("• hfnef_volcano_plot_comprehensive.png - HFnEF组火山图")
    print("• hfsnef_volcano_plot_comprehensive.png - HFsnEF组火山图")
    print("• hfsnef_forest_plot_top3.png - HFsnEF组前3个最显著代谢物森林图")
    print("="*80)

if __name__ == "__main__":
    main()
