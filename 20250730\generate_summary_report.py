#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cox分析结果摘要报告生成脚本

作者: AI Assistant
日期: 2025-07-30
描述: 基于Cox分析结果生成易读的摘要报告
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import glob

def find_latest_results_file():
    """
    查找最新的Cox分析结果文件
    """
    pattern = "cox_analysis_results_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到Cox分析结果文件")
    
    # 按修改时间排序，返回最新的文件
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def generate_summary_report(results_file):
    """
    生成摘要报告
    """
    print(f"正在读取结果文件：{results_file}")
    
    # 读取结果
    results = pd.read_csv(results_file)
    
    # 基本统计
    total_vars = len(results)
    successful_analyses = len(results[results['Status'] == '成功'])
    failed_analyses = total_vars - successful_analyses
    
    # 显著性分析
    significant_001 = len(results[(results['P_value'] < 0.001) & (results['Status'] == '成功')])
    significant_01 = len(results[(results['P_value'] < 0.01) & (results['Status'] == '成功')])
    significant_05 = len(results[(results['P_value'] < 0.05) & (results['Status'] == '成功')])
    
    # 按变量类型分组
    metabolite_results = results[results['Variable_Type'] == '代谢物']
    clinical_results = results[results['Variable_Type'] == '临床']
    
    metabolite_significant = len(metabolite_results[(metabolite_results['P_value'] < 0.05) & (metabolite_results['Status'] == '成功')])
    clinical_significant = len(clinical_results[(clinical_results['P_value'] < 0.05) & (clinical_results['Status'] == '成功')])
    
    # 生成报告
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    report_lines = []
    
    report_lines.append("=" * 80)
    report_lines.append("非心血管死亡差异代谢物和临床变量的单因素Cox分析 - 摘要报告")
    report_lines.append("=" * 80)
    report_lines.append(f"报告生成时间：{timestamp}")
    report_lines.append(f"数据来源：{results_file}")
    report_lines.append("")
    
    # 总体统计
    report_lines.append("【总体统计】")
    report_lines.append(f"  总变量数：{total_vars}")
    report_lines.append(f"  成功分析的变量数：{successful_analyses}")
    report_lines.append(f"  分析失败的变量数：{failed_analyses}")
    report_lines.append(f"  成功率：{successful_analyses/total_vars*100:.1f}%")
    report_lines.append("")
    
    # 显著性统计
    report_lines.append("【显著性统计】")
    report_lines.append(f"  p < 0.001：{significant_001} 个变量")
    report_lines.append(f"  p < 0.01：{significant_01} 个变量")
    report_lines.append(f"  p < 0.05：{significant_05} 个变量")
    report_lines.append(f"  显著变量比例：{significant_05/successful_analyses*100:.1f}%")
    report_lines.append("")
    
    # 按变量类型统计
    report_lines.append("【按变量类型统计】")
    report_lines.append(f"  代谢物变量：{len(metabolite_results)} 个，其中显著：{metabolite_significant} 个")
    report_lines.append(f"  临床变量：{len(clinical_results)} 个，其中显著：{clinical_significant} 个")
    report_lines.append("")
    
    # 最显著的变量（前20个）
    significant_vars = results[(results['P_value'] < 0.05) & (results['Status'] == '成功')].head(20)
    
    if len(significant_vars) > 0:
        report_lines.append("【最显著的变量（前20个）】")
        report_lines.append("排名  变量名称                                    类型    HR      95%CI           p值")
        report_lines.append("-" * 80)
        
        for i, (_, row) in enumerate(significant_vars.iterrows(), 1):
            var_name = row['Variable'][:40]  # 截断过长的变量名
            var_type = row['Variable_Type']
            hr = f"{row['HR']:.3f}" if not pd.isna(row['HR']) else "NA"
            ci = row['HR_95CI'][:15] if not pd.isna(row['HR']) else "NA"
            p_val = row['P_value_formatted']
            
            report_lines.append(f"{i:2d}    {var_name:<40} {var_type:<6} {hr:<8} {ci:<15} {p_val}")
        
        report_lines.append("")
    
    # 保护性因子（HR < 1且显著）
    protective_vars = results[
        (results['P_value'] < 0.05) & 
        (results['Status'] == '成功') & 
        (results['HR'] < 1)
    ].head(10)
    
    if len(protective_vars) > 0:
        report_lines.append("【保护性因子（HR < 1，前10个）】")
        report_lines.append("变量名称                                    类型    HR      p值")
        report_lines.append("-" * 60)
        
        for _, row in protective_vars.iterrows():
            var_name = row['Variable'][:40]
            var_type = row['Variable_Type']
            hr = f"{row['HR']:.3f}"
            p_val = row['P_value_formatted']
            
            report_lines.append(f"{var_name:<40} {var_type:<6} {hr:<8} {p_val}")
        
        report_lines.append("")
    
    # 危险因子（HR > 1且显著）
    risk_vars = results[
        (results['P_value'] < 0.05) & 
        (results['Status'] == '成功') & 
        (results['HR'] > 1)
    ].head(10)
    
    if len(risk_vars) > 0:
        report_lines.append("【危险因子（HR > 1，前10个）】")
        report_lines.append("变量名称                                    类型    HR      p值")
        report_lines.append("-" * 60)
        
        for _, row in risk_vars.iterrows():
            var_name = row['Variable'][:40]
            var_type = row['Variable_Type']
            hr = f"{row['HR']:.3f}"
            p_val = row['P_value_formatted']
            
            report_lines.append(f"{var_name:<40} {var_type:<6} {hr:<8} {p_val}")
        
        report_lines.append("")
    
    # 分析失败的变量
    failed_vars = results[results['Status'] != '成功']
    if len(failed_vars) > 0:
        report_lines.append("【分析失败的变量】")
        failure_counts = failed_vars['Status'].value_counts()
        for status, count in failure_counts.items():
            report_lines.append(f"  {status}：{count} 个变量")
        report_lines.append("")
    
    # 结论和建议
    report_lines.append("【结论和建议】")
    report_lines.append(f"1. 本次分析共识别出 {significant_05} 个与非心血管死亡显著相关的变量")
    report_lines.append(f"2. 其中代谢物变量 {metabolite_significant} 个，临床变量 {clinical_significant} 个")
    report_lines.append("3. 建议对显著变量进行进一步的多因素Cox回归分析")
    report_lines.append("4. 对于极显著的变量（p<0.001），建议进行生物学功能验证")
    report_lines.append("5. 保护性因子可能具有治疗潜力，值得深入研究")
    report_lines.append("")
    report_lines.append("=" * 80)
    
    return "\n".join(report_lines)

def main():
    """
    主函数
    """
    try:
        # 查找最新的结果文件
        results_file = find_latest_results_file()
        
        # 生成摘要报告
        report_content = generate_summary_report(results_file)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"cox_analysis_summary_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 同时输出到控制台
        print(report_content)
        print(f"\n摘要报告已保存到：{report_file}")
        
    except Exception as e:
        print(f"生成摘要报告时发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
