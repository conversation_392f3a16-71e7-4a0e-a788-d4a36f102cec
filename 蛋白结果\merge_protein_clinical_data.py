#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蛋白组和临床数据合并脚本
处理蛋白组数据并与临床数据合并
"""

import pandas as pd
import numpy as np
import re

def process_protein_data(protein_file):
    """
    处理蛋白组数据
    1. 只保留Gene Name列作为蛋白标识
    2. 删除最后8列统计分析结果
    3. 处理Record_ID（去除HFnEF_和HFsnEF_前缀）
    4. 转置数据
    """
    print("正在读取蛋白组数据...")
    df_protein = pd.read_csv(protein_file)
    
    print(f"原始蛋白组数据形状: {df_protein.shape}")
    print(f"原始列名: {list(df_protein.columns)}")
    
    # 获取需要删除的最后8列
    columns_to_remove = [
        'FC(HFsnEF_vs_HFnEF)', 
        'pValue(HFsnEF_vs_HFnEF)', 
        'FDR(HFsnEF_vs_HFnEF)', 
        'Sig(HFsnEF_vs_HFnEF)', 
        'CC', 
        'MF', 
        'BP', 
        'KEGG'
    ]
    
    # 删除最后8列
    for col in columns_to_remove:
        if col in df_protein.columns:
            df_protein = df_protein.drop(columns=[col])
    
    # 只保留Gene Name和样本列（删除ID, Accession, Description）
    columns_to_keep = ['Gene Name'] + [col for col in df_protein.columns 
                                      if col not in ['ID', 'Accession', 'Description', 'Gene Name']]
    df_protein = df_protein[columns_to_keep]
    
    print(f"删除不需要的列后形状: {df_protein.shape}")
    
    # 设置Gene Name为索引
    df_protein.set_index('Gene Name', inplace=True)
    
    # 处理列名：去除HFnEF_和HFsnEF_前缀
    new_columns = []
    for col in df_protein.columns:
        # 使用正则表达式去除HFnEF_和HFsnEF_前缀
        new_col = re.sub(r'^HF[sn]*EF_', '', col)
        new_columns.append(new_col)
    
    df_protein.columns = new_columns
    
    print(f"处理后的样本列名示例: {list(df_protein.columns)[:10]}")
    
    # 转置数据：样本变为行，蛋白变为列
    df_protein_transposed = df_protein.T
    
    # 重置索引，使Record_ID成为一列
    df_protein_transposed.reset_index(inplace=True)
    df_protein_transposed.rename(columns={'index': 'Record_ID'}, inplace=True)
    
    print(f"转置后数据形状: {df_protein_transposed.shape}")
    print(f"转置后前几个Record_ID: {list(df_protein_transposed['Record_ID'])[:10]}")
    
    return df_protein_transposed

def process_clinical_data(clinical_file):
    """
    处理临床数据
    """
    print("正在读取临床数据...")

    # 尝试不同的编码格式
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
    df_clinical = None

    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            df_clinical = pd.read_csv(clinical_file, encoding=encoding)
            print(f"成功使用编码: {encoding}")
            break
        except UnicodeDecodeError:
            print(f"编码 {encoding} 失败，尝试下一个...")
            continue

    if df_clinical is None:
        raise ValueError("无法读取临床数据文件，尝试了所有常见编码格式")

    print(f"临床数据形状: {df_clinical.shape}")
    print(f"临床数据Record_ID示例: {list(df_clinical['Record_ID'])[:10]}")

    # 确保Record_ID为字符串类型
    df_clinical['Record_ID'] = df_clinical['Record_ID'].astype(str)

    return df_clinical

def merge_datasets(df_protein, df_clinical):
    """
    合并蛋白组和临床数据
    以临床数据为基准进行左连接
    """
    print("正在合并数据集...")
    
    # 确保两个数据集的Record_ID都是字符串类型
    df_protein['Record_ID'] = df_protein['Record_ID'].astype(str)
    df_clinical['Record_ID'] = df_clinical['Record_ID'].astype(str)
    
    print(f"蛋白组数据中的Record_ID数量: {len(df_protein['Record_ID'].unique())}")
    print(f"临床数据中的Record_ID数量: {len(df_clinical['Record_ID'].unique())}")
    
    # 检查匹配情况
    protein_ids = set(df_protein['Record_ID'])
    clinical_ids = set(df_clinical['Record_ID'])
    
    matched_ids = protein_ids.intersection(clinical_ids)
    protein_only = protein_ids - clinical_ids
    clinical_only = clinical_ids - protein_ids
    
    print(f"匹配的Record_ID数量: {len(matched_ids)}")
    print(f"只在蛋白组数据中的Record_ID数量: {len(protein_only)}")
    print(f"只在临床数据中的Record_ID数量: {len(clinical_only)}")
    
    if len(protein_only) > 0:
        print(f"只在蛋白组数据中的Record_ID示例: {list(protein_only)[:10]}")
    if len(clinical_only) > 0:
        print(f"只在临床数据中的Record_ID示例: {list(clinical_only)[:10]}")
    
    # 以临床数据为基准进行左连接
    merged_df = pd.merge(df_clinical, df_protein, on='Record_ID', how='left')
    
    print(f"合并后数据形状: {merged_df.shape}")
    print(f"合并后列数: 临床变量 {len(df_clinical.columns)-1} + 蛋白 {len(df_protein.columns)-1} + Record_ID = {merged_df.shape[1]}")
    
    return merged_df

def main():
    """
    主函数
    """
    # 文件路径
    protein_file = "Proteins_all_diff.csv"
    clinical_file = "203HFsnEF_800d.csv"
    output_file = "merged_protein_clinical_data.csv"
    
    try:
        # 处理蛋白组数据
        df_protein = process_protein_data(protein_file)
        
        # 处理临床数据
        df_clinical = process_clinical_data(clinical_file)
        
        # 合并数据集
        merged_df = merge_datasets(df_protein, df_clinical)
        
        # 保存合并后的数据
        print(f"正在保存合并后的数据到 {output_file}...")
        merged_df.to_csv(output_file, index=False)
        
        print("数据处理完成！")
        print(f"最终数据形状: {merged_df.shape}")
        print(f"最终数据列名: Record_ID + {merged_df.shape[1]-1} 个变量")
        
        # 显示数据概览
        print("\n数据概览:")
        print(merged_df.head())
        
        # 检查缺失值情况
        print(f"\n缺失值统计:")
        missing_counts = merged_df.isnull().sum()
        print(f"有缺失值的列数: {(missing_counts > 0).sum()}")
        print(f"总缺失值数量: {missing_counts.sum()}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
