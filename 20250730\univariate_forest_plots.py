#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单因素Cox分析森林图生成脚本

作者: AI Assistant
日期: 2025-07-30
描述: 基于单因素Cox分析结果，分别为临床变量和代谢物变量生成森林图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import glob

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def find_latest_univariate_results():
    """
    查找最新的单因素Cox分析结果文件
    """
    pattern = "cox_analysis_results_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到单因素Cox分析结果文件")
    
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def parse_hr_ci(hr_ci_str):
    """
    解析HR_95CI字符串，提取HR、下限和上限
    格式：HR (lower-upper)
    """
    try:
        # 移除可能的空格
        hr_ci_str = str(hr_ci_str).strip()

        # 分割HR和置信区间
        parts = hr_ci_str.split(' (')
        hr = float(parts[0])

        # 提取置信区间
        ci_part = parts[1].rstrip(')')
        ci_bounds = ci_part.split('-')
        hr_lower = float(ci_bounds[0])
        hr_upper = float(ci_bounds[1])

        return hr, hr_lower, hr_upper
    except:
        return np.nan, np.nan, np.nan

def load_and_filter_results(exclude_vars=None):
    """
    加载并筛选单因素分析结果
    """
    if exclude_vars is None:
        exclude_vars = []

    print("正在加载单因素分析结果...")

    # 加载结果
    results_file = find_latest_univariate_results()
    results = pd.read_csv(results_file)

    print(f"原始结果：{len(results)} 个变量")

    # 解析HR_95CI列
    print("正在解析HR和置信区间...")
    hr_data = results['HR_95CI'].apply(parse_hr_ci)
    results['HR_parsed'] = [x[0] for x in hr_data]
    results['HR_95CI_Lower'] = [x[1] for x in hr_data]
    results['HR_95CI_Upper'] = [x[2] for x in hr_data]

    # 使用解析的HR值（如果原HR列有问题）
    results['HR'] = results['HR'].fillna(results['HR_parsed'])

    # 排除指定变量
    results = results[~results['Variable'].isin(exclude_vars)]
    print(f"排除 {len(exclude_vars)} 个变量后：{len(results)} 个变量")

    # 只保留成功分析的变量
    results = results[results['Status'] == '成功']
    print(f"成功分析的变量：{len(results)} 个")

    # 移除HR解析失败的变量
    results = results.dropna(subset=['HR', 'HR_95CI_Lower', 'HR_95CI_Upper'])
    print(f"HR数据完整的变量：{len(results)} 个")

    # 分离临床变量和代谢物变量
    clinical_vars = results[results['Variable_Type'] == '临床'].copy()
    metabolite_vars = results[results['Variable_Type'] == '代谢物'].copy()

    print(f"临床变量：{len(clinical_vars)} 个")
    print(f"代谢物变量：{len(metabolite_vars)} 个")

    return clinical_vars, metabolite_vars

def create_forest_plot(data, title, max_vars=30, figsize=(14, 16)):
    """
    创建森林图
    
    Parameters:
    data (pd.DataFrame): 分析结果数据
    title (str): 图表标题
    max_vars (int): 最大显示变量数
    figsize (tuple): 图形大小
    
    Returns:
    matplotlib.figure.Figure: 图形对象
    """
    print(f"正在创建森林图：{title}")
    
    # 按p值排序，选择前max_vars个
    if len(data) > max_vars:
        data_plot = data.nsmallest(max_vars, 'P_value').copy()
        print(f"显示前 {max_vars} 个最显著的变量")
    else:
        data_plot = data.copy()
    
    # 按p值排序（最显著的在上方）
    data_plot = data_plot.sort_values('P_value', ascending=False)
    
    n_vars = len(data_plot)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置y轴位置
    y_pos = np.arange(n_vars)
    
    # 提取数据
    hrs = data_plot['HR'].values
    hr_lowers = data_plot['HR_95CI_Lower'].values
    hr_uppers = data_plot['HR_95CI_Upper'].values
    p_values = data_plot['P_value'].values
    
    # 处理极端值（用于显示）
    hrs_display = np.copy(hrs)
    hr_lowers_display = np.copy(hr_lowers)
    hr_uppers_display = np.copy(hr_uppers)
    
    # 设置显示范围（避免极端值影响图形）
    x_min, x_max = 0.01, 100
    
    # 截断极端值用于显示
    hrs_display = np.clip(hrs_display, x_min, x_max)
    hr_lowers_display = np.clip(hr_lowers_display, x_min, x_max)
    hr_uppers_display = np.clip(hr_uppers_display, x_min, x_max)
    
    # 根据显著性设置颜色和大小
    colors = []
    sizes = []
    for p in p_values:
        if p < 0.001:
            colors.append('red')
            sizes.append(100)
        elif p < 0.01:
            colors.append('orange')
            sizes.append(80)
        elif p < 0.05:
            colors.append('blue')
            sizes.append(60)
        else:
            colors.append('gray')
            sizes.append(40)
    
    # 绘制误差条
    for i in range(n_vars):
        # 绘制置信区间线
        ax.plot([hr_lowers_display[i], hr_uppers_display[i]], [y_pos[i], y_pos[i]], 
               color=colors[i], linewidth=2, alpha=0.7)
        
        # 绘制置信区间端点
        ax.plot([hr_lowers_display[i], hr_lowers_display[i]], 
               [y_pos[i]-0.1, y_pos[i]+0.1], 
               color=colors[i], linewidth=2)
        ax.plot([hr_uppers_display[i], hr_uppers_display[i]], 
               [y_pos[i]-0.1, y_pos[i]+0.1], 
               color=colors[i], linewidth=2)
    
    # 绘制HR点
    scatter = ax.scatter(hrs_display, y_pos, c=colors, s=sizes, 
                        alpha=0.8, edgecolors='black', linewidth=1, zorder=5)
    
    # 添加HR=1的参考线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.7, linewidth=2)
    
    # 设置y轴标签
    var_labels = []
    for _, row in data_plot.iterrows():
        var_name = row['Variable']
        # 截断过长的变量名
        if len(var_name) > 50:
            var_name = var_name[:47] + '...'
        var_labels.append(var_name)
    
    ax.set_yticks(y_pos)
    ax.set_yticklabels(var_labels, fontsize=10)
    
    # 设置x轴
    ax.set_xlabel('风险比 (HR)', fontsize=14, fontweight='bold')
    ax.set_xscale('log')
    ax.set_xlim(x_min, x_max)
    
    # 设置x轴刻度
    x_ticks = [0.01, 0.1, 0.5, 1, 2, 5, 10, 50, 100]
    ax.set_xticks(x_ticks)
    ax.set_xticklabels([str(x) for x in x_ticks])
    
    # 设置标题
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    # 在右侧添加HR值、置信区间和p值
    ax2 = ax.twinx()
    ax2.set_ylim(ax.get_ylim())
    ax2.set_yticks(y_pos)
    
    # 创建右侧标签
    right_labels = []
    for _, row in data_plot.iterrows():
        hr_text = f"{row['HR']:.3f}"
        ci_text = f"({row['HR_95CI_Lower']:.3f}-{row['HR_95CI_Upper']:.3f})"
        
        # 格式化p值
        if row['P_value'] < 0.001:
            p_text = "<0.001"
        else:
            p_text = f"{row['P_value']:.3f}"
        
        right_labels.append(f"{hr_text} {ci_text}\np={p_text}")
    
    ax2.set_yticklabels(right_labels, fontsize=9)
    ax2.tick_params(axis='y', length=0)  # 隐藏刻度线
    
    # 添加右侧标题
    ax2.set_ylabel('HR (95% CI) 和 p值', fontsize=12, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        plt.scatter([], [], c='red', s=100, alpha=0.8, edgecolors='black', label='p < 0.001'),
        plt.scatter([], [], c='orange', s=80, alpha=0.8, edgecolors='black', label='p < 0.01'),
        plt.scatter([], [], c='blue', s=60, alpha=0.8, edgecolors='black', label='p < 0.05'),
        plt.scatter([], [], c='gray', s=40, alpha=0.8, edgecolors='black', label='p ≥ 0.05')
    ]
    ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
    
    # 添加统计信息
    total_vars = len(data_plot)
    significant_vars = len(data_plot[data_plot['P_value'] < 0.05])
    
    stats_text = f"总变量数: {total_vars}\n显著变量数: {significant_vars}\n显著比例: {significant_vars/total_vars*100:.1f}%"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
           verticalalignment='top', fontsize=10,
           bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    return fig

def create_summary_table(clinical_vars, metabolite_vars, exclude_vars):
    """
    创建摘要表
    """
    print("正在创建摘要表...")
    
    # 合并数据
    all_vars = pd.concat([clinical_vars, metabolite_vars], ignore_index=True)
    
    # 按p值排序
    all_vars = all_vars.sort_values('P_value')
    
    # 选择显著变量
    significant_vars = all_vars[all_vars['P_value'] < 0.05].copy()
    
    # 创建摘要
    summary_lines = []
    summary_lines.append("=" * 80)
    summary_lines.append("单因素Cox分析森林图 - 摘要报告")
    summary_lines.append("=" * 80)
    summary_lines.append(f"分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    summary_lines.append("")
    
    summary_lines.append("【排除变量】")
    for var in exclude_vars:
        summary_lines.append(f"  - {var}")
    summary_lines.append("")
    
    summary_lines.append("【总体统计】")
    summary_lines.append(f"  临床变量总数：{len(clinical_vars)}")
    summary_lines.append(f"  代谢物变量总数：{len(metabolite_vars)}")
    summary_lines.append(f"  总变量数：{len(all_vars)}")
    summary_lines.append("")
    
    summary_lines.append("【显著性统计】")
    clinical_sig = len(clinical_vars[clinical_vars['P_value'] < 0.05])
    metabolite_sig = len(metabolite_vars[metabolite_vars['P_value'] < 0.05])
    
    summary_lines.append(f"  临床变量显著数：{clinical_sig}/{len(clinical_vars)} ({clinical_sig/len(clinical_vars)*100:.1f}%)")
    summary_lines.append(f"  代谢物变量显著数：{metabolite_sig}/{len(metabolite_vars)} ({metabolite_sig/len(metabolite_vars)*100:.1f}%)")
    summary_lines.append(f"  总显著变量数：{len(significant_vars)}")
    summary_lines.append("")
    
    if len(significant_vars) > 0:
        summary_lines.append("【前20个最显著变量】")
        summary_lines.append("变量名称                                    类型    HR      95%CI           p值")
        summary_lines.append("-" * 80)
        
        top_20 = significant_vars.head(20)
        for _, row in top_20.iterrows():
            var_name = row['Variable'][:40]
            var_type = row['Variable_Type']
            hr = f"{row['HR']:.3f}"
            ci = f"({row['HR_95CI_Lower']:.3f}-{row['HR_95CI_Upper']:.3f})"
            p_val = f"{row['P_value']:.3f}" if row['P_value'] >= 0.001 else "<0.001"
            
            summary_lines.append(f"{var_name:<40} {var_type:<6} {hr:<8} {ci:<15} {p_val}")
    
    summary_lines.append("")
    summary_lines.append("=" * 80)
    
    return "\n".join(summary_lines)

def main():
    """
    主函数
    """
    print("=" * 80)
    print("单因素Cox分析森林图生成")
    print("=" * 80)
    
    try:
        # 1. 定义要排除的变量
        exclude_vars = [
            'Death_in_hospital',
            'Inhospital_vasoactive_drug', 
            'Inhospital_mehanical_ventilation',
            'AR'
        ]
        
        # 2. 加载和筛选数据
        clinical_vars, metabolite_vars = load_and_filter_results(exclude_vars)
        
        if len(clinical_vars) == 0 and len(metabolite_vars) == 0:
            print("没有可用的变量进行分析")
            return
        
        # 3. 创建时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 4. 创建临床变量森林图
        if len(clinical_vars) > 0:
            print(f"\n创建临床变量森林图（{len(clinical_vars)}个变量）...")
            clinical_fig = create_forest_plot(
                clinical_vars, 
                "临床变量单因素Cox回归分析森林图\n(非心血管死亡风险因子)",
                max_vars=30,
                figsize=(14, max(10, len(clinical_vars) * 0.4))
            )
            
            # 保存临床变量森林图
            clinical_plot_file = f"clinical_variables_forest_plot_{timestamp}.png"
            clinical_fig.savefig(clinical_plot_file, dpi=300, bbox_inches='tight')
            print(f"临床变量森林图已保存：{clinical_plot_file}")
        
        # 5. 创建代谢物变量森林图
        if len(metabolite_vars) > 0:
            print(f"\n创建代谢物变量森林图（{len(metabolite_vars)}个变量）...")
            
            # 由于代谢物变量较多，分批显示
            n_metabolites = len(metabolite_vars)
            max_per_plot = 40
            
            if n_metabolites <= max_per_plot:
                # 单个图显示所有代谢物
                metabolite_fig = create_forest_plot(
                    metabolite_vars,
                    "代谢物变量单因素Cox回归分析森林图\n(非心血管死亡风险因子)",
                    max_vars=max_per_plot,
                    figsize=(14, max(12, n_metabolites * 0.3))
                )
                
                metabolite_plot_file = f"metabolite_variables_forest_plot_{timestamp}.png"
                metabolite_fig.savefig(metabolite_plot_file, dpi=300, bbox_inches='tight')
                print(f"代谢物变量森林图已保存：{metabolite_plot_file}")
            else:
                # 分为两个图：显著的和最显著的前40个
                significant_metabolites = metabolite_vars[metabolite_vars['P_value'] < 0.05]
                
                if len(significant_metabolites) > 0:
                    # 显著代谢物图
                    sig_fig = create_forest_plot(
                        significant_metabolites,
                        "显著代谢物变量单因素Cox回归分析森林图\n(p < 0.05, 非心血管死亡风险因子)",
                        max_vars=50,
                        figsize=(14, max(12, len(significant_metabolites) * 0.3))
                    )
                    
                    sig_plot_file = f"significant_metabolites_forest_plot_{timestamp}.png"
                    sig_fig.savefig(sig_plot_file, dpi=300, bbox_inches='tight')
                    print(f"显著代谢物森林图已保存：{sig_plot_file}")
                
                # 前40个最显著代谢物图
                top_metabolites = metabolite_vars.nsmallest(max_per_plot, 'P_value')
                top_fig = create_forest_plot(
                    top_metabolites,
                    f"前{max_per_plot}个最显著代谢物变量单因素Cox回归分析森林图\n(非心血管死亡风险因子)",
                    max_vars=max_per_plot,
                    figsize=(14, max(12, max_per_plot * 0.3))
                )
                
                top_plot_file = f"top_metabolites_forest_plot_{timestamp}.png"
                top_fig.savefig(top_plot_file, dpi=300, bbox_inches='tight')
                print(f"前{max_per_plot}个代谢物森林图已保存：{top_plot_file}")
        
        # 6. 创建摘要报告
        summary_content = create_summary_table(clinical_vars, metabolite_vars, exclude_vars)
        summary_file = f"univariate_forest_summary_{timestamp}.txt"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        print(f"\n摘要报告已保存：{summary_file}")
        
        # 7. 输出统计摘要
        print("\n" + "=" * 80)
        print("森林图生成完成！")
        print("=" * 80)
        
        clinical_sig = len(clinical_vars[clinical_vars['P_value'] < 0.05]) if len(clinical_vars) > 0 else 0
        metabolite_sig = len(metabolite_vars[metabolite_vars['P_value'] < 0.05]) if len(metabolite_vars) > 0 else 0
        
        print(f"临床变量：{len(clinical_vars)} 个，其中显著：{clinical_sig} 个")
        print(f"代谢物变量：{len(metabolite_vars)} 个，其中显著：{metabolite_sig} 个")
        print(f"总显著变量：{clinical_sig + metabolite_sig} 个")
        
        # 显示图形
        plt.show()
        
    except Exception as e:
        print(f"分析过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
