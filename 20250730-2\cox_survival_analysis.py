#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cox比例风险回归分析 - 代谢物与非心血管死亡的关联
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from lifelines import CoxPHFitter
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def perform_cox_analysis():
    """进行Cox比例风险回归分析"""
    
    print("=" * 80)
    print("Cox比例风险回归分析 - 代谢物与非心血管死亡")
    print("=" * 80)
    
    # 读取数据
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    overall_results = pd.read_csv('overall_metabolite_results.csv')
    
    # 获取显著的代谢物（前20个）
    significant_metabolites = overall_results.head(20)['metabolite'].tolist()
    
    print(f"数据概况:")
    print(f"总样本数: {len(df)}")
    print(f"分析的代谢物数量: {len(significant_metabolites)}")
    
    # 清理数据
    analysis_df = df.dropna(subset=['NonCV_death', 'OS'])
    print(f"有效样本数: {len(analysis_df)}")
    print(f"非心血管死亡事件数: {int(analysis_df['NonCV_death'].sum())}")
    print()
    
    # 进行Cox回归分析
    cox_results = []
    
    print("正在进行Cox回归分析...")
    
    for i, metabolite in enumerate(significant_metabolites, 1):
        try:
            # 准备数据
            mask = ~(analysis_df[metabolite].isna() | analysis_df['OS'].isna() | analysis_df['NonCV_death'].isna())
            if mask.sum() < 20:  # 样本量太小跳过
                continue
                
            cox_df = analysis_df.loc[mask, [metabolite, 'OS', 'NonCV_death']].copy()
            
            # 标准化代谢物浓度（使用Z-score）
            cox_df[f'{metabolite}_std'] = (cox_df[metabolite] - cox_df[metabolite].mean()) / cox_df[metabolite].std()
            
            # 准备Cox回归数据
            cox_data = cox_df[['OS', 'NonCV_death', f'{metabolite}_std']].copy()
            cox_data.columns = ['duration', 'event', 'metabolite_std']
            
            # 拟合Cox模型
            cph = CoxPHFitter()
            cph.fit(cox_data, duration_col='duration', event_col='event')
            
            # 提取结果
            coef = cph.params_['metabolite_std']
            hr = np.exp(coef)
            se = cph.standard_errors_['metabolite_std']
            ci_lower = np.exp(coef - 1.96 * se)
            ci_upper = np.exp(coef + 1.96 * se)
            p_value = cph.summary.loc['metabolite_std', 'p']
            z_score = cph.summary.loc['metabolite_std', 'z']
            
            # 计算C-index
            c_index = cph.concordance_index_
            
            # Log-rank检验（将代谢物分为高低两组）
            median_value = cox_df[metabolite].median()
            cox_df['metabolite_high'] = (cox_df[metabolite] > median_value).astype(int)
            
            high_group = cox_df[cox_df['metabolite_high'] == 1]
            low_group = cox_df[cox_df['metabolite_high'] == 0]
            
            if len(high_group) > 0 and len(low_group) > 0:
                logrank_result = logrank_test(
                    high_group['OS'], low_group['OS'],
                    high_group['NonCV_death'], low_group['NonCV_death']
                )
                logrank_p = logrank_result.p_value
            else:
                logrank_p = np.nan
            
            cox_results.append({
                'metabolite': metabolite,
                'coefficient': coef,
                'hazard_ratio': hr,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'p_value': p_value,
                'z_score': z_score,
                'c_index': c_index,
                'logrank_p': logrank_p,
                'n_samples': len(cox_data),
                'n_events': int(cox_data['event'].sum()),
                'mean_metabolite': cox_df[metabolite].mean(),
                'std_metabolite': cox_df[metabolite].std()
            })
            
            print(f"完成 {i}/{len(significant_metabolites)}: {metabolite.split('_')[0][:40]}")
            
        except Exception as e:
            print(f"分析 {metabolite} 时出错: {e}")
            continue
    
    # 转换为DataFrame
    cox_results_df = pd.DataFrame(cox_results)
    
    if len(cox_results_df) > 0:
        # 按p值排序
        cox_results_df = cox_results_df.sort_values('p_value')
        
        print(f"\n完成Cox回归分析，共分析了 {len(cox_results_df)} 个代谢物")
        
        # 显示结果
        print("\nCox回归分析结果 (前15个):")
        print("=" * 120)
        print(f"{'代谢物名称':<45} {'HR':<8} {'95%CI':<20} {'P值':<10} {'C-index':<8} {'事件数':<6}")
        print("-" * 120)
        
        for i, row in cox_results_df.head(15).iterrows():
            metabolite_name = row['metabolite'].split('_')[0][:40]
            hr_str = f"{row['hazard_ratio']:.3f}"
            ci_str = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
            p_str = f"{row['p_value']:.4f}"
            c_str = f"{row['c_index']:.3f}"
            events_str = f"{row['n_events']}"
            
            print(f"{metabolite_name:<45} {hr_str:<8} {ci_str:<20} {p_str:<10} {c_str:<8} {events_str:<6}")
        
        # 保存结果
        cox_results_df.to_csv('cox_survival_analysis_results.csv', index=False)
        print(f"\nCox回归分析结果已保存到: cox_survival_analysis_results.csv")
        
        # 创建森林图
        create_forest_plot(cox_results_df)
        
        # 分组分析
        perform_subgroup_cox_analysis(analysis_df, significant_metabolites[:10])
        
        return cox_results_df
    
    else:
        print("未能完成任何代谢物的Cox回归分析")
        return pd.DataFrame()

def create_forest_plot(cox_results_df, top_n=15):
    """创建森林图"""
    
    print(f"\n正在创建森林图...")
    
    # 选择前N个最显著的结果
    plot_data = cox_results_df.head(top_n).copy()
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, max(8, len(plot_data) * 0.6)))
    
    # 绘制森林图
    y_positions = range(len(plot_data))
    
    # 绘制HR点和置信区间
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i  # 倒序排列
        
        # HR点
        color = 'red' if row['hazard_ratio'] > 1 else 'blue'
        ax.scatter(row['hazard_ratio'], y, s=100, color=color, alpha=0.8, zorder=3)
        
        # 置信区间
        ax.plot([row['ci_lower'], row['ci_upper']], [y, y], color=color, linewidth=2, alpha=0.6)
        ax.plot([row['ci_lower'], row['ci_lower']], [y-0.1, y+0.1], color=color, linewidth=2, alpha=0.6)
        ax.plot([row['ci_upper'], row['ci_upper']], [y-0.1, y+0.1], color=color, linewidth=2, alpha=0.6)
    
    # 添加参考线 HR=1
    ax.axvline(x=1, color='gray', linestyle='--', alpha=0.7, zorder=1)
    
    # 设置y轴标签
    metabolite_names = [name.split('_')[0][:35] for name in plot_data['metabolite']]
    ax.set_yticks(range(len(plot_data)))
    ax.set_yticklabels(reversed(metabolite_names))
    
    # 设置x轴
    ax.set_xlabel('风险比 (Hazard Ratio)')
    ax.set_title('代谢物与非心血管死亡的Cox回归分析森林图', fontsize=14, fontweight='bold')
    
    # 设置x轴范围
    all_hrs = list(plot_data['hazard_ratio']) + list(plot_data['ci_lower']) + list(plot_data['ci_upper'])
    x_min = max(0.1, min(all_hrs) * 0.8)
    x_max = max(all_hrs) * 1.2
    ax.set_xlim(x_min, x_max)
    
    # 使用对数刻度
    ax.set_xscale('log')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 添加HR值和P值标注
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i
        hr_text = f"HR={row['hazard_ratio']:.3f}"
        p_text = f"P={row['p_value']:.4f}"
        
        # 在右侧添加文本
        ax.text(x_max * 0.95, y, f"{hr_text}, {p_text}", 
                ha='right', va='center', fontsize=9, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('cox_forest_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("森林图已保存到: cox_forest_plot.png")

def perform_subgroup_cox_analysis(df, metabolites):
    """分组Cox回归分析"""
    
    print(f"\n正在进行分组Cox回归分析...")
    
    subgroup_results = []
    
    for metabolite in metabolites:
        try:
            # HFnEF组
            hfnef_df = df[df['HFsnEF'] == 0].copy()
            hfnef_result = single_cox_analysis(hfnef_df, metabolite, 'HFnEF')
            if hfnef_result:
                subgroup_results.append(hfnef_result)
            
            # HFsnEF组
            hfsnef_df = df[df['HFsnEF'] == 1].copy()
            hfsnef_result = single_cox_analysis(hfsnef_df, metabolite, 'HFsnEF')
            if hfsnef_result:
                subgroup_results.append(hfsnef_result)
                
        except Exception as e:
            print(f"分组分析 {metabolite} 时出错: {e}")
            continue
    
    if subgroup_results:
        subgroup_df = pd.DataFrame(subgroup_results)
        subgroup_df.to_csv('subgroup_cox_analysis_results.csv', index=False)
        print(f"分组Cox回归分析结果已保存到: subgroup_cox_analysis_results.csv")
        
        # 显示结果
        print(f"\n分组Cox回归分析结果:")
        print("-" * 100)
        for group in ['HFnEF', 'HFsnEF']:
            group_data = subgroup_df[subgroup_df['subgroup'] == group]
            if len(group_data) > 0:
                print(f"\n{group}组:")
                significant = group_data[group_data['p_value'] < 0.05]
                print(f"显著相关的代谢物: {len(significant)} 个")
                
                if len(significant) > 0:
                    for i, row in significant.head(5).iterrows():
                        metabolite_name = row['metabolite'].split('_')[0][:30]
                        print(f"  {metabolite_name:<30} HR={row['hazard_ratio']:.3f} [{row['ci_lower']:.3f}-{row['ci_upper']:.3f}] P={row['p_value']:.4f}")

def single_cox_analysis(df, metabolite, subgroup_name):
    """单个代谢物的Cox回归分析"""
    
    try:
        # 准备数据
        mask = ~(df[metabolite].isna() | df['OS'].isna() | df['NonCV_death'].isna())
        if mask.sum() < 10:  # 样本量太小
            return None
            
        cox_df = df.loc[mask, [metabolite, 'OS', 'NonCV_death']].copy()
        
        if cox_df['NonCV_death'].sum() < 3:  # 事件数太少
            return None
        
        # 标准化代谢物浓度
        cox_df[f'{metabolite}_std'] = (cox_df[metabolite] - cox_df[metabolite].mean()) / cox_df[metabolite].std()
        
        # 准备Cox回归数据
        cox_data = cox_df[['OS', 'NonCV_death', f'{metabolite}_std']].copy()
        cox_data.columns = ['duration', 'event', 'metabolite_std']
        
        # 拟合Cox模型
        cph = CoxPHFitter()
        cph.fit(cox_data, duration_col='duration', event_col='event')
        
        # 提取结果
        coef = cph.params_['metabolite_std']
        hr = np.exp(coef)
        se = cph.standard_errors_['metabolite_std']
        ci_lower = np.exp(coef - 1.96 * se)
        ci_upper = np.exp(coef + 1.96 * se)
        p_value = cph.summary.loc['metabolite_std', 'p']
        
        return {
            'metabolite': metabolite,
            'subgroup': subgroup_name,
            'hazard_ratio': hr,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'p_value': p_value,
            'n_samples': len(cox_data),
            'n_events': int(cox_data['event'].sum())
        }
        
    except Exception as e:
        return None

def main():
    """主函数"""
    print("开始Cox比例风险回归分析...")
    
    # 进行Cox回归分析
    cox_results = perform_cox_analysis()
    
    if len(cox_results) > 0:
        # 统计显著结果
        significant_cox = cox_results[cox_results['p_value'] < 0.05]
        
        print(f"\n=== 分析总结 ===")
        print(f"完成分析的代谢物数量: {len(cox_results)}")
        print(f"Cox回归显著的代谢物数量: {len(significant_cox)}")
        
        if len(significant_cox) > 0:
            print(f"\nCox回归显著的代谢物:")
            for i, row in significant_cox.iterrows():
                metabolite_name = row['metabolite'].split('_')[0][:40]
                risk_direction = "增加风险" if row['hazard_ratio'] > 1 else "降低风险"
                print(f"• {metabolite_name} - HR={row['hazard_ratio']:.3f} [{row['ci_lower']:.3f}-{row['ci_upper']:.3f}] P={row['p_value']:.4f} ({risk_direction})")
        
        print(f"\n注意: HR > 1 表示该代谢物浓度增加与非心血管死亡风险增加相关")
        print(f"      HR < 1 表示该代谢物浓度增加与非心血管死亡风险降低相关")
    
    print(f"\nCox回归分析完成!")

if __name__ == "__main__":
    main()
