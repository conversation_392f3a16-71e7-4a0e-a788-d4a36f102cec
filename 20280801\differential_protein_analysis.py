import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib_venn import venn2, venn2_circles
import matplotlib.patches as mpatches

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 读取蛋白质数据
print("读取蛋白质数据...")
df = pd.read_csv("Claude/Proteins_all_diff.csv")

# 显示数据基本信息
print(f"数据形状: {df.shape}")
print("数据列名:", df.columns.tolist()[:10], "...")

# 分析差异表达蛋白
# 根据p值<0.05和fold change>1.5筛选显著差异表达的蛋白
print("分析差异表达蛋白...")

# 提取FC和p-value列
fc_col = [col for col in df.columns if col.startswith('FC(HFsnEF_vs_HFnEF)')]
pvalue_col = [col for col in df.columns if col.startswith('pValue(HFsnEF_vs_HFnEF)')]

if fc_col and pvalue_col:
    fc_col = fc_col[0]
    pvalue_col = pvalue_col[0]
    
    # 创建新列来标识差异表达
    df['log2FC'] = np.log2(df[fc_col])
    df['-log10(pvalue)'] = -np.log10(df[pvalue_col])
    
    # 标记上调、下调和无显著差异的蛋白
    df['Regulation'] = 'Not Significant'
    # 上调: FC > 1.5 (log2FC > 0.585) 且 p-value < 0.05
    df.loc[(df[fc_col] > 1.5) & (df[pvalue_col] < 0.05), 'Regulation'] = 'Up-regulated'
    # 下调: FC < 1/1.5 (log2FC < -0.585) 且 p-value < 0.05
    df.loc[(df[fc_col] < 1/1.5) & (df[pvalue_col] < 0.05), 'Regulation'] = 'Down-regulated'
    
    # 统计差异蛋白数量
    up_regulated = df[df['Regulation'] == 'Up-regulated'].shape[0]
    down_regulated = df[df['Regulation'] == 'Down-regulated'].shape[0]
    
    print(f"上调蛋白数量: {up_regulated}")
    print(f"下调蛋白数量: {down_regulated}")
    print(f"总差异蛋白数量: {up_regulated + down_regulated}")
    
    # 保存差异表达蛋白结果
    diff_proteins = df[df['Regulation'] != 'Not Significant']
    diff_proteins.to_csv('differential_proteins.csv', index=False)
    
    # 绘制火山图
    print("绘制火山图...")
    plt.figure(figsize=(10, 8))
    
    # 设置点的颜色
    colors = {'Up-regulated': 'red', 'Down-regulated': 'blue', 'Not Significant': 'grey'}
    
    # 绘制散点图
    sns.scatterplot(
        data=df,
        x='log2FC',
        y='-log10(pvalue)',
        hue='Regulation',
        palette=colors,
        s=50,
        alpha=0.7
    )
    
    # 添加阈值线
    plt.axhline(y=-np.log10(0.05), color='grey', linestyle='--', alpha=0.7)
    plt.axvline(x=np.log2(1.5), color='grey', linestyle='--', alpha=0.7)
    plt.axvline(x=-np.log2(1.5), color='grey', linestyle='--', alpha=0.7)
    
    # 添加标签
    gene_col = 'Gene Name' if 'Gene Name' in df.columns else df.columns[2]  # 假设第3列是基因名称
    print(f"使用{gene_col}列作为蛋白标识符")
    
    # 标记Top 10显著差异的基因
    top_genes = df.sort_values(by='-log10(pvalue)', ascending=False).head(10)
    for _, row in top_genes.iterrows():
        plt.text(row['log2FC'], row['-log10(pvalue)'], row[gene_col], 
                 fontsize=9, ha='center', va='center', 
                 bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))
    
    # 设置图表标题和轴标签
    plt.title('HFsnEF vs HFnEF 差异蛋白火山图', fontsize=16)
    plt.xlabel('log2(Fold Change)', fontsize=14)
    plt.ylabel('-log10(p-value)', fontsize=14)
    
    # 添加图例
    legend_labels = [f'上调 ({up_regulated})', f'下调 ({down_regulated})', '无显著差异']
    handles, _ = plt.gca().get_legend_handles_labels()
    plt.legend(handles, legend_labels, title='调控状态', loc='upper right', fontsize=12)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig('volcano_plot_proteins.png', dpi=300, bbox_inches='tight')
    plt.savefig('volcano_plot_proteins.svg', format='svg', bbox_inches='tight')
    print("火山图已保存为 volcano_plot_proteins.png 和 volcano_plot_proteins.svg")
    
    # 显示图表
    plt.show()
    
    # 绘制韦恩图（使用全部蛋白数据）
    print("绘制韦恩图（全部蛋白）...")
    
    # 打印所有列名，帮助调试
    print("所有列名:")
    for i, col in enumerate(df.columns):
        print(f"  {i}: {col}")
    
    # 确定HFnEF和HFsnEF样本列（尝试不同的匹配方式）
    # 先尝试精确匹配
    hfnef_cols = [col for col in df.columns if 'HFnEF-' in col]
    hfsnef_cols = [col for col in df.columns if 'HFsnEF-' in col]
    
    # 如果没找到，尝试更宽松的匹配
    if not hfnef_cols and not hfsnef_cols:
        print("未找到严格匹配的样本列，尝试宽松匹配...")
        hfnef_cols = [col for col in df.columns if 'HFnEF' in col and not 'HFsnEF' in col]
        hfsnef_cols = [col for col in df.columns if 'HFsnEF' in col]
    
    # 如果还是没找到，检查常见替代名称
    if not hfnef_cols and not hfsnef_cols:
        print("尝试检查其他可能的组名格式...")
        hfnef_cols = [col for col in df.columns if ('HFNEF' in col.upper() or 'HF-NEF' in col.upper()) and not ('HFSNEF' in col.upper() or 'HFS-NEF' in col.upper())]
        hfsnef_cols = [col for col in df.columns if 'HFSNEF' in col.upper() or 'HFS-NEF' in col.upper()]
    
    print(f"找到HFnEF样本列: {len(hfnef_cols)}个")
    if hfnef_cols:
        print(f"  示例: {hfnef_cols[:3]}...")
    
    print(f"找到HFsnEF样本列: {len(hfsnef_cols)}个")
    if hfsnef_cols:
        print(f"  示例: {hfsnef_cols[:3]}...")
    
    # 如果没有找到任何样本列，尝试使用基于描述的分组方法
    if not hfnef_cols and not hfsnef_cols:
        print("警告：未能通过列名识别样本列。尝试使用FC和p-value信息进行分析...")
        
        # 通过fold change和p-value将蛋白分为两组
        # 上调 (HFsnEF) 和 下调 (HFnEF) 的蛋白
        fc_col = [col for col in df.columns if col.startswith('FC(HFsnEF_vs_HFnEF)') or 'FC' in col]
        pvalue_col = [col for col in df.columns if col.startswith('pValue') or 'p-value' in col or 'pValue' in col]
        
        if fc_col and pvalue_col:
            fc_col = fc_col[0]
            pvalue_col = pvalue_col[0]
            print(f"使用fold change列: {fc_col}")
            print(f"使用p-value列: {pvalue_col}")
            
            # 所有蛋白（至少能被检测到的）
            all_proteins = set(df[gene_col].dropna())
            
            # 在HFsnEF中高表达(FC>1)的视为HFsnEF蛋白
            hfsnef_proteins = set(df.loc[df[fc_col] > 1, gene_col])
            
            # 在HFnEF中高表达(FC<1)的视为HFnEF蛋白
            hfnef_proteins = set(df.loc[df[fc_col] < 1, gene_col])
            
            # 表达相同的蛋白可能属于两组
            print(f"总蛋白数量: {len(all_proteins)}")
        else:
            print("错误: 无法找到fold change或p-value列")
            hfnef_proteins = set()
            hfsnef_proteins = set()
    else:
        # 定义检测阈值（一般蛋白组学数据中表达值>0即为检测到）
        detection_threshold = 0.001  # 调高阈值，避免噪音
        
        print("检查样本数据范围:")
        if hfnef_cols:
            print(f"HFnEF组数据范围: {df[hfnef_cols].min().min()} 到 {df[hfnef_cols].max().max()}")
        if hfsnef_cols:
            print(f"HFsnEF组数据范围: {df[hfsnef_cols].min().min()} 到 {df[hfsnef_cols].max().max()}")
            
            # 在每组中识别检测到的所有蛋白
    # 如果任何一个样本中检测到该蛋白（表达值>阈值），则认为该蛋白在该组中存在
    hfnef_proteins = set()
    if hfnef_cols:
        hfnef_mask = df[hfnef_cols].max(axis=1) > detection_threshold
        hfnef_proteins = set(df.loc[hfnef_mask, gene_col].dropna())
        print(f"根据表达值阈值 > {detection_threshold} 筛选出 {hfnef_mask.sum()} 个蛋白在HFnEF组中检测到")
    
    hfsnef_proteins = set()
    if hfsnef_cols:
        hfsnef_mask = df[hfsnef_cols].max(axis=1) > detection_threshold
        hfsnef_proteins = set(df.loc[hfsnef_mask, gene_col].dropna())
        print(f"根据表达值阈值 > {detection_threshold} 筛选出 {hfsnef_mask.sum()} 个蛋白在HFsnEF组中检测到")
    
    # 计算交集蛋白数量
    shared_proteins = hfnef_proteins.intersection(hfsnef_proteins)
    print(f"仅在HFnEF中检测到的蛋白数量: {len(hfnef_proteins) - len(shared_proteins)}")
    print(f"仅在HFsnEF中检测到的蛋白数量: {len(hfsnef_proteins) - len(shared_proteins)}")
    print(f"两组中都检测到的蛋白数量: {len(shared_proteins)}")
    print(f"HFnEF组总检测蛋白数量: {len(hfnef_proteins)}")
    print(f"HFsnEF组总检测蛋白数量: {len(hfsnef_proteins)}")
    print(f"总检测到的非重复蛋白数量: {len(hfnef_proteins.union(hfsnef_proteins))}")
    
    # 创建韦恩图
    plt.figure(figsize=(10, 8))
    
    # 绘制韦恩图
    v = venn2(
        subsets=(len(hfnef_proteins) - len(shared_proteins), 
                 len(hfsnef_proteins) - len(shared_proteins), 
                 len(shared_proteins)),
        set_labels=('HFnEF', 'HFsnEF')
    )
    
    # 设置字体大小
    for label in v.set_labels:
        if label:
            label.set_fontsize(16)
    
    for text in v.subset_labels:
        if text:
            text.set_fontsize(14)
    
    # 设置颜色
    if v.patches[0]:  # HFnEF
        v.patches[0].set_color('blue')
        v.patches[0].set_alpha(0.6)
    
    if v.patches[1]:  # HFsnEF
        v.patches[1].set_color('red')
        v.patches[1].set_alpha(0.6)
    
    if v.patches[2]:  # 交集
        v.patches[2].set_color('purple')
        v.patches[2].set_alpha(0.6)
    
    # 添加边框
    venn2_circles(
        subsets=(len(hfnef_proteins) - len(shared_proteins), 
                 len(hfsnef_proteins) - len(shared_proteins), 
                 len(shared_proteins)),
        linestyle='solid'
    )
    
    # 设置标题
    plt.title('HFnEF vs HFsnEF 全部检测蛋白韦恩图', fontsize=16)
    
    # 添加图例说明
    blue_patch = mpatches.Patch(color='blue', alpha=0.6, label=f'仅在HFnEF中检测到的蛋白 ({len(hfnef_proteins) - len(shared_proteins)})')
    red_patch = mpatches.Patch(color='red', alpha=0.6, label=f'仅在HFsnEF中检测到的蛋白 ({len(hfsnef_proteins) - len(shared_proteins)})')
    purple_patch = mpatches.Patch(color='purple', alpha=0.6, label=f'两组中都检测到的蛋白 ({len(shared_proteins)})')
    plt.legend(handles=[blue_patch, red_patch, purple_patch], loc='upper right', fontsize=12)
    
    # 保存韦恩图
    plt.tight_layout()
    plt.savefig('venn_diagram_all_proteins.png', dpi=300, bbox_inches='tight')
    plt.savefig('venn_diagram_all_proteins.svg', format='svg', bbox_inches='tight')
    print("韦恩图已保存为 venn_diagram_all_proteins.png 和 venn_diagram_all_proteins.svg")
    
    # 显示韦恩图
    plt.show()
    
    # 保存各组特异性蛋白到CSV文件
    hfnef_specific = df[df[gene_col].isin(hfnef_proteins - shared_proteins)]
    hfsnef_specific = df[df[gene_col].isin(hfsnef_proteins - shared_proteins)]
    shared = df[df[gene_col].isin(shared_proteins)]
    
    hfnef_specific.to_csv('hfnef_specific_all_proteins.csv', index=False)
    hfsnef_specific.to_csv('hfsnef_specific_all_proteins.csv', index=False)
    shared.to_csv('shared_all_proteins.csv', index=False)
    print("已将各组特异性和共有蛋白保存到CSV文件")
else:
    print("错误: 未找到Fold Change或p-value列，请检查数据文件") 