#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
U型关系分析和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_u_shape_relationship():
    """分析和可视化U型关系"""
    
    # 读取数据
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    lvef_results = pd.read_csv('lvef_relationship_results.csv')
    
    # 找到呈现U型关系的代谢物
    u_shape_metabolite = '(6-carboxy-3,4,5-trihydroxyoxan-2-yl)[2-(3,4-dihydroxyphenyl)-3,5-dihydroxy-7H-chromen-7-ylidene]oxidanium_POS'
    
    print("=" * 80)
    print("U型关系分析 - 代谢物与LVEF的关系")
    print("=" * 80)
    
    print(f"分析的代谢物: {u_shape_metabolite.split('_')[0]}")
    
    # 获取数据
    mask = ~(df[u_shape_metabolite].isna() | df['LVEF'].isna() | df['NonCV_death'].isna())
    analysis_df = df.loc[mask, [u_shape_metabolite, 'LVEF', 'NonCV_death', 'HFsnEF']].copy()
    
    print(f"有效样本数: {len(analysis_df)}")
    print(f"LVEF范围: {analysis_df['LVEF'].min():.1f} - {analysis_df['LVEF'].max():.1f}")
    print(f"代谢物浓度范围: {analysis_df[u_shape_metabolite].min():.3f} - {analysis_df[u_shape_metabolite].max():.3f}")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('代谢物与LVEF的U型关系分析', fontsize=16, fontweight='bold')
    
    # 1. 散点图显示U型关系
    x = analysis_df['LVEF']
    y = analysis_df[u_shape_metabolite]
    
    # 拟合线性和二次模型
    linear_coeffs = np.polyfit(x, y, 1)
    quadratic_coeffs = np.polyfit(x, y, 2)
    
    x_smooth = np.linspace(x.min(), x.max(), 100)
    linear_fit = np.poly1d(linear_coeffs)(x_smooth)
    quadratic_fit = np.poly1d(quadratic_coeffs)(x_smooth)
    
    axes[0, 0].scatter(x, y, alpha=0.6, s=30, color='lightblue', edgecolor='navy', linewidth=0.5)
    axes[0, 0].plot(x_smooth, linear_fit, 'r--', label=f'线性拟合 (R²={stats.pearsonr(x, y)[0]**2:.3f})', linewidth=2)
    axes[0, 0].plot(x_smooth, quadratic_fit, 'g-', label=f'二次拟合 (R²={lvef_results[lvef_results["metabolite"]==u_shape_metabolite]["quadratic_r2"].iloc[0]:.3f})', linewidth=2)
    axes[0, 0].set_xlabel('LVEF (%)')
    axes[0, 0].set_ylabel('代谢物浓度')
    axes[0, 0].set_title('代谢物浓度 vs LVEF')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 按非心血管死亡分组的散点图
    death_0 = analysis_df[analysis_df['NonCV_death'] == 0]
    death_1 = analysis_df[analysis_df['NonCV_death'] == 1]
    
    axes[0, 1].scatter(death_0['LVEF'], death_0[u_shape_metabolite], 
                      alpha=0.6, s=30, color='blue', label=f'非心血管死亡=0 (n={len(death_0)})')
    axes[0, 1].scatter(death_1['LVEF'], death_1[u_shape_metabolite], 
                      alpha=0.8, s=50, color='red', marker='^', label=f'非心血管死亡=1 (n={len(death_1)})')
    axes[0, 1].plot(x_smooth, quadratic_fit, 'g-', alpha=0.7, linewidth=2)
    axes[0, 1].set_xlabel('LVEF (%)')
    axes[0, 1].set_ylabel('代谢物浓度')
    axes[0, 1].set_title('按非心血管死亡分组')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 按HF类型分组的散点图
    hfnef = analysis_df[analysis_df['HFsnEF'] == 0]
    hfsnef = analysis_df[analysis_df['HFsnEF'] == 1]
    
    axes[1, 0].scatter(hfnef['LVEF'], hfnef[u_shape_metabolite], 
                      alpha=0.6, s=30, color='orange', label=f'HFnEF (n={len(hfnef)})')
    axes[1, 0].scatter(hfsnef['LVEF'], hfsnef[u_shape_metabolite], 
                      alpha=0.6, s=30, color='purple', label=f'HFsnEF (n={len(hfsnef)})')
    axes[1, 0].plot(x_smooth, quadratic_fit, 'g-', alpha=0.7, linewidth=2)
    axes[1, 0].set_xlabel('LVEF (%)')
    axes[1, 0].set_ylabel('代谢物浓度')
    axes[1, 0].set_title('按心衰类型分组')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. LVEF分段分析
    # 将LVEF分为三段：低、中、高
    lvef_tertiles = np.percentile(analysis_df['LVEF'], [33.33, 66.67])
    analysis_df['LVEF_group'] = pd.cut(analysis_df['LVEF'], 
                                      bins=[-np.inf, lvef_tertiles[0], lvef_tertiles[1], np.inf],
                                      labels=['低LVEF', '中LVEF', '高LVEF'])
    
    # 箱线图
    box_data = [analysis_df[analysis_df['LVEF_group'] == group][u_shape_metabolite].dropna() 
                for group in ['低LVEF', '中LVEF', '高LVEF']]
    
    bp = axes[1, 1].boxplot(box_data, labels=['低LVEF', '中LVEF', '高LVEF'], patch_artist=True)
    colors = ['lightcoral', 'lightblue', 'lightgreen']
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    axes[1, 1].set_ylabel('代谢物浓度')
    axes[1, 1].set_title('LVEF分组的代谢物浓度分布')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加统计检验结果
    from scipy.stats import kruskal
    stat, p_value = kruskal(*box_data)
    axes[1, 1].text(0.5, 0.95, f'Kruskal-Wallis检验: P={p_value:.4f}', 
                   transform=axes[1, 1].transAxes, ha='center', va='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('u_shape_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 统计分析
    print(f"\n统计分析结果:")
    print("-" * 50)
    
    # 线性相关
    linear_corr, linear_p = stats.pearsonr(analysis_df['LVEF'], analysis_df[u_shape_metabolite])
    print(f"线性相关系数: {linear_corr:.4f} (P={linear_p:.4f})")
    
    # 二次模型检验
    lvef_result = lvef_results[lvef_results['metabolite'] == u_shape_metabolite].iloc[0]
    print(f"二次模型R²: {lvef_result['quadratic_r2']:.4f}")
    print(f"U型检验P值: {lvef_result['f_p']:.4f}")
    
    # 分组比较
    print(f"\nLVEF分组的代谢物浓度 (均值±标准差):")
    for group in ['低LVEF', '中LVEF', '高LVEF']:
        group_data = analysis_df[analysis_df['LVEF_group'] == group][u_shape_metabolite]
        print(f"{group}: {group_data.mean():.4f} ± {group_data.std():.4f} (n={len(group_data)})")
    
    # 非心血管死亡在不同LVEF组的分布
    print(f"\n非心血管死亡在不同LVEF组的分布:")
    crosstab = pd.crosstab(analysis_df['LVEF_group'], analysis_df['NonCV_death'], margins=True)
    print(crosstab)
    
    # 计算每组的非心血管死亡率
    print(f"\n各LVEF组的非心血管死亡率:")
    for group in ['低LVEF', '中LVEF', '高LVEF']:
        group_data = analysis_df[analysis_df['LVEF_group'] == group]
        death_rate = group_data['NonCV_death'].mean() * 100
        print(f"{group}: {death_rate:.2f}% ({group_data['NonCV_death'].sum()}/{len(group_data)})")
    
    print(f"\n结论:")
    print("-" * 50)
    print(f"• 该代谢物与LVEF呈现显著的U型关系 (P={lvef_result['f_p']:.4f})")
    print(f"• 在LVEF的两端（低值和高值），代谢物浓度相对较高")
    print(f"• 这种U型关系可能反映了不同LVEF水平下的代谢状态差异")
    print(f"• 需要进一步研究验证这种关系的生物学机制")

if __name__ == "__main__":
    analyze_u_shape_relationship()
