#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多因素Cox回归分析脚本

作者: AI Assistant
日期: 2025-07-30
描述: 基于单因素Cox分析结果，进行多因素Cox回归分析并生成森林图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from lifelines import CoxPHFitter
import warnings
import os
from datetime import datetime
import glob

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def find_latest_results_file():
    """
    查找最新的Cox分析结果文件
    """
    pattern = "cox_analysis_results_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到Cox分析结果文件")
    
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def load_data_and_results():
    """
    加载原始数据和单因素分析结果
    """
    print("正在加载数据和分析结果...")
    
    # 加载原始数据
    try:
        data = pd.read_csv("merged_metabolomics_clinical_final.csv", encoding='utf-8')
    except UnicodeDecodeError:
        data = pd.read_csv("merged_metabolomics_clinical_final.csv", encoding='gbk')
    
    # 预处理数据
    data['event'] = pd.to_numeric(data['NonCV_death'], errors='coerce')
    data['duration'] = pd.to_numeric(data['OS'], errors='coerce')
    data = data.dropna(subset=['event', 'duration'])
    data = data[data['duration'] > 0]
    data['event'] = data['event'].astype(int)
    
    # 加载单因素分析结果
    results_file = find_latest_results_file()
    results = pd.read_csv(results_file)
    
    print(f"数据加载完成：{data.shape[0]} 行，{data.shape[1]} 列")
    print(f"单因素分析结果：{len(results)} 个变量")
    
    return data, results

def select_variables_for_multivariate(results, exclude_vars=None, p_threshold=0.05):
    """
    选择用于多因素分析的变量
    
    Parameters:
    results (pd.DataFrame): 单因素分析结果
    exclude_vars (list): 要排除的变量列表
    p_threshold (float): p值阈值
    
    Returns:
    list: 选中的变量列表
    """
    if exclude_vars is None:
        exclude_vars = []
    
    print(f"正在选择多因素分析变量（p < {p_threshold}）...")
    
    # 筛选显著变量
    significant_vars = results[
        (results['P_value'] < p_threshold) & 
        (results['Status'] == '成功')
    ].copy()
    
    # 排除指定变量
    significant_vars = significant_vars[
        ~significant_vars['Variable'].isin(exclude_vars)
    ]
    
    selected_vars = significant_vars['Variable'].tolist()
    
    print(f"排除变量：{exclude_vars}")
    print(f"选中 {len(selected_vars)} 个变量用于多因素分析")
    
    return selected_vars, significant_vars

def prepare_multivariate_data(data, selected_vars):
    """
    准备多因素分析数据
    """
    print("正在准备多因素分析数据...")
    
    # 选择需要的列
    analysis_cols = ['duration', 'event'] + selected_vars
    analysis_data = data[analysis_cols].copy()
    
    # 移除缺失值
    initial_count = len(analysis_data)
    analysis_data = analysis_data.dropna()
    removed_count = initial_count - len(analysis_data)
    
    if removed_count > 0:
        print(f"移除了 {removed_count} 条包含缺失值的记录")
    
    print(f"多因素分析数据：{analysis_data.shape[0]} 行，{len(selected_vars)} 个变量")
    
    # 检查变量的变异性
    low_variance_vars = []
    for var in selected_vars:
        if analysis_data[var].nunique() < 2:
            low_variance_vars.append(var)
    
    if low_variance_vars:
        print(f"移除无变异的变量：{low_variance_vars}")
        analysis_data = analysis_data.drop(columns=low_variance_vars)
        selected_vars = [var for var in selected_vars if var not in low_variance_vars]
    
    return analysis_data, selected_vars

def perform_multivariate_cox_analysis(analysis_data):
    """
    执行多因素Cox回归分析
    """
    print("正在执行多因素Cox回归分析...")
    
    try:
        # 创建Cox模型
        cph = CoxPHFitter()
        
        # 拟合模型
        cph.fit(analysis_data, duration_col='duration', event_col='event')
        
        # 获取结果摘要
        summary = cph.summary
        
        print("多因素Cox回归分析完成")
        try:
            print(f"AIC (partial)：{cph.AIC_partial_:.2f}")
        except:
            print("AIC信息不可用")
        print(f"Concordance Index：{cph.concordance_index_:.3f}")
        
        return cph, summary
        
    except Exception as e:
        print(f"多因素Cox回归分析失败：{str(e)}")
        raise

def format_multivariate_results(summary, univariate_results):
    """
    格式化多因素分析结果
    """
    print("正在格式化多因素分析结果...")
    
    # 创建结果DataFrame
    results = []
    
    for var in summary.index:
        # 获取多因素结果
        hr = summary.loc[var, 'exp(coef)']
        hr_lower = summary.loc[var, 'exp(coef) lower 95%']
        hr_upper = summary.loc[var, 'exp(coef) upper 95%']
        p_value = summary.loc[var, 'p']
        coef = summary.loc[var, 'coef']
        se = summary.loc[var, 'se(coef)']
        
        # 获取单因素结果用于比较
        univar_result = univariate_results[univariate_results['Variable'] == var]
        if len(univar_result) > 0:
            univar_hr = univar_result.iloc[0]['HR']
            univar_p = univar_result.iloc[0]['P_value']
            var_type = univar_result.iloc[0]['Variable_Type']
        else:
            univar_hr = np.nan
            univar_p = np.nan
            var_type = '未知'
        
        # 格式化p值
        if p_value < 0.001:
            p_formatted = '<0.001'
            significance = '***'
        elif p_value < 0.01:
            p_formatted = f'{p_value:.3f}'
            significance = '**'
        elif p_value < 0.05:
            p_formatted = f'{p_value:.3f}'
            significance = '*'
        else:
            p_formatted = f'{p_value:.3f}'
            significance = ''
        
        results.append({
            'Variable': var,
            'Variable_Type': var_type,
            'Multivariate_HR': hr,
            'Multivariate_HR_Lower': hr_lower,
            'Multivariate_HR_Upper': hr_upper,
            'Multivariate_P_value': p_value,
            'Multivariate_P_formatted': p_formatted,
            'Significance': significance,
            'Coefficient': coef,
            'SE': se,
            'Univariate_HR': univar_hr,
            'Univariate_P_value': univar_p,
            'HR_95CI': f"{hr:.3f} ({hr_lower:.3f}-{hr_upper:.3f})"
        })
    
    results_df = pd.DataFrame(results)
    
    # 按p值排序
    results_df = results_df.sort_values('Multivariate_P_value')
    
    return results_df

def create_forest_plot(results_df, title="多因素Cox回归分析森林图"):
    """
    创建森林图
    """
    print("正在创建森林图...")
    
    # 准备数据
    n_vars = len(results_df)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, max(8, n_vars * 0.5)))
    
    # 设置y轴位置
    y_pos = np.arange(n_vars)
    
    # 绘制HR点和置信区间
    hrs = results_df['Multivariate_HR'].values
    hr_lowers = results_df['Multivariate_HR_Lower'].values
    hr_uppers = results_df['Multivariate_HR_Upper'].values
    
    # 计算误差条
    errors = np.array([hrs - hr_lowers, hr_uppers - hrs])
    
    # 根据显著性设置颜色
    colors = []
    for _, row in results_df.iterrows():
        if row['Multivariate_P_value'] < 0.001:
            colors.append('red')
        elif row['Multivariate_P_value'] < 0.01:
            colors.append('orange')
        elif row['Multivariate_P_value'] < 0.05:
            colors.append('blue')
        else:
            colors.append('gray')
    
    # 绘制点和误差条
    for i, (hr, color) in enumerate(zip(hrs, colors)):
        ax.errorbar(hr, y_pos[i], xerr=[[hrs[i] - hr_lowers[i]], [hr_uppers[i] - hrs[i]]], 
                   fmt='o', color=color, capsize=5, capthick=2, markersize=8)
    
    # 添加HR=1的参考线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.7, linewidth=1)
    
    # 设置y轴标签
    var_labels = []
    for _, row in results_df.iterrows():
        var_name = row['Variable']
        if len(var_name) > 40:
            var_name = var_name[:37] + '...'
        var_labels.append(f"{var_name} ({row['Variable_Type']})")
    
    ax.set_yticks(y_pos)
    ax.set_yticklabels(var_labels)
    
    # 设置x轴
    ax.set_xlabel('风险比 (HR)', fontsize=12)
    ax.set_xscale('log')
    
    # 设置标题
    ax.set_title(title, fontsize=14, fontweight='bold')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 在右侧添加HR值和p值文本
    for i, (_, row) in enumerate(results_df.iterrows()):
        hr_text = f"HR: {row['HR_95CI']}"
        p_text = f"p: {row['Multivariate_P_formatted']}"
        
        # 计算文本位置
        x_max = ax.get_xlim()[1]
        ax.text(x_max * 1.1, y_pos[i], f"{hr_text}\n{p_text}", 
               verticalalignment='center', fontsize=9)
    
    # 扩展x轴范围以容纳文本
    xlim = ax.get_xlim()
    ax.set_xlim(xlim[0], xlim[1] * 2)
    
    # 添加图例
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=8, label='p < 0.001'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='orange', markersize=8, label='p < 0.01'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='p < 0.05'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='gray', markersize=8, label='p ≥ 0.05')
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    return fig

def main():
    """
    主函数
    """
    print("=" * 80)
    print("多因素Cox回归分析")
    print("=" * 80)
    
    try:
        # 1. 加载数据和结果
        data, univariate_results = load_data_and_results()
        
        # 2. 定义要排除的变量
        exclude_vars = [
            'Death_in_hospital',
            'Inhospital_vasoactive_drug', 
            'Inhospital_mehanical_ventilation',
            'AR'
        ]
        
        # 3. 选择用于多因素分析的变量
        selected_vars, significant_results = select_variables_for_multivariate(
            univariate_results, exclude_vars=exclude_vars, p_threshold=0.05
        )
        
        if len(selected_vars) == 0:
            print("没有符合条件的变量进行多因素分析")
            return
        
        # 4. 准备多因素分析数据
        analysis_data, final_vars = prepare_multivariate_data(data, selected_vars)
        
        if len(final_vars) < 2:
            print("可用于多因素分析的变量太少")
            return
        
        # 5. 执行多因素Cox回归分析
        cph_model, summary = perform_multivariate_cox_analysis(analysis_data)
        
        # 6. 格式化结果
        results_df = format_multivariate_results(summary, univariate_results)
        
        # 7. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"multivariate_cox_results_{timestamp}.csv"
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
        
        # 8. 创建森林图
        fig = create_forest_plot(results_df)
        
        # 保存森林图
        forest_plot_file = f"multivariate_cox_forest_plot_{timestamp}.png"
        fig.savefig(forest_plot_file, dpi=300, bbox_inches='tight')
        
        # 9. 输出结果摘要
        print("\n" + "=" * 80)
        print("多因素Cox回归分析完成！")
        print("=" * 80)
        
        print(f"纳入变量数：{len(final_vars)}")
        print(f"分析样本数：{len(analysis_data)}")
        print(f"事件数：{analysis_data['event'].sum()}")
        try:
            print(f"模型AIC (partial)：{cph_model.AIC_partial_:.2f}")
        except:
            print("模型AIC信息不可用")
        print(f"一致性指数：{cph_model.concordance_index_:.3f}")
        
        significant_multivar = len(results_df[results_df['Multivariate_P_value'] < 0.05])
        print(f"多因素分析中显著变量数：{significant_multivar}")
        
        print(f"\n结果文件：{results_file}")
        print(f"森林图文件：{forest_plot_file}")
        
        # 显示前10个最显著的变量
        print(f"\n前10个最显著的变量：")
        top_vars = results_df.head(10)
        for i, (_, row) in enumerate(top_vars.iterrows(), 1):
            print(f"{i:2d}. {row['Variable'][:50]} (HR={row['Multivariate_HR']:.3f}, p={row['Multivariate_P_formatted']})")
        
        plt.show()
        
    except Exception as e:
        print(f"分析过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
