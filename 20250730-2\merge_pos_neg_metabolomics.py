#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并正负离子模式代谢组数据的脚本
- data_all.diff_NEG.tsv: 负离子模式结果
- data_all.diff_POS.tsv: 正离子模式结果
根据CPD_Name合并相同的代谢物，并在CPD_ID上添加离子模式标识
"""

import pandas as pd
import numpy as np
import os

def load_metabolomics_data(file_path, ion_mode):
    """
    加载代谢组数据并添加离子模式标识
    
    Parameters:
    file_path: 文件路径
    ion_mode: 离子模式 ('NEG' 或 'POS')
    
    Returns:
    DataFrame: 处理后的数据
    """
    print(f"正在加载{ion_mode}离子模式数据: {file_path}")
    
    # 读取TSV文件
    df = pd.read_csv(file_path, sep='\t')
    
    print(f"{ion_mode}离子模式数据形状: {df.shape}")
    print(f"代谢物数量: {len(df)}")
    
    # 在CPD_ID上添加离子模式标识
    df['CPD_ID_with_mode'] = df['CPD_ID'] + '_' + ion_mode
    
    # 添加离子模式列
    df['Ion_Mode'] = ion_mode
    
    return df

def merge_by_metabolite_name(neg_df, pos_df):
    """
    根据CPD_Name合并正负离子模式数据
    
    Parameters:
    neg_df: 负离子模式数据
    pos_df: 正离子模式数据
    
    Returns:
    DataFrame: 合并后的数据
    """
    print("正在根据CPD_Name合并数据...")
    
    # 检查共同的代谢物
    neg_metabolites = set(neg_df['CPD_Name'])
    pos_metabolites = set(pos_df['CPD_Name'])
    
    common_metabolites = neg_metabolites & pos_metabolites
    neg_only = neg_metabolites - pos_metabolites
    pos_only = pos_metabolites - neg_metabolites
    
    print(f"负离子模式独有代谢物: {len(neg_only)}")
    print(f"正离子模式独有代谢物: {len(pos_only)}")
    print(f"共同代谢物: {len(common_metabolites)}")
    print(f"总代谢物数: {len(neg_metabolites | pos_metabolites)}")
    
    # 合并数据
    # 使用外连接保留所有代谢物
    merged_df = pd.concat([neg_df, pos_df], ignore_index=True)
    
    # 按CPD_Name排序，相同代谢物的不同离子模式会相邻
    merged_df = merged_df.sort_values(['CPD_Name', 'Ion_Mode']).reset_index(drop=True)
    
    print(f"合并后数据形状: {merged_df.shape}")
    
    return merged_df, common_metabolites, neg_only, pos_only

def create_summary_report(merged_df, common_metabolites, neg_only, pos_only):
    """
    创建合并结果的汇总报告
    """
    print("\n=== 数据合并汇总报告 ===")
    
    # 基本统计
    total_records = len(merged_df)
    neg_records = len(merged_df[merged_df['Ion_Mode'] == 'NEG'])
    pos_records = len(merged_df[merged_df['Ion_Mode'] == 'POS'])
    
    print(f"总记录数: {total_records}")
    print(f"负离子模式记录数: {neg_records}")
    print(f"正离子模式记录数: {pos_records}")
    
    # 代谢物统计
    unique_metabolites = merged_df['CPD_Name'].nunique()
    print(f"\n代谢物统计:")
    print(f"唯一代谢物总数: {unique_metabolites}")
    print(f"共同代谢物数: {len(common_metabolites)}")
    print(f"仅在负离子模式中的代谢物数: {len(neg_only)}")
    print(f"仅在正离子模式中的代谢物数: {len(pos_only)}")
    
    # 显示一些共同代谢物的例子
    if common_metabolites:
        print(f"\n共同代谢物示例 (前5个):")
        common_list = sorted(list(common_metabolites))[:5]
        for metabolite in common_list:
            print(f"  - {metabolite}")
    
    # 检查数据完整性
    print(f"\n数据完整性检查:")
    print(f"CPD_ID缺失值: {merged_df['CPD_ID'].isnull().sum()}")
    print(f"CPD_Name缺失值: {merged_df['CPD_Name'].isnull().sum()}")
    print(f"Ion_Mode缺失值: {merged_df['Ion_Mode'].isnull().sum()}")
    
    return {
        'total_records': total_records,
        'neg_records': neg_records,
        'pos_records': pos_records,
        'unique_metabolites': unique_metabolites,
        'common_metabolites': len(common_metabolites),
        'neg_only': len(neg_only),
        'pos_only': len(pos_only)
    }

def save_merged_data(merged_df, output_file):
    """
    保存合并后的数据
    """
    print(f"\n正在保存合并后的数据到: {output_file}")
    
    # 保存为TSV文件，保持原始格式
    merged_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
    
    print(f"数据已成功保存")
    print(f"文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")

def main():
    """
    主函数
    """
    # 文件路径
    neg_file = "data_all.diff_NEG.tsv"
    pos_file = "data_all.diff_POS.tsv"
    output_file = "merged_pos_neg_metabolomics.tsv"
    
    # 检查文件是否存在
    if not os.path.exists(neg_file):
        print(f"错误: 找不到负离子模式数据文件 {neg_file}")
        return
    
    if not os.path.exists(pos_file):
        print(f"错误: 找不到正离子模式数据文件 {pos_file}")
        return
    
    try:
        # 加载数据
        neg_df = load_metabolomics_data(neg_file, 'NEG')
        pos_df = load_metabolomics_data(pos_file, 'POS')
        
        # 显示数据预览
        print(f"\n=== 负离子模式数据预览 ===")
        print(f"列名: {list(neg_df.columns[:10])}...")
        print(f"前3行CPD_Name: {list(neg_df['CPD_Name'].head(3))}")
        
        print(f"\n=== 正离子模式数据预览 ===")
        print(f"列名: {list(pos_df.columns[:10])}...")
        print(f"前3行CPD_Name: {list(pos_df['CPD_Name'].head(3))}")
        
        # 合并数据
        merged_df, common_metabolites, neg_only, pos_only = merge_by_metabolite_name(neg_df, pos_df)
        
        # 创建汇总报告
        summary = create_summary_report(merged_df, common_metabolites, neg_only, pos_only)
        
        # 保存结果
        save_merged_data(merged_df, output_file)
        
        # 显示最终结果预览
        print(f"\n=== 合并结果预览 ===")
        print(f"前5行数据:")
        print(merged_df[['CPD_ID_with_mode', 'CPD_Name', 'Ion_Mode']].head())
        
        print(f"\n=== 合并完成 ===")
        print(f"输出文件: {output_file}")
        print(f"总记录数: {summary['total_records']}")
        print(f"唯一代谢物数: {summary['unique_metabolites']}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
