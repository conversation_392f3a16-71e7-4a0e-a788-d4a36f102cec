#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import matplotlib.ticker as ticker

def create_test_forest_plot():
    """创建测试森林图来验证横坐标格式"""
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'protein': ['Test1', 'Test2', 'Test3'],
        'hazard_ratio': [2.122, 2.201, 2.352],
        'ci_lower': [1.525, 1.524, 1.569],
        'ci_upper': [2.953, 3.178, 3.525],
        'p_value': [0.000008, 0.000025, 0.000035]
    })
    
    print("测试数据:")
    print(test_data)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 设置y轴位置
    y_positions = range(len(test_data))
    
    # 绘制森林图
    for i, (idx, row) in enumerate(test_data.iterrows()):
        y = len(test_data) - 1 - i  # 倒序排列
        
        # 绘制置信区间线
        ax.plot([row['ci_lower'], row['ci_upper']], [y, y], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制置信区间端点
        ax.plot([row['ci_lower'], row['ci_lower']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        ax.plot([row['ci_upper'], row['ci_upper']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制HR点
        ax.scatter(row['hazard_ratio'], y, s=150, color='#1E88E5', alpha=0.8, 
                  zorder=3, marker='s', edgecolors='black', linewidth=1)
    
    # 添加HR=1的参考线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.8, linewidth=2, zorder=1)
    
    # 设置y轴标签
    ax.set_yticks(range(len(test_data)))
    ax.set_yticklabels(reversed(test_data['protein'].tolist()), fontsize=10)
    
    # 设置x轴 - 使用对数刻度
    ax.set_xscale('log')
    
    # 设置x轴范围
    all_values = list(test_data['hazard_ratio']) + list(test_data['ci_lower']) + list(test_data['ci_upper'])
    all_values = [v for v in all_values if np.isfinite(v) and v > 0]
    x_min = max(0.1, min(all_values) * 0.8)
    x_max = min(max(all_values) * 1.5, 10)
    ax.set_xlim(x_min, x_max)
    
    # 设置刻度位置
    ticks = [0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4]
    ticks = [t for t in ticks if x_min <= t <= x_max]
    ax.set_xticks(ticks)
    
    # 手动设置刻度标签，确保不显示指数
    tick_labels = []
    for tick in ticks:
        if tick == int(tick):
            tick_labels.append(f'{int(tick)}')
        else:
            tick_labels.append(f'{tick:.1f}')
    
    ax.set_xticklabels(tick_labels)
    
    # 设置标签
    ax.set_xlabel('Hazard Ratio (95% CI)', fontsize=12, fontweight='bold')
    ax.set_title('Test Forest Plot - No Scientific Notation', fontsize=14, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    plt.tight_layout()
    plt.savefig('test_forest_plot.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print("测试森林图已保存到: test_forest_plot.png")

if __name__ == "__main__":
    create_test_forest_plot()
