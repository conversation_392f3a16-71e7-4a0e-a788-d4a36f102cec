#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并代谢组数据和临床数据的脚本
- 代谢组数据：processed_metabolomics_data.tsv
- 临床数据：203HFsnEF_800d.xlsx
按照Record_ID合并两个数据集
"""

import pandas as pd
import numpy as np
import os

def load_metabolomics_data(file_path):
    """
    加载代谢组数据并转置
    
    Parameters:
    file_path: 代谢组数据文件路径
    
    Returns:
    DataFrame: 转置后的代谢组数据，样本为行，代谢物为列
    """
    print(f"正在加载代谢组数据: {file_path}")
    
    # 读取TSV文件
    df = pd.read_csv(file_path, sep='\t')
    
    print(f"代谢组数据原始形状: {df.shape}")
    print(f"代谢物数量: {len(df)}")
    
    # 获取样本列（排除基本信息列）
    info_columns = ['CPD_ID', 'CPD_Name', 'CPD_ID_with_mode', 'Ion_Mode']
    sample_columns = [col for col in df.columns if col not in info_columns]
    
    print(f"样本列数: {len(sample_columns)}")
    print(f"样本ID示例: {sample_columns[:10]}")
    
    # 创建代谢物标识符（结合代谢物名称和离子模式）
    df['Metabolite_ID'] = df['CPD_Name'] + '_' + df['Ion_Mode']
    
    # 选择样本数据和代谢物标识符
    metabolite_data = df[['Metabolite_ID'] + sample_columns]
    
    # 转置数据：样本为行，代谢物为列
    metabolite_data_transposed = metabolite_data.set_index('Metabolite_ID').T
    
    # 重置索引，将样本ID作为一列
    metabolite_data_transposed.reset_index(inplace=True)
    metabolite_data_transposed.rename(columns={'index': 'Record_ID'}, inplace=True)
    
    print(f"转置后代谢组数据形状: {metabolite_data_transposed.shape}")
    print(f"样本数: {len(metabolite_data_transposed)}")
    print(f"代谢物数: {len(metabolite_data_transposed.columns) - 1}")
    
    return metabolite_data_transposed

def load_clinical_data(file_path):
    """
    加载临床数据
    
    Parameters:
    file_path: 临床数据文件路径
    
    Returns:
    DataFrame: 临床数据
    """
    print(f"\n正在加载临床数据: {file_path}")
    
    # 读取Excel文件
    df = pd.read_excel(file_path)
    
    print(f"临床数据形状: {df.shape}")
    print(f"样本数: {len(df)}")
    print(f"临床变量数: {len(df.columns) - 1}")
    print(f"前5列名: {list(df.columns[:5])}")
    
    return df

def merge_datasets(metabolomics_df, clinical_df):
    """
    按照Record_ID合并代谢组数据和临床数据
    
    Parameters:
    metabolomics_df: 转置后的代谢组数据
    clinical_df: 临床数据
    
    Returns:
    DataFrame: 合并后的数据
    """
    print(f"\n正在合并数据集...")
    
    # 确保Record_ID列的数据类型一致
    metabolomics_df['Record_ID'] = metabolomics_df['Record_ID'].astype(str)
    clinical_df['Record_ID'] = clinical_df['Record_ID'].astype(str)
    
    print(f"代谢组数据样本数: {len(metabolomics_df)}")
    print(f"临床数据样本数: {len(clinical_df)}")
    
    # 检查共同的Record_ID
    metabolomics_ids = set(metabolomics_df['Record_ID'])
    clinical_ids = set(clinical_df['Record_ID'])
    
    common_ids = metabolomics_ids & clinical_ids
    metabolomics_only = metabolomics_ids - clinical_ids
    clinical_only = clinical_ids - metabolomics_ids
    
    print(f"代谢组数据独有样本: {len(metabolomics_only)}")
    print(f"临床数据独有样本: {len(clinical_only)}")
    print(f"共同样本: {len(common_ids)}")
    
    if len(metabolomics_only) > 0:
        print(f"代谢组独有样本示例: {list(metabolomics_only)[:5]}")
    if len(clinical_only) > 0:
        print(f"临床数据独有样本示例: {list(clinical_only)[:5]}")
    
    # 内连接合并，只保留两个数据集中都存在的样本
    merged_df = pd.merge(
        clinical_df, 
        metabolomics_df, 
        on='Record_ID', 
        how='inner'
    )
    
    print(f"合并后数据形状: {merged_df.shape}")
    print(f"成功合并的样本数: {len(merged_df)}")
    
    return merged_df

def save_merged_data(merged_df, output_file):
    """
    保存合并后的数据
    
    Parameters:
    merged_df: 合并后的数据
    output_file: 输出文件路径
    """
    print(f"\n正在保存合并后的数据到: {output_file}")
    
    # 保存为TSV文件
    merged_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
    
    print(f"数据已成功保存")
    print(f"最终数据形状: {merged_df.shape}")
    
    # 显示列信息
    print(f"总列数: {len(merged_df.columns)}")
    print(f"第一列: {merged_df.columns[0]}")
    print(f"前10列: {list(merged_df.columns[:10])}")
    print(f"后5列: {list(merged_df.columns[-5:])}")

def create_summary_report(merged_df, metabolomics_df, clinical_df):
    """
    创建合并结果的汇总报告
    """
    print(f"\n=== 数据合并汇总报告 ===")
    
    # 基本统计
    print(f"最终合并数据:")
    print(f"  样本数: {len(merged_df)}")
    print(f"  总变量数: {len(merged_df.columns)}")
    print(f"  临床变量数: {len(clinical_df.columns) - 1}")
    print(f"  代谢物变量数: {len(metabolomics_df.columns) - 1}")
    
    # 数据类型检查
    print(f"\n数据类型检查:")
    print(f"  Record_ID列类型: {merged_df['Record_ID'].dtype}")
    print(f"  Record_ID缺失值: {merged_df['Record_ID'].isnull().sum()}")
    print(f"  Record_ID重复值: {merged_df['Record_ID'].duplicated().sum()}")
    
    # 显示样本ID示例
    print(f"\n样本ID示例:")
    print(f"  前5个: {list(merged_df['Record_ID'].head())}")
    print(f"  后5个: {list(merged_df['Record_ID'].tail())}")

def main():
    """
    主函数
    """
    # 文件路径
    metabolomics_file = "processed_metabolomics_data.tsv"
    clinical_file = "203HFsnEF_800d.xlsx"
    output_file = "merged_metabolomics_clinical_data.tsv"
    
    # 检查文件是否存在
    if not os.path.exists(metabolomics_file):
        print(f"错误: 找不到代谢组数据文件 {metabolomics_file}")
        return
    
    if not os.path.exists(clinical_file):
        print(f"错误: 找不到临床数据文件 {clinical_file}")
        return
    
    try:
        # 加载数据
        metabolomics_df = load_metabolomics_data(metabolomics_file)
        clinical_df = load_clinical_data(clinical_file)
        
        # 显示数据预览
        print(f"\n=== 代谢组数据预览 ===")
        print(f"前3行前5列:")
        print(metabolomics_df.iloc[:3, :5])
        
        print(f"\n=== 临床数据预览 ===")
        print(f"前3行前5列:")
        print(clinical_df.iloc[:3, :5])
        
        # 合并数据
        merged_df = merge_datasets(metabolomics_df, clinical_df)
        
        # 保存结果
        save_merged_data(merged_df, output_file)
        
        # 创建汇总报告
        create_summary_report(merged_df, metabolomics_df, clinical_df)
        
        print(f"\n=== 合并完成 ===")
        print(f"输出文件: {output_file}")
        print(f"合并成功! 第一列是Record_ID")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
