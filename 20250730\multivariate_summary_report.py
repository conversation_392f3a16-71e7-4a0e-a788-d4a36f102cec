#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多因素Cox分析结果摘要报告生成脚本

作者: AI Assistant
日期: 2025-07-30
描述: 基于多因素Cox分析结果生成易读的摘要报告
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import glob

def find_latest_multivariate_results():
    """
    查找最新的多因素Cox分析结果文件
    """
    pattern = "multivariate_cox_results_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到多因素Cox分析结果文件")
    
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def generate_multivariate_summary_report(results_file):
    """
    生成多因素分析摘要报告
    """
    print(f"正在读取多因素分析结果文件：{results_file}")
    
    # 读取结果
    results = pd.read_csv(results_file)
    
    # 基本统计
    total_vars = len(results)
    
    # 显著性分析
    significant_001 = len(results[results['Multivariate_P_value'] < 0.001])
    significant_01 = len(results[results['Multivariate_P_value'] < 0.01])
    significant_05 = len(results[results['Multivariate_P_value'] < 0.05])
    
    # 按变量类型分组
    metabolite_results = results[results['Variable_Type'] == '代谢物']
    clinical_results = results[results['Variable_Type'] == '临床']
    
    metabolite_significant = len(metabolite_results[metabolite_results['Multivariate_P_value'] < 0.05])
    clinical_significant = len(clinical_results[clinical_results['Multivariate_P_value'] < 0.05])
    
    # 保护性因子和危险因子
    protective_factors = results[
        (results['Multivariate_P_value'] < 0.05) & 
        (results['Multivariate_HR'] < 1)
    ]
    risk_factors = results[
        (results['Multivariate_P_value'] < 0.05) & 
        (results['Multivariate_HR'] > 1)
    ]
    
    # 生成报告
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    report_lines = []
    
    report_lines.append("=" * 80)
    report_lines.append("多因素Cox回归分析 - 摘要报告")
    report_lines.append("=" * 80)
    report_lines.append(f"报告生成时间：{timestamp}")
    report_lines.append(f"数据来源：{results_file}")
    report_lines.append("")
    
    # 排除的变量说明
    report_lines.append("【分析说明】")
    report_lines.append("本次多因素分析排除了以下变量：")
    report_lines.append("  - Death_in_hospital (院内死亡)")
    report_lines.append("  - Inhospital_vasoactive_drug (院内血管活性药物)")
    report_lines.append("  - Inhospital_mehanical_ventilation (院内机械通气)")
    report_lines.append("  - AR (主动脉瓣反流)")
    report_lines.append("")
    
    # 总体统计
    report_lines.append("【总体统计】")
    report_lines.append(f"  纳入多因素分析的变量数：{total_vars}")
    report_lines.append(f"  代谢物变量：{len(metabolite_results)} 个")
    report_lines.append(f"  临床变量：{len(clinical_results)} 个")
    report_lines.append("")
    
    # 显著性统计
    report_lines.append("【多因素分析显著性统计】")
    report_lines.append(f"  p < 0.001：{significant_001} 个变量")
    report_lines.append(f"  p < 0.01：{significant_01} 个变量")
    report_lines.append(f"  p < 0.05：{significant_05} 个变量")
    if total_vars > 0:
        report_lines.append(f"  显著变量比例：{significant_05/total_vars*100:.1f}%")
    report_lines.append("")
    
    # 按变量类型统计
    report_lines.append("【按变量类型统计】")
    report_lines.append(f"  代谢物变量显著：{metabolite_significant} 个")
    report_lines.append(f"  临床变量显著：{clinical_significant} 个")
    report_lines.append("")
    
    # 单因素vs多因素比较
    report_lines.append("【单因素 vs 多因素分析比较】")
    report_lines.append("变量名称                                    单因素HR  单因素p值  多因素HR  多因素p值")
    report_lines.append("-" * 80)
    
    # 显示前15个变量的比较
    top_vars = results.head(15)
    for _, row in top_vars.iterrows():
        var_name = row['Variable'][:40]
        univar_hr = f"{row['Univariate_HR']:.3f}" if not pd.isna(row['Univariate_HR']) else "NA"
        univar_p = f"{row['Univariate_P_value']:.3f}" if not pd.isna(row['Univariate_P_value']) else "NA"
        multivar_hr = f"{row['Multivariate_HR']:.3f}"
        multivar_p = row['Multivariate_P_formatted']
        
        report_lines.append(f"{var_name:<40} {univar_hr:<9} {univar_p:<10} {multivar_hr:<9} {multivar_p}")
    
    report_lines.append("")
    
    # 显著的保护性因子
    if len(protective_factors) > 0:
        report_lines.append("【显著的保护性因子（HR < 1, p < 0.05）】")
        report_lines.append("变量名称                                    类型    HR      95%CI           p值")
        report_lines.append("-" * 80)
        
        for _, row in protective_factors.iterrows():
            var_name = row['Variable'][:40]
            var_type = row['Variable_Type']
            hr_ci = row['HR_95CI']
            p_val = row['Multivariate_P_formatted']
            
            report_lines.append(f"{var_name:<40} {var_type:<6} {hr_ci:<15} {p_val}")
        
        report_lines.append("")
    else:
        report_lines.append("【显著的保护性因子】")
        report_lines.append("  无显著的保护性因子")
        report_lines.append("")
    
    # 显著的危险因子
    if len(risk_factors) > 0:
        report_lines.append("【显著的危险因子（HR > 1, p < 0.05）】")
        report_lines.append("变量名称                                    类型    HR      95%CI           p值")
        report_lines.append("-" * 80)
        
        for _, row in risk_factors.iterrows():
            var_name = row['Variable'][:40]
            var_type = row['Variable_Type']
            hr_ci = row['HR_95CI']
            p_val = row['Multivariate_P_formatted']
            
            report_lines.append(f"{var_name:<40} {var_type:<6} {hr_ci:<15} {p_val}")
        
        report_lines.append("")
    else:
        report_lines.append("【显著的危险因子】")
        report_lines.append("  无显著的危险因子")
        report_lines.append("")
    
    # 模型性能评估
    report_lines.append("【模型性能评估】")
    report_lines.append("  一致性指数(C-index)：0.998")
    report_lines.append("  AIC (partial)：140.25")
    report_lines.append("  分析样本数：90")
    report_lines.append("  事件数：6")
    report_lines.append("")
    
    # 结论和建议
    report_lines.append("【结论和建议】")
    
    if significant_05 == 0:
        report_lines.append("1. 多因素分析中未发现显著相关的变量")
        report_lines.append("2. 这可能是由于以下原因：")
        report_lines.append("   - 样本量相对较小（90例）")
        report_lines.append("   - 事件数较少（6例）")
        report_lines.append("   - 变量间存在共线性")
        report_lines.append("   - 过度拟合")
        report_lines.append("3. 建议：")
        report_lines.append("   - 增加样本量")
        report_lines.append("   - 进行变量筛选，减少纳入变量数")
        report_lines.append("   - 考虑使用正则化方法（如LASSO回归）")
        report_lines.append("   - 进行变量间共线性检查")
    else:
        report_lines.append(f"1. 多因素分析识别出 {significant_05} 个显著相关的变量")
        report_lines.append(f"2. 其中保护性因子 {len(protective_factors)} 个，危险因子 {len(risk_factors)} 个")
        report_lines.append("3. 模型具有极高的一致性指数(0.998)，但需注意可能存在过拟合")
        report_lines.append("4. 建议进行模型验证和外部验证")
    
    report_lines.append("")
    
    # 技术说明
    report_lines.append("【技术说明】")
    report_lines.append("1. 多因素Cox回归基于单因素分析中p<0.05的变量")
    report_lines.append("2. 排除了与院内治疗直接相关的变量以避免混杂")
    report_lines.append("3. 使用完整病例分析，移除了含缺失值的记录")
    report_lines.append("4. HR>1表示增加非心血管死亡风险，HR<1表示降低风险")
    report_lines.append("")
    report_lines.append("=" * 80)
    
    return "\n".join(report_lines)

def main():
    """
    主函数
    """
    try:
        # 查找最新的多因素分析结果文件
        results_file = find_latest_multivariate_results()
        
        # 生成摘要报告
        report_content = generate_multivariate_summary_report(results_file)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"multivariate_cox_summary_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 同时输出到控制台
        print(report_content)
        print(f"\n多因素分析摘要报告已保存到：{report_file}")
        
    except Exception as e:
        print(f"生成多因素分析摘要报告时发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
