#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理代谢组数据文件的脚本
1. 删除含QC文本的质控样本列
2. 简化样本名称：去掉HFsnEF和HFnEF前缀，只保留下划线后的数字/字母
3. 删除统计分析列
"""

import pandas as pd
import re

def process_metabolomics_data(input_file, output_file):
    """
    处理代谢组数据文件
    
    Parameters:
    input_file: 输入文件路径
    output_file: 输出文件路径
    """
    print(f"正在读取文件: {input_file}")
    
    # 读取TSV文件
    df = pd.read_csv(input_file, sep='\t')
    
    print(f"原始数据形状: {df.shape}")
    print(f"原始列数: {len(df.columns)}")
    
    # 获取列名
    columns = df.columns.tolist()
    print(f"前10个列名: {columns[:10]}")
    print(f"后10个列名: {columns[-10:]}")
    
    # 1. 识别需要删除的QC列
    qc_columns = [col for col in columns if 'QC' in col]
    print(f"\n找到 {len(qc_columns)} 个QC质控列: {qc_columns}")
    
    # 2. 识别需要删除的统计分析列
    stats_columns = [
        'VIP(HFsnEF_vs_HFnEF)',
        'FC(HFsnEF_vs_HFnEF)', 
        'pValue(HFsnEF_vs_HFnEF)',
        'FDR(HFsnEF_vs_HFnEF)',
        'Sig(HFsnEF_vs_HFnEF)'
    ]
    
    # 检查哪些统计列实际存在
    existing_stats_columns = [col for col in stats_columns if col in columns]
    print(f"找到 {len(existing_stats_columns)} 个统计分析列: {existing_stats_columns}")
    
    # 3. 识别需要重命名的样本列
    sample_columns = [col for col in columns if col.startswith('HFsnEF_') or col.startswith('HFnEF_')]
    print(f"找到 {len(sample_columns)} 个样本列需要重命名")
    
    # 4. 创建列名映射字典
    column_mapping = {}
    
    for col in sample_columns:
        if col.startswith('HFsnEF_'):
            # 去掉 HFsnEF_ 前缀
            new_name = col.replace('HFsnEF_', '')
        elif col.startswith('HFnEF_'):
            # 去掉 HFnEF_ 前缀
            new_name = col.replace('HFnEF_', '')
        else:
            new_name = col
        
        column_mapping[col] = new_name
    
    print(f"\n样本列重命名示例 (前5个):")
    for i, (old, new) in enumerate(list(column_mapping.items())[:5]):
        print(f"  {old} -> {new}")
    
    # 5. 删除QC列和统计分析列
    columns_to_drop = qc_columns + existing_stats_columns
    print(f"\n将删除 {len(columns_to_drop)} 列: {columns_to_drop}")
    
    # 删除指定列
    df_processed = df.drop(columns=columns_to_drop)
    
    # 6. 重命名样本列
    df_processed = df_processed.rename(columns=column_mapping)
    
    print(f"\n处理后数据形状: {df_processed.shape}")
    print(f"处理后列数: {len(df_processed.columns)}")
    
    # 7. 显示处理后的列名
    new_columns = df_processed.columns.tolist()
    print(f"处理后前10个列名: {new_columns[:10]}")
    print(f"处理后后10个列名: {new_columns[-10:]}")
    
    # 8. 保存处理后的数据
    print(f"\n正在保存到: {output_file}")
    df_processed.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
    
    print(f"文件处理完成!")
    
    # 9. 生成处理报告
    print(f"\n=== 处理报告 ===")
    print(f"原始数据: {df.shape[0]} 行 × {df.shape[1]} 列")
    print(f"处理后数据: {df_processed.shape[0]} 行 × {df_processed.shape[1]} 列")
    print(f"删除的列数: {len(columns_to_drop)}")
    print(f"重命名的列数: {len(column_mapping)}")
    print(f"保留的样本列数: {len([col for col in new_columns if col not in ['CPD_ID', 'CPD_Name', 'CPD_ID_with_mode', 'Ion_Mode']])}")
    
    return df_processed

def validate_processing(original_file, processed_file):
    """
    验证处理结果
    """
    print(f"\n=== 验证处理结果 ===")
    
    # 读取原始和处理后的文件
    df_original = pd.read_csv(original_file, sep='\t')
    df_processed = pd.read_csv(processed_file, sep='\t')
    
    print(f"原始文件行数: {len(df_original)}")
    print(f"处理后文件行数: {len(df_processed)}")
    print(f"行数是否一致: {len(df_original) == len(df_processed)}")
    
    # 检查关键列是否保留
    key_columns = ['CPD_ID', 'CPD_Name', 'CPD_ID_with_mode', 'Ion_Mode']
    for col in key_columns:
        if col in df_processed.columns:
            print(f"✅ 关键列 '{col}' 已保留")
        else:
            print(f"❌ 关键列 '{col}' 丢失")
    
    # 检查QC列是否删除
    qc_columns_remaining = [col for col in df_processed.columns if 'QC' in col]
    if len(qc_columns_remaining) == 0:
        print(f"✅ 所有QC列已删除")
    else:
        print(f"❌ 仍有QC列存在: {qc_columns_remaining}")
    
    # 检查统计列是否删除
    stats_patterns = ['VIP(', 'FC(', 'pValue(', 'FDR(', 'Sig(']
    stats_columns_remaining = [col for col in df_processed.columns 
                              if any(pattern in col for pattern in stats_patterns)]
    if len(stats_columns_remaining) == 0:
        print(f"✅ 所有统计分析列已删除")
    else:
        print(f"❌ 仍有统计分析列存在: {stats_columns_remaining}")
    
    # 检查样本列重命名
    hf_columns_remaining = [col for col in df_processed.columns 
                           if col.startswith('HFsnEF_') or col.startswith('HFnEF_')]
    if len(hf_columns_remaining) == 0:
        print(f"✅ 所有样本列已重命名")
    else:
        print(f"❌ 仍有未重命名的样本列: {hf_columns_remaining[:5]}...")
    
    # 显示一些重命名后的样本列示例
    sample_columns = [col for col in df_processed.columns 
                     if col not in key_columns and not col.startswith('HF')]
    print(f"重命名后的样本列示例: {sample_columns[:10]}")

def main():
    """
    主函数
    """
    input_file = "merged_metabolomics_separate.tsv"
    output_file = "processed_metabolomics_data.tsv"
    
    try:
        # 处理数据
        df_processed = process_metabolomics_data(input_file, output_file)
        
        # 验证处理结果
        validate_processing(input_file, output_file)
        
        print(f"\n✅ 数据处理成功完成!")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
