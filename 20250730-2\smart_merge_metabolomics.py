#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能合并正负离子模式代谢组数据
- 根据代谢物名称相似度智能合并相同代谢物
- 在CPD_ID上添加离子模式标识
- 提供多种合并策略选择
"""

import pandas as pd
import numpy as np
from difflib import SequenceMatcher

def similarity(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a, b).ratio()

def smart_merge_metabolomics(neg_file, pos_file, similarity_threshold=0.95, merge_strategy='separate'):
    """
    智能合并代谢组数据
    
    Parameters:
    neg_file: 负离子模式文件路径
    pos_file: 正离子模式文件路径
    similarity_threshold: 相似度阈值，用于判断是否为同一代谢物
    merge_strategy: 合并策略
        - 'separate': 保持分离，所有代谢物都独立记录
        - 'merge_similar': 合并相似的代谢物
        - 'exact_only': 只合并完全相同的代谢物
    """
    
    print(f"正在使用策略 '{merge_strategy}' 合并数据...")
    print(f"相似度阈值: {similarity_threshold}")
    
    # 读取数据
    neg_df = pd.read_csv(neg_file, sep='\t')
    pos_df = pd.read_csv(pos_file, sep='\t')
    
    print(f"负离子模式: {len(neg_df)} 个代谢物")
    print(f"正离子模式: {len(pos_df)} 个代谢物")
    
    # 添加离子模式标识
    neg_df['CPD_ID_with_mode'] = neg_df['CPD_ID'] + '_NEG'
    neg_df['Ion_Mode'] = 'NEG'
    
    pos_df['CPD_ID_with_mode'] = pos_df['CPD_ID'] + '_POS'
    pos_df['Ion_Mode'] = 'POS'
    
    if merge_strategy == 'separate':
        # 策略1: 保持完全分离
        merged_df = pd.concat([neg_df, pos_df], ignore_index=True)
        merged_df = merged_df.sort_values(['CPD_Name', 'Ion_Mode']).reset_index(drop=True)
        
        print(f"合并完成: {len(merged_df)} 条记录")
        return merged_df, [], []
    
    elif merge_strategy == 'exact_only':
        # 策略2: 只合并完全相同的代谢物名称
        common_names = set(neg_df['CPD_Name']) & set(pos_df['CPD_Name'])
        
        if common_names:
            print(f"找到 {len(common_names)} 个完全相同的代谢物名称")
            # 这里可以实现具体的合并逻辑
        
        merged_df = pd.concat([neg_df, pos_df], ignore_index=True)
        merged_df = merged_df.sort_values(['CPD_Name', 'Ion_Mode']).reset_index(drop=True)
        
        return merged_df, list(common_names), []
    
    elif merge_strategy == 'merge_similar':
        # 策略3: 合并相似的代谢物
        similar_pairs = find_similar_pairs(neg_df, pos_df, similarity_threshold)
        
        print(f"找到 {len(similar_pairs)} 对相似的代谢物")
        
        # 创建合并映射
        merge_groups = create_merge_groups(similar_pairs)
        
        # 执行合并
        merged_df = execute_merge(neg_df, pos_df, merge_groups)
        
        return merged_df, similar_pairs, merge_groups
    
    else:
        raise ValueError(f"未知的合并策略: {merge_strategy}")

def find_similar_pairs(neg_df, pos_df, threshold):
    """寻找相似的代谢物对"""
    similar_pairs = []
    
    for i, neg_row in neg_df.iterrows():
        for j, pos_row in pos_df.iterrows():
            sim = similarity(neg_row['CPD_Name'], pos_row['CPD_Name'])
            if sim >= threshold:
                similar_pairs.append({
                    'neg_idx': i,
                    'pos_idx': j,
                    'neg_cpd_id': neg_row['CPD_ID'],
                    'pos_cpd_id': pos_row['CPD_ID'],
                    'neg_name': neg_row['CPD_Name'],
                    'pos_name': pos_row['CPD_Name'],
                    'similarity': sim
                })
    
    return similar_pairs

def create_merge_groups(similar_pairs):
    """创建合并组"""
    # 简单实现：每对相似的代谢物作为一个组
    merge_groups = []
    used_neg = set()
    used_pos = set()
    
    for pair in sorted(similar_pairs, key=lambda x: x['similarity'], reverse=True):
        if pair['neg_idx'] not in used_neg and pair['pos_idx'] not in used_pos:
            merge_groups.append(pair)
            used_neg.add(pair['neg_idx'])
            used_pos.add(pair['pos_idx'])
    
    return merge_groups

def execute_merge(neg_df, pos_df, merge_groups):
    """执行合并操作"""
    merged_records = []
    used_neg_indices = set()
    used_pos_indices = set()
    
    # 处理相似的代谢物对
    for group in merge_groups:
        neg_idx = group['neg_idx']
        pos_idx = group['pos_idx']
        
        neg_row = neg_df.iloc[neg_idx].copy()
        pos_row = pos_df.iloc[pos_idx].copy()
        
        # 创建合并记录 - 负离子模式
        neg_row['CPD_ID_with_mode'] = neg_row['CPD_ID'] + '_NEG'
        neg_row['Ion_Mode'] = 'NEG'
        neg_row['Merged_Group'] = f"Group_{len(merged_records)//2 + 1}"
        merged_records.append(neg_row)
        
        # 创建合并记录 - 正离子模式
        pos_row['CPD_ID_with_mode'] = pos_row['CPD_ID'] + '_POS'
        pos_row['Ion_Mode'] = 'POS'
        pos_row['Merged_Group'] = f"Group_{len(merged_records)//2}"
        merged_records.append(pos_row)
        
        used_neg_indices.add(neg_idx)
        used_pos_indices.add(pos_idx)
    
    # 添加未合并的负离子模式代谢物
    for i, row in neg_df.iterrows():
        if i not in used_neg_indices:
            row = row.copy()
            row['CPD_ID_with_mode'] = row['CPD_ID'] + '_NEG'
            row['Ion_Mode'] = 'NEG'
            row['Merged_Group'] = 'Unique_NEG'
            merged_records.append(row)
    
    # 添加未合并的正离子模式代谢物
    for i, row in pos_df.iterrows():
        if i not in used_pos_indices:
            row = row.copy()
            row['CPD_ID_with_mode'] = row['CPD_ID'] + '_POS'
            row['Ion_Mode'] = 'POS'
            row['Merged_Group'] = 'Unique_POS'
            merged_records.append(row)
    
    merged_df = pd.DataFrame(merged_records)
    return merged_df.sort_values(['Merged_Group', 'Ion_Mode']).reset_index(drop=True)

def save_results(merged_df, similar_pairs, merge_groups, strategy):
    """保存结果"""
    # 保存主要合并结果
    output_file = f"merged_metabolomics_{strategy}.tsv"
    merged_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
    print(f"合并结果已保存到: {output_file}")
    
    # 保存相似对信息
    if similar_pairs:
        similar_df = pd.DataFrame(similar_pairs)
        similar_file = f"similar_pairs_{strategy}.csv"
        similar_df.to_csv(similar_file, index=False, encoding='utf-8-sig')
        print(f"相似对信息已保存到: {similar_file}")
    
    # 保存合并组信息
    if merge_groups:
        groups_df = pd.DataFrame(merge_groups)
        groups_file = f"merge_groups_{strategy}.csv"
        groups_df.to_csv(groups_file, index=False, encoding='utf-8-sig')
        print(f"合并组信息已保存到: {groups_file}")

def create_summary_report(merged_df, similar_pairs, merge_groups, strategy):
    """创建汇总报告"""
    print(f"\n=== {strategy.upper()} 策略合并报告 ===")
    
    total_records = len(merged_df)
    neg_records = len(merged_df[merged_df['Ion_Mode'] == 'NEG'])
    pos_records = len(merged_df[merged_df['Ion_Mode'] == 'POS'])
    
    print(f"总记录数: {total_records}")
    print(f"负离子模式记录: {neg_records}")
    print(f"正离子模式记录: {pos_records}")
    
    if 'Merged_Group' in merged_df.columns:
        unique_groups = merged_df['Merged_Group'].nunique()
        paired_groups = len([g for g in merged_df['Merged_Group'].unique() if g.startswith('Group_')])
        print(f"合并组总数: {unique_groups}")
        print(f"配对组数: {paired_groups}")
    
    if similar_pairs:
        print(f"相似代谢物对数: {len(similar_pairs)}")
        avg_similarity = np.mean([p['similarity'] for p in similar_pairs])
        print(f"平均相似度: {avg_similarity:.3f}")

def main():
    """主函数"""
    neg_file = "data_all.diff_NEG.tsv"
    pos_file = "data_all.diff_POS.tsv"
    
    # 策略1: 完全分离（推荐用于大多数分析）
    print("=== 策略1: 完全分离 ===")
    merged_df1, similar_pairs1, merge_groups1 = smart_merge_metabolomics(
        neg_file, pos_file, 
        similarity_threshold=0.95, 
        merge_strategy='separate'
    )
    save_results(merged_df1, similar_pairs1, merge_groups1, 'separate')
    create_summary_report(merged_df1, similar_pairs1, merge_groups1, 'separate')
    
    # 策略2: 合并高度相似的代谢物（用于探索性分析）
    print("\n=== 策略2: 合并相似代谢物 ===")
    merged_df2, similar_pairs2, merge_groups2 = smart_merge_metabolomics(
        neg_file, pos_file, 
        similarity_threshold=0.95, 
        merge_strategy='merge_similar'
    )
    save_results(merged_df2, similar_pairs2, merge_groups2, 'merge_similar')
    create_summary_report(merged_df2, similar_pairs2, merge_groups2, 'merge_similar')
    
    print(f"\n=== 合并完成 ===")
    print(f"推荐使用 'separate' 策略的结果进行后续分析")
    print(f"主要输出文件: merged_metabolomics_separate.tsv")

if __name__ == "__main__":
    main()
