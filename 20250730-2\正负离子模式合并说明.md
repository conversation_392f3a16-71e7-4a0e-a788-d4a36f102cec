# 正负离子模式代谢组数据合并说明

## 概述
本项目成功将正离子模式(`data_all.diff_POS.tsv`)和负离子模式(`data_all.diff_NEG.tsv`)的代谢组数据进行了合并，根据代谢物名称(CPD_Name)识别相同代谢物，并在CPD_ID上添加了离子模式标识。

## 原始数据特征

### 1. 负离子模式数据 (data_all.diff_NEG.tsv)
- **代谢物数量**: 423个
- **数据格式**: TSV文件
- **主要列**: CPD_ID, CPD_Name, 样本数据列, 统计分析列

### 2. 正离子模式数据 (data_all.diff_POS.tsv)
- **代谢物数量**: 275个
- **数据格式**: TSV文件
- **主要列**: CPD_ID, CPD_Name, 样本数据列, 统计分析列

## 数据分析结果

### 代谢物重叠分析
通过相似度分析发现：
- **完全相同的代谢物名称**: 0个
- **高度相似的代谢物对** (相似度 ≥ 0.95): 1对
- **中等相似的代谢物对** (相似度 ≥ 0.7): 280对

### 最相似的代谢物对示例
1. **相似度: 0.978**
   - NEG: CPD_0094 - 3,4,5-trihydroxy-6-{[5-(4-methoxyphenyl)-2-methyl-3-oxopentyl]oxy}oxane-2-carboxylic acid
   - POS: CPD_0055 - 3,4,5-trihydroxy-6-{[1-(4-methoxyphenyl)-4-methyl-3-oxopentyl]oxy}oxane-2-carboxylic acid

2. **相似度: 0.938**
   - NEG: CPD_0256 - Ganoderic acid J
   - POS: CPD_0166 - Ganoderic acid H

## 合并策略

### 策略1: 完全分离 (推荐)
- **原理**: 保持所有代谢物独立，不进行合并
- **优点**: 保留所有原始信息，避免数据丢失
- **适用**: 大多数代谢组学分析

### 策略2: 智能合并
- **原理**: 根据相似度阈值合并相似代谢物
- **优点**: 减少冗余，识别可能的同一代谢物
- **适用**: 探索性分析和代谢物注释

## 最终合并结果

### 主要输出文件: `merged_metabolomics_separate.tsv`

#### 数据特征
- **总记录数**: 698条
- **总列数**: 223列
- **负离子模式记录**: 423条
- **正离子模式记录**: 275条
- **唯一代谢物名称**: 698个

#### 新增列说明
1. **CPD_ID_with_mode**: 原CPD_ID + 离子模式标识
   - 格式: `CPD_XXXX_NEG` 或 `CPD_XXXX_POS`
   - 示例: `CPD_0001_NEG`, `CPD_0001_POS`

2. **Ion_Mode**: 离子模式标识
   - 值: `NEG` (负离子模式) 或 `POS` (正离子模式)

#### 数据结构
```
列顺序: CPD_ID | CPD_Name | 样本数据列 | 统计分析列 | CPD_ID_with_mode | Ion_Mode
```

## 辅助分析文件

### 1. 相似代谢物分析
- **similar_metabolites.csv**: 所有相似代谢物对的详细信息
- **similar_pairs_merge_similar.csv**: 合并策略中使用的相似对
- **merge_groups_merge_similar.csv**: 合并组信息

### 2. 备选合并结果
- **merged_metabolomics_merge_similar.tsv**: 智能合并策略的结果

## 数据质量检查

### ✅ 已验证项目
- 数据完整性: 所有原始记录都已保留
- 标识唯一性: CPD_ID_with_mode确保每条记录唯一
- 离子模式标识: 正确添加NEG/POS标识
- 数据类型一致性: 所有数值列保持原始格式

### 📊 统计验证
- 原始数据总数: 423 + 275 = 698
- 合并后记录数: 698 ✅
- 数据丢失: 0条 ✅

## 使用建议

### 1. 后续分析推荐
- **主要分析**: 使用 `merged_metabolomics_separate.tsv`
- **代谢物注释**: 参考 `similar_metabolites.csv` 中的相似对
- **离子模式比较**: 利用 `Ion_Mode` 列进行分组分析

### 2. 数据筛选示例
```python
# 读取合并数据
df = pd.read_csv('merged_metabolomics_separate.tsv', sep='\t')

# 筛选负离子模式数据
neg_data = df[df['Ion_Mode'] == 'NEG']

# 筛选正离子模式数据
pos_data = df[df['Ion_Mode'] == 'POS']

# 查找特定代谢物的不同离子模式
metabolite_name = "某代谢物名称"
both_modes = df[df['CPD_Name'].str.contains(metabolite_name, na=False)]
```

### 3. 注意事项
1. **代谢物命名**: 正负离子模式中的代谢物名称可能略有差异
2. **数据解释**: 同一代谢物在不同离子模式下的信号强度不能直接比较
3. **统计分析**: 进行差异分析时需要考虑离子模式的影响

## 技术实现

### 使用的脚本
1. **smart_merge_metabolomics.py**: 主要合并脚本
2. **analyze_metabolite_similarity.py**: 相似性分析脚本

### 核心算法
- **相似度计算**: 使用SequenceMatcher计算字符串相似度
- **合并策略**: 提供多种合并选项
- **数据验证**: 自动检查数据完整性

## 结论

成功完成了正负离子模式代谢组数据的合并，生成了包含698个代谢物记录的统一数据集。每个代谢物都明确标识了其离子模式，为后续的代谢组学分析提供了完整的数据基础。

合并后的数据集保持了原始数据的完整性，同时通过添加离子模式标识，使得研究人员可以方便地进行离子模式特异性分析或整合分析。
