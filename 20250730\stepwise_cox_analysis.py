#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步多因素Cox回归分析脚本

作者: AI Assistant
日期: 2025-07-30
描述: 使用逐步回归方法进行多因素Cox分析，避免过拟合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from lifelines import CoxPHFitter
import warnings
import os
from datetime import datetime
import glob

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def find_latest_results_file():
    """
    查找最新的Cox分析结果文件
    """
    pattern = "cox_analysis_results_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到Cox分析结果文件")
    
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def load_data_and_results():
    """
    加载原始数据和单因素分析结果
    """
    print("正在加载数据和分析结果...")
    
    # 加载原始数据
    try:
        data = pd.read_csv("merged_metabolomics_clinical_final.csv", encoding='utf-8')
    except UnicodeDecodeError:
        data = pd.read_csv("merged_metabolomics_clinical_final.csv", encoding='gbk')
    
    # 预处理数据
    data['event'] = pd.to_numeric(data['NonCV_death'], errors='coerce')
    data['duration'] = pd.to_numeric(data['OS'], errors='coerce')
    data = data.dropna(subset=['event', 'duration'])
    data = data[data['duration'] > 0]
    data['event'] = data['event'].astype(int)
    
    # 加载单因素分析结果
    results_file = find_latest_results_file()
    results = pd.read_csv(results_file)
    
    print(f"数据加载完成：{data.shape[0]} 行，{data.shape[1]} 列")
    print(f"事件数：{data['event'].sum()}")
    
    return data, results

def select_top_variables(results, exclude_vars=None, max_vars=10):
    """
    选择最显著的变量进行多因素分析
    
    Parameters:
    results (pd.DataFrame): 单因素分析结果
    exclude_vars (list): 要排除的变量列表
    max_vars (int): 最大变量数
    
    Returns:
    list: 选中的变量列表
    """
    if exclude_vars is None:
        exclude_vars = []
    
    print(f"正在选择前{max_vars}个最显著的变量...")
    
    # 筛选成功的分析结果
    successful_vars = results[results['Status'] == '成功'].copy()
    
    # 排除指定变量
    successful_vars = successful_vars[
        ~successful_vars['Variable'].isin(exclude_vars)
    ]
    
    # 按p值排序，选择前max_vars个
    top_vars = successful_vars.nsmallest(max_vars, 'P_value')
    selected_vars = top_vars['Variable'].tolist()
    
    print(f"排除变量：{exclude_vars}")
    print(f"选中 {len(selected_vars)} 个变量用于多因素分析")
    
    return selected_vars, top_vars

def prepare_analysis_data(data, selected_vars):
    """
    准备分析数据
    """
    print("正在准备分析数据...")
    
    # 选择需要的列
    analysis_cols = ['duration', 'event'] + selected_vars
    analysis_data = data[analysis_cols].copy()
    
    # 移除缺失值
    initial_count = len(analysis_data)
    analysis_data = analysis_data.dropna()
    removed_count = initial_count - len(analysis_data)
    
    if removed_count > 0:
        print(f"移除了 {removed_count} 条包含缺失值的记录")
    
    print(f"分析数据：{analysis_data.shape[0]} 行，{len(selected_vars)} 个变量")
    print(f"事件数：{analysis_data['event'].sum()}")
    
    # 检查事件数与变量数的比例
    events_per_var = analysis_data['event'].sum() / len(selected_vars)
    print(f"每个变量的事件数：{events_per_var:.1f}")
    
    if events_per_var < 10:
        print("警告：每个变量的事件数少于10，可能导致过拟合")
    
    return analysis_data

def perform_stepwise_cox_analysis(analysis_data, selected_vars):
    """
    执行逐步Cox回归分析
    """
    print("正在执行逐步Cox回归分析...")
    
    results = []
    
    # 方法1：单独分析每个变量（调整后的单因素分析）
    print("\n方法1：调整后的单因素分析")
    for var in selected_vars:
        try:
            var_data = analysis_data[['duration', 'event', var]].copy()
            
            cph = CoxPHFitter()
            cph.fit(var_data, duration_col='duration', event_col='event')
            
            summary = cph.summary
            hr = summary.loc[var, 'exp(coef)']
            hr_lower = summary.loc[var, 'exp(coef) lower 95%']
            hr_upper = summary.loc[var, 'exp(coef) upper 95%']
            p_value = summary.loc[var, 'p']
            
            results.append({
                'Variable': var,
                'Method': '调整后单因素',
                'HR': hr,
                'HR_Lower': hr_lower,
                'HR_Upper': hr_upper,
                'P_value': p_value,
                'N': len(var_data),
                'Events': var_data['event'].sum()
            })
            
        except Exception as e:
            print(f"变量 {var} 分析失败：{str(e)}")
    
    # 方法2：两两组合分析
    print("\n方法2：两两组合分析（前5个最显著变量）")
    top_5_vars = selected_vars[:5]
    
    for i, var1 in enumerate(top_5_vars):
        for var2 in top_5_vars[i+1:]:
            try:
                pair_data = analysis_data[['duration', 'event', var1, var2]].copy()
                
                cph = CoxPHFitter()
                cph.fit(pair_data, duration_col='duration', event_col='event')
                
                summary = cph.summary
                
                # 记录第一个变量的结果
                hr1 = summary.loc[var1, 'exp(coef)']
                hr1_lower = summary.loc[var1, 'exp(coef) lower 95%']
                hr1_upper = summary.loc[var1, 'exp(coef) upper 95%']
                p1_value = summary.loc[var1, 'p']
                
                results.append({
                    'Variable': var1,
                    'Method': f'双变量({var1}+{var2})',
                    'HR': hr1,
                    'HR_Lower': hr1_lower,
                    'HR_Upper': hr1_upper,
                    'P_value': p1_value,
                    'N': len(pair_data),
                    'Events': pair_data['event'].sum()
                })
                
                # 记录第二个变量的结果
                hr2 = summary.loc[var2, 'exp(coef)']
                hr2_lower = summary.loc[var2, 'exp(coef) lower 95%']
                hr2_upper = summary.loc[var2, 'exp(coef) upper 95%']
                p2_value = summary.loc[var2, 'p']
                
                results.append({
                    'Variable': var2,
                    'Method': f'双变量({var1}+{var2})',
                    'HR': hr2,
                    'HR_Lower': hr2_lower,
                    'HR_Upper': hr2_upper,
                    'P_value': p2_value,
                    'N': len(pair_data),
                    'Events': pair_data['event'].sum()
                })
                
            except Exception as e:
                print(f"变量组合 {var1}+{var2} 分析失败：{str(e)}")
    
    # 方法3：前3个变量的三变量模型
    print("\n方法3：三变量模型")
    if len(selected_vars) >= 3:
        try:
            top_3_vars = selected_vars[:3]
            triple_data = analysis_data[['duration', 'event'] + top_3_vars].copy()
            
            cph = CoxPHFitter()
            cph.fit(triple_data, duration_col='duration', event_col='event')
            
            summary = cph.summary
            
            for var in top_3_vars:
                hr = summary.loc[var, 'exp(coef)']
                hr_lower = summary.loc[var, 'exp(coef) lower 95%']
                hr_upper = summary.loc[var, 'exp(coef) upper 95%']
                p_value = summary.loc[var, 'p']
                
                results.append({
                    'Variable': var,
                    'Method': '三变量模型',
                    'HR': hr,
                    'HR_Lower': hr_lower,
                    'HR_Upper': hr_upper,
                    'P_value': p_value,
                    'N': len(triple_data),
                    'Events': triple_data['event'].sum()
                })
                
        except Exception as e:
            print(f"三变量模型分析失败：{str(e)}")
    
    return pd.DataFrame(results)

def format_stepwise_results(results_df):
    """
    格式化逐步分析结果
    """
    print("正在格式化结果...")
    
    # 格式化HR和置信区间
    def format_hr_ci(row):
        return f"{row['HR']:.3f} ({row['HR_Lower']:.3f}-{row['HR_Upper']:.3f})"
    
    results_df['HR_95CI'] = results_df.apply(format_hr_ci, axis=1)
    
    # 格式化p值
    def format_pvalue(p):
        if p < 0.001:
            return '<0.001'
        else:
            return f"{p:.3f}"
    
    results_df['P_value_formatted'] = results_df['P_value'].apply(format_pvalue)
    
    # 添加显著性标记
    def significance_mark(p):
        if p < 0.001:
            return '***'
        elif p < 0.01:
            return '**'
        elif p < 0.05:
            return '*'
        else:
            return ''
    
    results_df['Significance'] = results_df['P_value'].apply(significance_mark)
    
    return results_df

def create_comparison_plot(results_df):
    """
    创建比较图表
    """
    print("正在创建比较图表...")
    
    # 获取唯一变量
    unique_vars = results_df['Variable'].unique()[:8]  # 只显示前8个变量
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 图1：不同方法下的HR值比较
    methods = results_df['Method'].unique()
    colors = plt.cm.Set1(np.linspace(0, 1, len(methods)))
    
    for i, method in enumerate(methods):
        method_data = results_df[
            (results_df['Method'] == method) & 
            (results_df['Variable'].isin(unique_vars))
        ]
        
        if len(method_data) > 0:
            x_pos = np.arange(len(method_data))
            ax1.scatter(x_pos + i*0.1, method_data['HR'], 
                       label=method, color=colors[i], alpha=0.7, s=60)
    
    ax1.axhline(y=1, color='black', linestyle='--', alpha=0.5)
    ax1.set_xlabel('变量')
    ax1.set_ylabel('风险比 (HR)')
    ax1.set_title('不同分析方法下的HR值比较')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 图2：p值比较
    for i, method in enumerate(methods):
        method_data = results_df[
            (results_df['Method'] == method) & 
            (results_df['Variable'].isin(unique_vars))
        ]
        
        if len(method_data) > 0:
            x_pos = np.arange(len(method_data))
            ax2.scatter(x_pos + i*0.1, -np.log10(method_data['P_value']), 
                       label=method, color=colors[i], alpha=0.7, s=60)
    
    ax2.axhline(y=-np.log10(0.05), color='red', linestyle='--', alpha=0.7, label='p=0.05')
    ax2.set_xlabel('变量')
    ax2.set_ylabel('-log10(p值)')
    ax2.set_title('不同分析方法下的显著性比较')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    return fig

def main():
    """
    主函数
    """
    print("=" * 80)
    print("逐步多因素Cox回归分析")
    print("=" * 80)
    
    try:
        # 1. 加载数据和结果
        data, univariate_results = load_data_and_results()
        
        # 2. 定义要排除的变量
        exclude_vars = [
            'Death_in_hospital',
            'Inhospital_vasoactive_drug', 
            'Inhospital_mehanical_ventilation',
            'AR'
        ]
        
        # 3. 选择前10个最显著的变量
        selected_vars, top_vars = select_top_variables(
            univariate_results, exclude_vars=exclude_vars, max_vars=10
        )
        
        if len(selected_vars) == 0:
            print("没有符合条件的变量进行分析")
            return
        
        # 4. 准备分析数据
        analysis_data = prepare_analysis_data(data, selected_vars)
        
        # 5. 执行逐步分析
        results_df = perform_stepwise_cox_analysis(analysis_data, selected_vars)
        
        # 6. 格式化结果
        formatted_results = format_stepwise_results(results_df)
        
        # 7. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"stepwise_cox_results_{timestamp}.csv"
        formatted_results.to_csv(results_file, index=False, encoding='utf-8-sig')
        
        # 8. 创建比较图表
        fig = create_comparison_plot(formatted_results)
        
        # 保存图表
        plot_file = f"stepwise_cox_comparison_{timestamp}.png"
        fig.savefig(plot_file, dpi=300, bbox_inches='tight')
        
        # 9. 输出结果摘要
        print("\n" + "=" * 80)
        print("逐步Cox回归分析完成！")
        print("=" * 80)
        
        print(f"分析变量数：{len(selected_vars)}")
        print(f"分析样本数：{len(analysis_data)}")
        print(f"事件数：{analysis_data['event'].sum()}")
        
        # 显示各方法中的显著变量
        significant_results = formatted_results[formatted_results['P_value'] < 0.05]
        if len(significant_results) > 0:
            print(f"\n显著变量（p<0.05）：")
            for method in significant_results['Method'].unique():
                method_sig = significant_results[significant_results['Method'] == method]
                print(f"\n{method}：")
                for _, row in method_sig.iterrows():
                    print(f"  {row['Variable']}: HR={row['HR']:.3f}, p={row['P_value_formatted']}")
        else:
            print("\n未发现显著变量")
        
        print(f"\n结果文件：{results_file}")
        print(f"比较图表：{plot_file}")
        
        plt.show()
        
    except Exception as e:
        print(f"分析过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
