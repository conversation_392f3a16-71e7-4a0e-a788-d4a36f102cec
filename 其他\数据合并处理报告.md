# 蛋白组和临床数据合并处理报告

## 处理概述

本报告详细说明了蛋白组数据（Proteins_all_diff.csv）和临床数据（203HFsnEF_800d.csv）的合并处理过程和结果。

## 数据处理步骤

### 1. 蛋白组数据处理（Proteins_all_diff.csv）

#### 原始数据结构：
- **数据形状**: 4,401行 × 215列
- **行**: 每行代表一个蛋白
- **列**: ID, Accession, Gene Name, Description + 203个样本列 + 8个统计分析列

#### 处理步骤：
1. **删除不需要的列**：
   - 删除ID、Accession、Description列
   - 保留Gene Name作为蛋白标识
   - 删除最后8列统计分析结果：FC(HFsnEF_vs_HFnEF)、pValue(HFsnEF_vs_HFnEF)、FDR(HFsnEF_vs_HFnEF)、Sig(HFsnEF_vs_HFnEF)、CC、MF、BP、KEGG

2. **处理Record_ID**：
   - 去除样本列名中的"HFnEF_"和"HFsnEF_"前缀
   - 保留数字部分作为真正的Record_ID
   - 例如：HFnEF_1 → 1, HFsnEF_8 → 8

3. **数据转置**：
   - 将蛋白作为列，样本作为行
   - 转置后形状：203行 × 4,402列（包括Record_ID列）

#### 处理后结果：
- **数据形状**: 203行 × 4,402列
- **行**: 每行代表一个样本
- **列**: Record_ID + 4,401个蛋白

### 2. 临床数据处理（203HFsnEF_800d.csv）

#### 原始数据结构：
- **数据形状**: 3,454行 × 181列
- **编码**: 使用GBK编码成功读取
- **行**: 每行代表一个样本
- **列**: Record_ID + 180个临床变量

#### 处理步骤：
- 确保Record_ID为字符串类型以便匹配
- 保持原始数据结构不变

### 3. 数据合并

#### 合并策略：
- **合并方式**: 左连接（以临床数据为基准）
- **合并键**: Record_ID
- **合并原则**: 保留临床数据的所有样本，匹配对应的蛋白数据

#### 匹配情况：
- **蛋白组数据Record_ID数量**: 203个
- **临床数据Record_ID数量**: 204个
- **匹配的Record_ID数量**: 203个
- **只在蛋白组数据中的Record_ID**: 0个
- **只在临床数据中的Record_ID**: 1个（"nan"）

## 最终合并结果

### 数据概览：
- **最终数据形状**: 3,454行 × 4,582列
- **样本数**: 3,454个（包含重复的Record_ID）
- **唯一Record_ID数量**: 204个
- **变量总数**: 4,581个（临床变量 + 蛋白变量）

### 变量分布：
- **临床变量**: 180个
- **蛋白变量**: 4,401个
- **Record_ID**: 1个

### 数据质量：
- **总缺失值数量**: 14,897,456个
- **缺失值比例**: 94.13%
- **有缺失值的列数**: 4,582列（所有列）
- **完全无缺失值的样本数**: 11个

### 缺失值分析：
- **平均每行缺失值**: 4,313.10个
- **最大每行缺失值**: 4,582个
- **最小每行缺失值**: 0个

### 蛋白数据特征：
- **蛋白数量**: 4,401个
- **蛋白数据缺失值**: 14,731,564个
- **蛋白数据非零值比例**: 5.82%

## 数据文件输出

### 主要输出文件：
1. **merged_protein_clinical_data.csv**: 最终合并的数据集
2. **merge_protein_clinical_data.py**: 数据处理脚本
3. **check_merged_data.py**: 数据质量检查脚本

## 数据使用说明

### 数据结构：
- **第一列**: Record_ID（样本标识）
- **第2-181列**: 临床变量
- **第182-4582列**: 蛋白变量（以Gene Name命名）

### 注意事项：
1. **缺失值处理**: 数据中存在大量缺失值，使用前需要根据分析需求进行适当的缺失值处理
2. **数据重复**: 临床数据中存在重复的Record_ID，导致最终数据行数多于唯一样本数
3. **数据类型**: 建议在读取数据时指定适当的数据类型以避免混合类型警告
4. **蛋白数据稀疏**: 蛋白数据中非零值比例较低（5.82%），可能需要进行数据预处理

### 推荐的后续处理：
1. **去重处理**: 根据Record_ID去除重复样本
2. **缺失值处理**: 根据分析需求选择适当的缺失值处理策略
3. **数据标准化**: 对蛋白数据进行标准化或归一化处理
4. **特征选择**: 根据研究目标选择相关的临床变量和蛋白

## 处理完成确认

✅ 蛋白组数据成功处理和转置  
✅ 临床数据成功读取  
✅ 数据成功按Record_ID合并  
✅ 合并后数据保存为CSV格式  
✅ 数据质量检查完成  

合并后的数据集已准备就绪，可用于后续的生物信息学分析和统计建模。
