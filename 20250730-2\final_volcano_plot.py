#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化的Cox回归火山图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import warnings
warnings.filterwarnings('ignore')

# 设置字体 - 确保中文显示
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def create_final_volcano_plot():
    """创建最终优化的Cox回归火山图"""
    
    print("正在创建最终优化的Cox回归火山图...")
    
    # 读取数据
    cox_results = pd.read_csv('all_metabolites_cox_results.csv')
    
    # 准备数据
    plot_data = cox_results.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义显著性阈值
    p_threshold = 0.05
    
    # 分类
    plot_data['significance'] = 'Non-significant'
    significant_protective = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] < 1)
    significant_risk = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] > 1)
    
    plot_data.loc[significant_protective, 'significance'] = 'Significant Protective'
    plot_data.loc[significant_risk, 'significance'] = 'Significant Risk'
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 设置背景色
    ax.set_facecolor('white')
    
    # 定义颜色
    colors = {
        'Non-significant': '#CCCCCC',      # 浅灰色
        'Significant Protective': '#1E88E5',  # 蓝色
        'Significant Risk': '#D32F2F'         # 红色
    }
    
    # 绘制散点图 - 先画非显著的，再画显著的
    for category in ['Non-significant', 'Significant Protective', 'Significant Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.6 if category == 'Non-significant' else 0.8
            size = 25 if category == 'Non-significant' else 40
            zorder = 1 if category == 'Non-significant' else 3
            
            ax.scatter(plot_data.loc[mask, 'log2_hr'], 
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category], 
                      alpha=alpha,
                      s=size,
                      zorder=zorder,
                      edgecolors='white' if category != 'Non-significant' else 'none',
                      linewidth=0.5)
    
    # 添加P=0.05的水平线
    ax.axhline(y=-np.log10(p_threshold), color='black', linestyle='--', 
               alpha=0.8, linewidth=1.5, zorder=2)
    
    # 添加HR=1的垂直线
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.6, linewidth=1, zorder=2)
    
    # 设置坐标轴范围 - 缩窄两侧空白
    ax.set_xlim(-4.5, 4.5)  # 缩窄范围，减少空白
    
    y_max = plot_data['neg_log10_p'].max() * 1.05
    ax.set_ylim(0, y_max)
    
    # 设置x轴刻度
    x_ticks = [-4, -3, -2, -1, 0, 1, 2, 3, 4]
    ax.set_xticks(x_ticks)
    ax.set_xticklabels([str(tick) for tick in x_ticks], fontsize=12)
    
    # 设置y轴刻度
    ax.tick_params(axis='y', labelsize=12)
    
    # 设置标签和标题 - 使用英文确保显示
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=14, fontweight='bold')
    ax.set_ylabel('-log₁₀(P value)', fontsize=14, fontweight='bold')
    ax.set_title('Cox Regression Analysis Volcano Plot - Metabolites vs Non-CV Death', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 在P=0.05线旁边添加标注
    ax.text(-4.2, -np.log10(p_threshold) + 0.1, 'P = 0.05', 
            fontsize=11, color='black', fontweight='bold')
    
    # 添加HR解释文本
    ax.text(-4.2, 0.2, 'Protective', fontsize=12, color='#1E88E5', 
            fontweight='bold', ha='left')
    ax.text(3.5, 0.2, 'Risk', fontsize=12, color='#D32F2F', 
            fontweight='bold', ha='right')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)
    
    # 设置刻度参数
    ax.tick_params(axis='both', which='major', labelsize=12, 
                   length=6, width=1.5, direction='out')
    
    plt.tight_layout()
    plt.savefig('final_cox_volcano_plot.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    print("最终优化的Cox回归火山图已保存到: final_cox_volcano_plot.png")
    
    # 输出统计信息
    print(f"\n火山图统计:")
    print(f"总代谢物数量: {len(plot_data)}")
    print(f"显著保护性 (蓝色): {significant_protective.sum()}")
    print(f"显著危险性 (红色): {significant_risk.sum()}")
    print(f"非显著 (灰色): {(plot_data['significance'] == 'Non-significant').sum()}")

def create_publication_ready_plot():
    """创建发表级别的火山图"""
    
    print("\n正在创建发表级别的火山图...")
    
    # 读取数据
    cox_results = pd.read_csv('all_metabolites_cox_results.csv')
    
    # 准备数据
    plot_data = cox_results.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义阈值
    p_threshold = 0.05
    
    # 分类
    significant_protective = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] < 1)
    significant_risk = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] > 1)
    non_significant = plot_data['p_value'] >= p_threshold
    
    # 创建图形 - 使用更标准的尺寸
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # 设置样式
    ax.set_facecolor('white')
    
    # 绘制点
    # 非显著点
    ax.scatter(plot_data.loc[non_significant, 'log2_hr'], 
              plot_data.loc[non_significant, 'neg_log10_p'],
              c='#DDDDDD', alpha=0.6, s=20, zorder=1)
    
    # 显著保护性点
    ax.scatter(plot_data.loc[significant_protective, 'log2_hr'], 
              plot_data.loc[significant_protective, 'neg_log10_p'],
              c='#2196F3', alpha=0.8, s=35, zorder=3, 
              edgecolors='white', linewidth=0.5)
    
    # 显著危险性点
    ax.scatter(plot_data.loc[significant_risk, 'log2_hr'], 
              plot_data.loc[significant_risk, 'neg_log10_p'],
              c='#F44336', alpha=0.8, s=35, zorder=3,
              edgecolors='white', linewidth=0.5)
    
    # 添加阈值线
    ax.axhline(y=-np.log10(p_threshold), color='black', linestyle='--', 
               alpha=0.8, linewidth=1.2, zorder=2)
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5, linewidth=1, zorder=2)
    
    # 设置坐标轴
    ax.set_xlim(-4.2, 4.2)
    ax.set_ylim(0, plot_data['neg_log10_p'].max() * 1.02)
    
    # 设置刻度
    ax.set_xticks([-4, -2, 0, 2, 4])
    ax.set_xticklabels(['-4', '-2', '0', '2', '4'], fontsize=11)
    ax.tick_params(axis='y', labelsize=11)
    
    # 标签
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=12, fontweight='bold')
    ax.set_ylabel('-log₁₀(P value)', fontsize=12, fontweight='bold')
    
    # 添加P=0.05标注
    ax.text(-4, -np.log10(p_threshold) + 0.05, 'P = 0.05', 
            fontsize=10, color='black')
    
    # 美化
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.grid(True, alpha=0.2)
    
    plt.tight_layout()
    plt.savefig('publication_volcano_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("发表级别火山图已保存到: publication_volcano_plot.png")

def main():
    """主函数"""
    # 创建最终优化版火山图
    create_final_volcano_plot()
    
    # 创建发表级别火山图
    create_publication_ready_plot()
    
    print(f"\n已生成优化的火山图:")
    print(f"• final_cox_volcano_plot.png - 最终优化版本")
    print(f"• publication_volcano_plot.png - 发表级别版本")

if __name__ == "__main__":
    main()
