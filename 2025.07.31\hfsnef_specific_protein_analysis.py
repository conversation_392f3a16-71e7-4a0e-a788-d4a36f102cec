#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HFsnEF特异相关显著差异蛋白分析
结合HFnEF和HFsnEF两组非心血管死亡相关蛋白的cox分析结果
选出HFsnEF特异相关的显著差异蛋白（p值<0.05），也就是和HFnEF组不重叠的显著差异蛋白
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import networkx as nx
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_and_process_data():
    """加载和处理Cox分析结果数据"""
    print("正在加载Cox分析结果数据...")
    
    # 读取HFnEF和HFsnEF的Cox分析结果
    hfnef_data = pd.read_csv('hfnef_protein_cox_results.csv')
    hfsnef_data = pd.read_csv('hfsnef_protein_cox_results.csv')
    
    print(f"HFnEF数据: {len(hfnef_data)} 个蛋白")
    print(f"HFsnEF数据: {len(hfsnef_data)} 个蛋白")
    
    return hfnef_data, hfsnef_data

def identify_specific_proteins(hfnef_data, hfsnef_data, p_threshold=0.05):
    """识别HFsnEF特异相关的显著差异蛋白"""
    print(f"\n正在识别HFsnEF特异相关的显著差异蛋白 (p < {p_threshold})...")
    
    # 筛选显著差异蛋白
    hfnef_significant = hfnef_data[hfnef_data['p_value'] < p_threshold]['protein'].tolist()
    hfsnef_significant = hfsnef_data[hfsnef_data['p_value'] < p_threshold]['protein'].tolist()
    
    print(f"HFnEF显著差异蛋白: {len(hfnef_significant)} 个")
    print(f"HFsnEF显著差异蛋白: {len(hfsnef_significant)} 个")
    
    # 找出HFsnEF特异的蛋白（在HFsnEF中显著但在HFnEF中不显著）
    hfsnef_specific = list(set(hfsnef_significant) - set(hfnef_significant))
    
    print(f"HFsnEF特异相关显著差异蛋白: {len(hfsnef_specific)} 个")
    
    # 获取HFsnEF特异蛋白的详细信息
    hfsnef_specific_data = hfsnef_data[hfsnef_data['protein'].isin(hfsnef_specific)].copy()
    hfsnef_specific_data = hfsnef_specific_data.sort_values('p_value')
    
    return hfsnef_specific, hfsnef_specific_data, hfnef_significant, hfsnef_significant

def create_protein_clusters(specific_data, n_clusters=5):
    """基于风险比和p值创建蛋白质簇"""
    print(f"\n正在创建蛋白质簇 (k={n_clusters})...")
    
    # 准备聚类数据
    features = specific_data[['hazard_ratio', 'p_value']].copy()
    features['log_p'] = -np.log10(features['p_value'])
    features['log_hr'] = np.log2(features['hazard_ratio'])
    
    # 标准化特征
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features[['log_hr', 'log_p']])
    
    # K-means聚类
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    clusters = kmeans.fit_predict(features_scaled)
    
    # 添加聚类标签
    specific_data_clustered = specific_data.copy()
    specific_data_clustered['cluster'] = clusters
    
    # 为每个簇命名
    cluster_names = {}
    for i in range(n_clusters):
        cluster_proteins = specific_data_clustered[specific_data_clustered['cluster'] == i]
        mean_hr = cluster_proteins['hazard_ratio'].mean()
        mean_p = cluster_proteins['p_value'].mean()
        
        if mean_hr > 1.5:
            if mean_p < 0.01:
                cluster_names[i] = f"高风险簇_{i+1}"
            else:
                cluster_names[i] = f"中等风险簇_{i+1}"
        elif mean_hr < 0.67:
            if mean_p < 0.01:
                cluster_names[i] = f"保护性簇_{i+1}"
            else:
                cluster_names[i] = f"轻度保护簇_{i+1}"
        else:
            cluster_names[i] = f"中性簇_{i+1}"
    
    specific_data_clustered['cluster_name'] = specific_data_clustered['cluster'].map(cluster_names)
    
    return specific_data_clustered, cluster_names

def analyze_cluster_characteristics(clustered_data):
    """分析各簇的特征"""
    print("\n正在分析各簇特征...")
    
    cluster_summary = []
    
    for cluster_name in clustered_data['cluster_name'].unique():
        cluster_proteins = clustered_data[clustered_data['cluster_name'] == cluster_name]
        
        summary = {
            '簇名称': cluster_name,
            '蛋白数量': len(cluster_proteins),
            '平均风险比': cluster_proteins['hazard_ratio'].mean(),
            '风险比范围': f"{cluster_proteins['hazard_ratio'].min():.2f}-{cluster_proteins['hazard_ratio'].max():.2f}",
            '平均p值': cluster_proteins['p_value'].mean(),
            'p值范围': f"{cluster_proteins['p_value'].min():.4f}-{cluster_proteins['p_value'].max():.4f}",
            '代表蛋白': cluster_proteins.nsmallest(3, 'p_value')['protein'].tolist()
        }
        cluster_summary.append(summary)
    
    cluster_summary_df = pd.DataFrame(cluster_summary)
    return cluster_summary_df

def create_visualizations(clustered_data, cluster_summary):
    """创建可视化图表"""
    print("\n正在创建可视化图表...")
    
    # 设置图形样式
    plt.style.use('default')
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 火山图 - 显示所有HFsnEF特异蛋白
    ax1 = plt.subplot(2, 3, 1)
    x = np.log2(clustered_data['hazard_ratio'])
    y = -np.log10(clustered_data['p_value'])
    colors = clustered_data['cluster']
    
    scatter = ax1.scatter(x, y, c=colors, cmap='tab10', alpha=0.7, s=50)
    ax1.axhline(y=-np.log10(0.05), color='red', linestyle='--', alpha=0.5)
    ax1.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    ax1.set_xlabel('Log2(风险比)')
    ax1.set_ylabel('-Log10(p值)')
    ax1.set_title('HFsnEF特异蛋白火山图')
    ax1.grid(True, alpha=0.3)
    
    # 2. 簇分布饼图
    ax2 = plt.subplot(2, 3, 2)
    cluster_counts = clustered_data['cluster_name'].value_counts()
    colors_pie = plt.cm.Set3(np.linspace(0, 1, len(cluster_counts)))
    wedges, texts, autotexts = ax2.pie(cluster_counts.values, labels=cluster_counts.index, 
                                       autopct='%1.1f%%', colors=colors_pie)
    ax2.set_title('蛋白质簇分布')
    
    # 3. 风险比分布箱线图
    ax3 = plt.subplot(2, 3, 3)
    cluster_names = clustered_data['cluster_name'].unique()
    hr_data = [clustered_data[clustered_data['cluster_name'] == name]['hazard_ratio'] 
               for name in cluster_names]
    
    box_plot = ax3.boxplot(hr_data, labels=cluster_names, patch_artist=True)
    colors_box = plt.cm.Set2(np.linspace(0, 1, len(cluster_names)))
    for patch, color in zip(box_plot['boxes'], colors_box):
        patch.set_facecolor(color)
    
    ax3.axhline(y=1, color='red', linestyle='--', alpha=0.5)
    ax3.set_ylabel('风险比')
    ax3.set_title('各簇风险比分布')
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. Top蛋白条形图
    ax4 = plt.subplot(2, 3, 4)
    top_proteins = clustered_data.nsmallest(15, 'p_value')
    y_pos = np.arange(len(top_proteins))
    
    bars = ax4.barh(y_pos, -np.log10(top_proteins['p_value']), 
                    color=plt.cm.viridis(top_proteins['hazard_ratio']/top_proteins['hazard_ratio'].max()))
    ax4.set_yticks(y_pos)
    ax4.set_yticklabels(top_proteins['protein'])
    ax4.set_xlabel('-Log10(p值)')
    ax4.set_title('Top 15 显著蛋白')
    ax4.grid(True, alpha=0.3)
    
    # 5. 簇特征热图
    ax5 = plt.subplot(2, 3, 5)
    heatmap_data = []
    for cluster_name in cluster_names:
        cluster_data = clustered_data[clustered_data['cluster_name'] == cluster_name]
        heatmap_data.append([
            cluster_data['hazard_ratio'].mean(),
            -np.log10(cluster_data['p_value'].mean()),
            len(cluster_data)
        ])
    
    heatmap_df = pd.DataFrame(heatmap_data, 
                             columns=['平均风险比', '平均-Log10(p值)', '蛋白数量'],
                             index=cluster_names)
    
    # 标准化数据用于热图
    heatmap_normalized = (heatmap_df - heatmap_df.mean()) / heatmap_df.std()
    
    im = ax5.imshow(heatmap_normalized.T, cmap='RdYlBu_r', aspect='auto')
    ax5.set_xticks(range(len(cluster_names)))
    ax5.set_xticklabels(cluster_names, rotation=45)
    ax5.set_yticks(range(len(heatmap_df.columns)))
    ax5.set_yticklabels(heatmap_df.columns)
    ax5.set_title('簇特征热图')
    
    # 添加数值标注
    for i in range(len(heatmap_df.columns)):
        for j in range(len(cluster_names)):
            text = ax5.text(j, i, f'{heatmap_df.iloc[j, i]:.2f}',
                           ha="center", va="center", color="black", fontsize=8)
    
    # 6. 网络图（简化版）
    ax6 = plt.subplot(2, 3, 6)
    
    # 创建简单的网络图显示簇间关系
    G = nx.Graph()
    
    # 添加节点（每个簇）
    for cluster_name in cluster_names:
        cluster_size = len(clustered_data[clustered_data['cluster_name'] == cluster_name])
        G.add_node(cluster_name, size=cluster_size)
    
    # 基于风险比相似性添加边
    for i, cluster1 in enumerate(cluster_names):
        for j, cluster2 in enumerate(cluster_names[i+1:], i+1):
            hr1 = clustered_data[clustered_data['cluster_name'] == cluster1]['hazard_ratio'].mean()
            hr2 = clustered_data[clustered_data['cluster_name'] == cluster2]['hazard_ratio'].mean()
            
            # 如果风险比相似，添加边
            if abs(np.log2(hr1) - np.log2(hr2)) < 1:
                G.add_edge(cluster1, cluster2, weight=1/(abs(hr1-hr2)+0.1))
    
    # 绘制网络
    pos = nx.spring_layout(G, k=1, iterations=50)
    node_sizes = [G.nodes[node]['size'] * 100 for node in G.nodes()]
    
    nx.draw(G, pos, ax=ax6, with_labels=True, node_size=node_sizes,
            node_color=range(len(G.nodes())), cmap='Set3',
            font_size=8, font_weight='bold')
    ax6.set_title('蛋白质簇网络图')
    
    plt.tight_layout()
    plt.savefig('hfsnef_specific_protein_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def generate_summary_report(clustered_data, cluster_summary, hfsnef_specific, 
                          hfnef_significant, hfsnef_significant):
    """生成分析总结报告"""
    print("\n正在生成分析总结报告...")
    
    report = f"""
# HFsnEF特异相关显著差异蛋白分析报告

## 1. 数据概览
- HFnEF显著差异蛋白: {len(hfnef_significant)} 个
- HFsnEF显著差异蛋白: {len(hfsnef_significant)} 个
- HFsnEF特异相关蛋白: {len(hfsnef_specific)} 个

## 2. 蛋白质簇分析结果

### 2.1 簇特征总结
"""
    
    for _, row in cluster_summary.iterrows():
        report += f"""
#### {row['簇名称']}
- 蛋白数量: {row['蛋白数量']} 个
- 平均风险比: {row['平均风险比']:.3f}
- 风险比范围: {row['风险比范围']}
- 平均p值: {row['平均p值']:.6f}
- p值范围: {row['p值范围']}
- 代表蛋白: {', '.join(row['代表蛋白'])}
"""
    
    report += f"""
### 2.2 关键发现

#### 高风险蛋白簇
"""
    
    high_risk_clusters = cluster_summary[cluster_summary['簇名称'].str.contains('高风险')]
    for _, cluster in high_risk_clusters.iterrows():
        cluster_proteins = clustered_data[clustered_data['cluster_name'] == cluster['簇名称']]
        top_proteins = cluster_proteins.nsmallest(5, 'p_value')
        
        report += f"""
**{cluster['簇名称']}** ({cluster['蛋白数量']} 个蛋白):
- 特征: 高风险比 (平均 {cluster['平均风险比']:.2f})，强显著性
- Top 5 蛋白:
"""
        for _, protein in top_proteins.iterrows():
            report += f"  - {protein['protein']}: HR={protein['hazard_ratio']:.2f}, p={protein['p_value']:.4f}\n"
    
    report += f"""
#### 保护性蛋白簇
"""
    
    protective_clusters = cluster_summary[cluster_summary['簇名称'].str.contains('保护')]
    for _, cluster in protective_clusters.iterrows():
        cluster_proteins = clustered_data[clustered_data['cluster_name'] == cluster['簇名称']]
        top_proteins = cluster_proteins.nsmallest(5, 'p_value')
        
        report += f"""
**{cluster['簇名称']}** ({cluster['蛋白数量']} 个蛋白):
- 特征: 低风险比 (平均 {cluster['平均风险比']:.2f})，保护作用
- Top 5 蛋白:
"""
        for _, protein in top_proteins.iterrows():
            report += f"  - {protein['protein']}: HR={protein['hazard_ratio']:.2f}, p={protein['p_value']:.4f}\n"
    
    # 保存报告
    with open('hfsnef_specific_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    return report

def main():
    """主函数"""
    print("=== HFsnEF特异相关显著差异蛋白分析 ===")
    
    # 1. 加载数据
    hfnef_data, hfsnef_data = load_and_process_data()
    
    # 2. 识别HFsnEF特异蛋白
    hfsnef_specific, hfsnef_specific_data, hfnef_significant, hfsnef_significant = identify_specific_proteins(
        hfnef_data, hfsnef_data)
    
    # 3. 创建蛋白质簇
    clustered_data, cluster_names = create_protein_clusters(hfsnef_specific_data)
    
    # 4. 分析簇特征
    cluster_summary = analyze_cluster_characteristics(clustered_data)
    
    # 5. 创建可视化
    fig = create_visualizations(clustered_data, cluster_summary)
    
    # 6. 生成报告
    report = generate_summary_report(clustered_data, cluster_summary, hfsnef_specific,
                                   hfnef_significant, hfsnef_significant)
    
    # 7. 保存结果
    clustered_data.to_csv('hfsnef_specific_proteins_clustered.csv', index=False, encoding='utf-8')
    cluster_summary.to_csv('cluster_summary.csv', index=False, encoding='utf-8')
    
    print(f"\n=== 分析完成 ===")
    print(f"发现 {len(hfsnef_specific)} 个HFsnEF特异相关显著差异蛋白")
    print(f"分为 {len(cluster_names)} 个功能簇")
    print(f"结果已保存到:")
    print(f"- hfsnef_specific_proteins_clustered.csv")
    print(f"- cluster_summary.csv") 
    print(f"- hfsnef_specific_analysis_report.md")
    print(f"- hfsnef_specific_protein_analysis.png")
    
    return clustered_data, cluster_summary

if __name__ == "__main__":
    clustered_data, cluster_summary = main()
