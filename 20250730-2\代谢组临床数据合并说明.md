# 代谢组数据与临床数据合并说明

## 概述
本项目成功将处理后的代谢组数据(`processed_metabolomics_data.tsv`)与临床数据(`203HFsnEF_800d.xlsx`)按照`Record_ID`进行合并，生成了包含代谢物和临床变量的统一数据集。

## 输入数据特征

### 1. 代谢组数据 (processed_metabolomics_data.tsv)
- **数据格式**: TSV文件
- **原始维度**: 698行 × 207列
- **数据结构**:
  - 行：代谢物记录（698个代谢物）
  - 列：样本数据（203个样本）+ 基本信息列
  - 基本信息列：CPD_ID, CPD_Name, CPD_ID_with_mode, Ion_Mode
- **离子模式分布**:
  - 负离子模式(NEG)：423个代谢物
  - 正离子模式(POS)：275个代谢物

### 2. 临床数据 (203HFsnEF_800d.xlsx)
- **数据格式**: Excel文件
- **数据维度**: 203行 × 181列
- **数据结构**:
  - 行：样本记录（203个样本）
  - 列：临床变量（180个）+ Record_ID
  - 第一列：Record_ID（样本标识符）

## 数据处理流程

### 1. 代谢组数据转置
由于代谢组数据的原始格式是代谢物为行、样本为列，需要转置为样本为行、代谢物为列的格式：

#### 转置前（原始格式）
```
CPD_ID | CPD_Name | 样本1 | 样本2 | ... | CPD_ID_with_mode | Ion_Mode
代谢物1 | 名称1    | 数值  | 数值  | ... | ID_NEG          | NEG
代谢物2 | 名称2    | 数值  | 数值  | ... | ID_POS          | POS
```

#### 转置后（合并格式）
```
Record_ID | 代谢物1_NEG | 代谢物2_POS | ...
样本1     | 数值        | 数值        | ...
样本2     | 数值        | 数值        | ...
```

### 2. 代谢物标识符创建
为了确保代谢物的唯一性，创建了组合标识符：
- **格式**: `代谢物名称_离子模式`
- **示例**: 
  - `(+)-cis-5,6-Dihydro-5-hydroxy-4-methoxy-6-(2-phenylethyl)-2H-pyran-2-one_NEG`
  - `(2'E,4'Z,7'Z,8E)-Colnelenic acid_POS`

### 3. 数据合并
使用内连接(inner join)按`Record_ID`合并两个数据集：
- **合并方式**: 只保留两个数据集中都存在的样本
- **合并结果**: 所有203个样本都成功匹配

## 最终合并结果

### 输出文件: `merged_metabolomics_clinical_data.tsv`

#### 数据特征
- **数据维度**: 203行 × 879列
- **样本数**: 203个
- **变量构成**:
  - 1个样本标识符 (`Record_ID`)
  - 180个临床变量
  - 698个代谢物变量

#### 数据结构
```
列顺序: Record_ID | 临床变量(180个) | 代谢物变量(698个)
行数: 203个样本
```

#### 样本匹配情况
- **代谢组数据样本数**: 203个
- **临床数据样本数**: 203个
- **成功匹配样本数**: 203个
- **匹配率**: 100%

### 数据质量验证

#### ✅ 验证通过项目
1. **数据完整性**: 
   - 第一列确实是Record_ID ✅
   - 无缺失的Record_ID ✅
   - 无重复的Record_ID ✅

2. **样本一致性**:
   - 样本数量保持203个 ✅
   - 所有样本都成功匹配 ✅

3. **变量完整性**:
   - 临床变量：180个 ✅
   - 代谢物变量：698个 ✅
   - 总变量数：879个 ✅

## 列结构详细说明

### 1. 样本标识符 (1列)
- **Record_ID**: 样本唯一标识符
- **示例**: '1', '8', '9', '28', '39', ..., 'F157', '973', '990', '992', '994'

### 2. 临床变量 (180列)
包括但不限于：
- **基本信息**: HFsnEF, Sex, Age, Height, Weight, BMI等
- **生化指标**: WBC, RBC, Hb, ALT, AST, BUN, Cr等
- **心功能指标**: LVEF, LVFS, NT_proBNPx等
- **用药信息**: RAASi, β_blocker, Loop_diuretic等
- **预后信息**: CV death, HF_hospitalization等

### 3. 代谢物变量 (698列)
- **命名格式**: `代谢物名称_离子模式`
- **离子模式**: NEG（负离子）或 POS（正离子）
- **数据类型**: 数值型（代谢物定量结果）

#### 代谢物示例
- `(+)-cis-5,6-Dihydro-5-hydroxy-4-methoxy-6-(2-phenylethyl)-2H-pyran-2-one_NEG`
- `(2'E,4'Z,7'Z,8E)-Colnelenic acid_POS`
- `Arachidonic acid_NEG`
- `L-Glutamic acid_POS`

## 使用建议

### 1. 数据读取
```python
import pandas as pd

# 读取合并后的数据
df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')

# 获取样本ID
sample_ids = df['Record_ID']

# 获取临床变量（第2-181列）
clinical_data = df.iloc[:, 1:181]

# 获取代谢物数据（第182-879列）
metabolomics_data = df.iloc[:, 181:]
```

### 2. 数据分析建议
1. **探索性分析**: 检查数据分布、缺失值、异常值
2. **相关性分析**: 分析代谢物与临床指标的关联
3. **差异分析**: 比较不同组别间的代谢物差异
4. **预测建模**: 使用代谢物预测临床结局

### 3. 离子模式分析
```python
# 分离正负离子模式的代谢物
neg_metabolites = [col for col in df.columns if col.endswith('_NEG')]
pos_metabolites = [col for col in df.columns if col.endswith('_POS')]

print(f"负离子模式代谢物数: {len(neg_metabolites)}")
print(f"正离子模式代谢物数: {len(pos_metabolites)}")
```

## 技术实现

### 使用的脚本
- **主要脚本**: `merge_metabolomics_clinical.py`
- **功能**: 自动化数据转置、合并和验证

### 核心处理步骤
1. 读取代谢组TSV文件
2. 读取临床Excel文件
3. 转置代谢组数据（代谢物为列，样本为行）
4. 创建代谢物唯一标识符
5. 按Record_ID内连接合并
6. 保存合并结果
7. 数据质量验证

## 注意事项

### 1. 数据解释
- 代谢物数值为定量结果，单位可能因代谢物而异
- 正负离子模式的同一代谢物不能直接比较强度
- 临床变量的含义需参考原始数据字典

### 2. 分析建议
- 进行统计分析前建议先进行数据标准化
- 考虑批次效应和技术变异的影响
- 注意多重比较校正

### 3. 文件管理
- 原始文件保持不变
- 合并后文件较大（203×879），注意存储空间
- 建议定期备份重要分析结果

## 结论

成功完成了代谢组数据与临床数据的合并，生成了包含203个样本、879个变量的综合数据集。合并后的数据集：

- ✅ 第一列是Record_ID
- ✅ 包含完整的临床和代谢组信息
- ✅ 数据质量良好，无缺失或重复的样本ID
- ✅ 为后续的多组学整合分析提供了理想的数据基础

该数据集可用于代谢组学与临床表型的关联分析、疾病预测建模、生物标志物发现等研究。
