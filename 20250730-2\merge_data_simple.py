#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版数据合并脚本
用于合并代谢组数据(Metabolism_POS_mix.csv)和临床数据(203HFsnEF_800d.xlsx)
"""

import pandas as pd
import os

def merge_metabolism_clinical_data():
    """
    合并代谢组数据和临床数据
    """
    print("开始合并代谢组数据和临床数据...")
    
    # 1. 读取代谢组数据
    print("正在读取代谢组数据...")
    try:
        # 尝试不同编码读取CSV文件
        metabolism_df = None
        for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
            try:
                metabolism_df = pd.read_csv('Metabolism_POS_mix.csv', encoding=encoding)
                print(f"成功使用 {encoding} 编码读取代谢组数据")
                break
            except UnicodeDecodeError:
                continue
        
        if metabolism_df is None:
            raise ValueError("无法读取代谢组数据文件")
        
        # 转置代谢组数据
        # 获取样本ID（列名，从第3列开始）
        sample_ids = [str(sid) for sid in metabolism_df.columns[2:] 
                     if pd.notna(sid) and str(sid).strip() != '']
        
        # 获取代谢物名称（第二列，从第2行开始）
        metabolite_names = metabolism_df.iloc[1:, 1].values
        
        # 获取代谢物数据（从第2行第3列开始）
        num_valid_samples = len(sample_ids)
        metabolite_data = metabolism_df.iloc[1:, 2:2+num_valid_samples].values
        
        # 转置数据，使样本成为行
        metabolite_data_transposed = metabolite_data.T
        
        # 创建新的DataFrame
        metabolism_final = pd.DataFrame(
            metabolite_data_transposed,
            columns=metabolite_names,
            index=sample_ids
        )
        metabolism_final.reset_index(inplace=True)
        metabolism_final.rename(columns={'index': 'Record_ID'}, inplace=True)
        
        print(f"代谢组数据处理完成: {metabolism_final.shape}")
        
    except Exception as e:
        print(f"读取代谢组数据时出错: {e}")
        return None
    
    # 2. 读取临床数据
    print("正在读取临床数据...")
    try:
        clinical_df = pd.read_excel('203HFsnEF_800d.xlsx')
        print(f"临床数据读取完成: {clinical_df.shape}")
    except Exception as e:
        print(f"读取临床数据时出错: {e}")
        return None
    
    # 3. 合并数据
    print("正在合并数据...")
    try:
        # 确保Record_ID列的数据类型一致
        metabolism_final['Record_ID'] = metabolism_final['Record_ID'].astype(str)
        clinical_df['Record_ID'] = clinical_df['Record_ID'].astype(str)
        
        # 内连接合并
        merged_df = pd.merge(
            clinical_df, 
            metabolism_final, 
            on='Record_ID', 
            how='inner'
        )
        
        print(f"数据合并完成: {merged_df.shape}")
        print(f"成功合并的样本数: {len(merged_df)}")
        
        # 保存合并后的数据
        output_file = 'merged_metabolism_clinical_data.csv'
        merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"合并后的数据已保存到: {output_file}")
        
        return merged_df
        
    except Exception as e:
        print(f"合并数据时出错: {e}")
        return None

def main():
    """主函数"""
    # 检查文件是否存在
    if not os.path.exists('Metabolism_POS_mix.csv'):
        print("错误: 找不到代谢组数据文件 Metabolism_POS_mix.csv")
        return
    
    if not os.path.exists('203HFsnEF_800d.xlsx'):
        print("错误: 找不到临床数据文件 203HFsnEF_800d.xlsx")
        return
    
    # 执行合并
    result = merge_metabolism_clinical_data()
    
    if result is not None:
        print("\n=== 合并成功 ===")
        print(f"最终数据集包含:")
        print(f"- 样本数: {len(result)}")
        print(f"- 总变量数: {len(result.columns)}")
        print(f"- 第一列是Record_ID: {result.columns[0] == 'Record_ID'}")
        print(f"- 前5个样本ID: {list(result['Record_ID'].head())}")
    else:
        print("合并失败，请检查错误信息")

if __name__ == "__main__":
    main()
