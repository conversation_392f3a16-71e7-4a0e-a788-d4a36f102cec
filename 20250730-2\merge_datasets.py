#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并代谢组数据和临床数据的脚本
代谢组数据：Metabolism_POS_mix.csv
临床数据：203HFsnEF_800d.xlsx
"""

import pandas as pd
import numpy as np
import os

def load_metabolism_data(file_path):
    """
    加载代谢组数据
    第一列是代谢物编号，第二列是代谢物名称，后面每一列代表一个样本
    第一行是Record_ID
    """
    print(f"正在加载代谢组数据: {file_path}")

    # 尝试不同的编码方式读取CSV文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
    df = None

    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用编码 {encoding} 读取文件")
            break
        except UnicodeDecodeError:
            print(f"编码 {encoding} 失败，尝试下一个...")
            continue

    if df is None:
        raise ValueError("无法使用任何编码读取CSV文件")
    
    print(f"代谢组数据形状: {df.shape}")
    print(f"前几列名称: {list(df.columns[:5])}")

    # 转置数据，使样本成为行，代谢物成为列
    # 第一行是Record_ID，第一列是代谢物编号，第二列是代谢物名称

    # 获取样本ID（第一行，从第3列开始，去除空值）
    # 注意：第一行第一列是"Record_ID"，第二列是空的，从第三列开始是样本ID
    sample_ids_raw = df.columns[2:].tolist()  # 使用列名而不是第一行的值
    # 过滤掉空值和NaN
    sample_ids = [str(sid) for sid in sample_ids_raw if pd.notna(sid) and str(sid).strip() != '' and str(sid) != 'Unnamed: 1']

    # 获取代谢物名称（第二列，从第2行开始）
    metabolite_names = df.iloc[1:, 1].values

    # 获取代谢物数据（从第2行第3列开始，只取有效样本的列数）
    num_valid_samples = len(sample_ids)
    metabolite_data = df.iloc[1:, 2:2+num_valid_samples].values

    # 转置数据，使样本成为行
    metabolite_data_transposed = metabolite_data.T

    # 创建新的DataFrame
    metabolism_df = pd.DataFrame(
        metabolite_data_transposed,
        columns=metabolite_names,
        index=sample_ids
    )

    # 重置索引，将样本ID作为一列
    metabolism_df.reset_index(inplace=True)
    metabolism_df.rename(columns={'index': 'Record_ID'}, inplace=True)

    print(f"转置后的代谢组数据形状: {metabolism_df.shape}")
    print(f"样本数量: {len(metabolism_df)}")
    print(f"代谢物数量: {len(metabolite_names)}")
    print(f"有效样本ID示例: {sample_ids[:5]}")
    
    return metabolism_df

def load_clinical_data(file_path):
    """
    加载临床数据
    每一行代表一个样本，每一列代表一个临床变量
    第一列是Record_ID
    """
    print(f"正在加载临床数据: {file_path}")
    
    # 读取Excel文件
    df = pd.read_excel(file_path)
    
    print(f"临床数据形状: {df.shape}")
    print(f"前几列名称: {list(df.columns[:5])}")
    
    return df

def merge_datasets(metabolism_df, clinical_df):
    """
    按照Record_ID合并两个数据集
    """
    print("正在合并数据集...")
    
    # 确保Record_ID列的数据类型一致
    metabolism_df['Record_ID'] = metabolism_df['Record_ID'].astype(str)
    clinical_df['Record_ID'] = clinical_df['Record_ID'].astype(str)
    
    print(f"代谢组数据中的样本数: {len(metabolism_df)}")
    print(f"临床数据中的样本数: {len(clinical_df)}")
    
    # 检查重复的Record_ID
    metabolism_duplicates = metabolism_df['Record_ID'].duplicated().sum()
    clinical_duplicates = clinical_df['Record_ID'].duplicated().sum()
    
    if metabolism_duplicates > 0:
        print(f"警告: 代谢组数据中有 {metabolism_duplicates} 个重复的Record_ID")
    if clinical_duplicates > 0:
        print(f"警告: 临床数据中有 {clinical_duplicates} 个重复的Record_ID")
    
    # 内连接合并，只保留两个数据集中都存在的样本
    merged_df = pd.merge(
        clinical_df, 
        metabolism_df, 
        on='Record_ID', 
        how='inner'
    )
    
    print(f"合并后的数据形状: {merged_df.shape}")
    print(f"成功合并的样本数: {len(merged_df)}")
    
    # 检查合并结果
    common_samples = set(metabolism_df['Record_ID']) & set(clinical_df['Record_ID'])
    print(f"两个数据集共同的样本数: {len(common_samples)}")
    
    return merged_df

def save_merged_data(merged_df, output_path):
    """
    保存合并后的数据
    """
    print(f"正在保存合并后的数据到: {output_path}")
    
    # 保存为CSV文件
    merged_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    
    print(f"数据已成功保存到: {output_path}")
    print(f"最终数据形状: {merged_df.shape}")
    print(f"列数: {len(merged_df.columns)}")
    print(f"行数: {len(merged_df)}")

def main():
    """
    主函数
    """
    # 文件路径
    metabolism_file = "Metabolism_POS_mix.csv"
    clinical_file = "203HFsnEF_800d.xlsx"
    output_file = "merged_metabolism_clinical_data.csv"
    
    # 检查文件是否存在
    if not os.path.exists(metabolism_file):
        print(f"错误: 找不到代谢组数据文件 {metabolism_file}")
        return
    
    if not os.path.exists(clinical_file):
        print(f"错误: 找不到临床数据文件 {clinical_file}")
        return
    
    try:
        # 加载数据
        metabolism_df = load_metabolism_data(metabolism_file)
        clinical_df = load_clinical_data(clinical_file)
        
        # 显示数据预览
        print("\n=== 代谢组数据预览 ===")
        print(metabolism_df.head())
        print(f"\n代谢组数据的Record_ID示例: {list(metabolism_df['Record_ID'].head())}")
        
        print("\n=== 临床数据预览 ===")
        print(clinical_df.head())
        print(f"\n临床数据的Record_ID示例: {list(clinical_df['Record_ID'].head())}")
        
        # 合并数据
        merged_df = merge_datasets(metabolism_df, clinical_df)
        
        # 保存结果
        save_merged_data(merged_df, output_file)
        
        print("\n=== 合并完成 ===")
        print(f"合并后的数据包含:")
        print(f"- 样本数: {len(merged_df)}")
        print(f"- 总变量数: {len(merged_df.columns) - 1}")  # 减去Record_ID列
        print(f"- 临床变量数: {len(clinical_df.columns) - 1}")
        print(f"- 代谢物数: {len(metabolism_df.columns) - 1}")
        
        print(f"\n合并后数据的前几列:")
        print(list(merged_df.columns[:10]))
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
