import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns


def analyze_differential_proteins(file_path, p_threshold=0.05, fc_threshold=1.5):
    """
    分析差异蛋白并绘制火山图
    """
    # 读取数据
    df = pd.read_csv(file_path)

    # 提取必要列
    fc_col = 'FC(HFsnEF_vs_HFnEF)'
    p_col = 'pValue(HFsnEF_vs_HFnEF)'
    gene_col = 'Gene Name'

    # 数据预处理
    df['abs_fc'] = abs(df[fc_col])
    df['log2_fc'] = np.log2(df['abs_fc'])
    df['neg_log10_p'] = -np.log10(df[p_col])

    # 分类蛋白
    conditions = [
        (df[p_col] < p_threshold) & (df['abs_fc'] > fc_threshold) & (df[fc_col] > 0),
        (df[p_col] < p_threshold) & (df['abs_fc'] > fc_threshold) & (df[fc_col] < 0)
    ]
    choices = ['Up-regulated', 'Down-regulated']
    df['significance'] = np.select(conditions, choices, default='Not Significant')

    # 统计结果
    summary = df['significance'].value_counts()
    print("差异蛋白分析结果:")
    print(f"总蛋白数: {len(df)}")
    print(f"上调蛋白: {summary.get('Up-regulated', 0)}")
    print(f"下调蛋白: {summary.get('Down-regulated', 0)}")
    print(f"无显著差异: {summary.get('Not Significant', 0)}")

    # 绘制火山图
    plt.figure(figsize=(10, 8))
    colors = {'Up-regulated': 'red', 'Down-regulated': 'darkgray', 'Not Significant': 'lightgray'}

    for sig_type in df['significance'].unique():
        subset = df[df['significance'] == sig_type]
        plt.scatter(subset['log2_fc'], subset['neg_log10_p'],
                    c=colors[sig_type], label=sig_type, alpha=0.7, s=30)

    # 添加阈值线
    plt.axvline(x=np.log2(fc_threshold), color='gray', linestyle='--', alpha=0.7)
    plt.axvline(x=-np.log2(fc_threshold), color='gray', linestyle='--', alpha=0.7)
    plt.axhline(y=-np.log10(p_threshold), color='gray', linestyle='--', alpha=0.7)

    plt.xlabel('Log2(Fold Change)')
    plt.ylabel('-Log10(p-value)')
    plt.title('Volcano Plot: HFsnEF vs HFnEF')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    return df

# 使用示例
df_results = analyze_differential_proteins('./Proteins_all_diff.csv')
