#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢组学与非心血管死亡关联分析
比较HFnEF和HFsnEF两组，分析与非心血管死亡相关的代谢物
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import mannwhitneyu, chi2_contingency
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
from lifelines.statistics import logrank_test
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载和预处理数据"""
    print("正在加载数据...")
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    
    # 获取代谢物列
    metabolite_cols = [col for col in df.columns if '_POS' in col or '_NEG' in col]
    
    # 清理数据
    # 移除缺失值过多的样本
    df_clean = df.dropna(subset=['NonCV_death', 'OS'])
    
    print(f"原始样本数: {len(df)}")
    print(f"清理后样本数: {len(df_clean)}")
    print(f"代谢物数量: {len(metabolite_cols)}")
    
    return df_clean, metabolite_cols

def univariate_analysis(df, metabolite_cols, outcome='NonCV_death'):
    """单变量分析：寻找与结局相关的代谢物"""
    print(f"\n正在进行单变量分析 - 结局变量: {outcome}")
    
    results = []
    
    for metabolite in metabolite_cols:
        try:
            # 获取非缺失值
            mask = ~(df[metabolite].isna() | df[outcome].isna())
            if mask.sum() < 10:  # 样本量太小跳过
                continue
                
            metabolite_values = df.loc[mask, metabolite]
            outcome_values = df.loc[mask, outcome]
            
            # Mann-Whitney U检验
            group0 = metabolite_values[outcome_values == 0]
            group1 = metabolite_values[outcome_values == 1]
            
            if len(group0) > 0 and len(group1) > 0:
                statistic, p_value = mannwhitneyu(group0, group1, alternative='two-sided')
                
                # 计算效应量 (Cohen's d)
                pooled_std = np.sqrt(((len(group0)-1)*group0.std()**2 + (len(group1)-1)*group1.std()**2) / 
                                   (len(group0) + len(group1) - 2))
                if pooled_std > 0:
                    cohens_d = (group1.mean() - group0.mean()) / pooled_std
                else:
                    cohens_d = 0
                
                results.append({
                    'metabolite': metabolite,
                    'p_value': p_value,
                    'statistic': statistic,
                    'cohens_d': cohens_d,
                    'mean_group0': group0.mean(),
                    'mean_group1': group1.mean(),
                    'n_group0': len(group0),
                    'n_group1': len(group1)
                })
        except Exception as e:
            print(f"分析 {metabolite} 时出错: {e}")
            continue
    
    # 转换为DataFrame并进行多重检验校正
    results_df = pd.DataFrame(results)
    if len(results_df) > 0:
        # Benjamini-Hochberg校正
        from statsmodels.stats.multitest import multipletests
        _, results_df['p_adjusted'], _, _ = multipletests(results_df['p_value'], method='fdr_bh')
        
        # 按p值排序
        results_df = results_df.sort_values('p_value')
    
    return results_df

def analyze_lvef_relationship(df, significant_metabolites, alpha=0.05):
    """分析显著代谢物与LVEF的关系，检验U型曲线"""
    print(f"\n正在分析与LVEF的关系...")
    
    lvef_results = []
    
    for metabolite in significant_metabolites:
        try:
            # 获取非缺失值
            mask = ~(df[metabolite].isna() | df['LVEF'].isna())
            if mask.sum() < 20:  # 样本量太小跳过
                continue
                
            metabolite_values = df.loc[mask, metabolite]
            lvef_values = df.loc[mask, 'LVEF']
            
            # 线性关系
            linear_corr, linear_p = stats.pearsonr(metabolite_values, lvef_values)
            
            # 二次关系检验 (U型曲线)
            # 拟合二次多项式
            coeffs = np.polyfit(lvef_values, metabolite_values, 2)
            poly_fit = np.poly1d(coeffs)
            
            # 计算R²
            y_pred = poly_fit(lvef_values)
            ss_res = np.sum((metabolite_values - y_pred) ** 2)
            ss_tot = np.sum((metabolite_values - np.mean(metabolite_values)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            # F检验比较线性vs二次模型
            from scipy.stats import f
            n = len(metabolite_values)
            
            # 线性模型残差
            linear_coeffs = np.polyfit(lvef_values, metabolite_values, 1)
            linear_fit = np.poly1d(linear_coeffs)
            linear_pred = linear_fit(lvef_values)
            ss_res_linear = np.sum((metabolite_values - linear_pred) ** 2)
            
            # F统计量
            if ss_res > 0:
                f_stat = ((ss_res_linear - ss_res) / 1) / (ss_res / (n - 3))
                f_p = 1 - f.cdf(f_stat, 1, n - 3)
            else:
                f_stat = 0
                f_p = 1
            
            lvef_results.append({
                'metabolite': metabolite,
                'linear_corr': linear_corr,
                'linear_p': linear_p,
                'quadratic_r2': r_squared,
                'quadratic_coeff': coeffs[0],  # 二次项系数
                'f_stat': f_stat,
                'f_p': f_p,
                'u_shape': coeffs[0] > 0,  # 正的二次项系数表示U型
                'n_samples': n
            })
            
        except Exception as e:
            print(f"分析 {metabolite} 与LVEF关系时出错: {e}")
            continue
    
    return pd.DataFrame(lvef_results)

def survival_analysis(df, significant_metabolites):
    """生存分析"""
    print(f"\n正在进行生存分析...")
    
    survival_results = []
    
    for metabolite in significant_metabolites:
        try:
            # 准备生存数据
            mask = ~(df[metabolite].isna() | df['OS'].isna() | df['NonCV_death'].isna())
            if mask.sum() < 20:
                continue
                
            survival_df = df.loc[mask, [metabolite, 'OS', 'NonCV_death']].copy()
            
            # 将代谢物分为高低两组（中位数分割）
            median_value = survival_df[metabolite].median()
            survival_df['metabolite_high'] = (survival_df[metabolite] > median_value).astype(int)
            
            # Cox回归分析
            cph = CoxPHFitter()
            cox_df = survival_df[['OS', 'NonCV_death', 'metabolite_high']].copy()
            cox_df.columns = ['duration', 'event', 'metabolite_high']
            
            cph.fit(cox_df, duration_col='duration', event_col='event')
            
            # 提取结果
            hr = cph.hazard_ratios_['metabolite_high']
            ci_lower = cph.confidence_intervals_.loc['metabolite_high', 'coef lower 95%']
            ci_upper = cph.confidence_intervals_.loc['metabolite_high', 'coef upper 95%']
            p_value = cph.summary.loc['metabolite_high', 'p']
            
            # Log-rank检验
            high_group = survival_df[survival_df['metabolite_high'] == 1]
            low_group = survival_df[survival_df['metabolite_high'] == 0]
            
            logrank_result = logrank_test(
                high_group['OS'], low_group['OS'],
                high_group['NonCV_death'], low_group['NonCV_death']
            )
            
            survival_results.append({
                'metabolite': metabolite,
                'hazard_ratio': hr,
                'ci_lower': np.exp(ci_lower),
                'ci_upper': np.exp(ci_upper),
                'cox_p': p_value,
                'logrank_p': logrank_result.p_value,
                'n_high': len(high_group),
                'n_low': len(low_group),
                'events_high': high_group['NonCV_death'].sum(),
                'events_low': low_group['NonCV_death'].sum()
            })
            
        except Exception as e:
            print(f"生存分析 {metabolite} 时出错: {e}")
            continue
    
    return pd.DataFrame(survival_results)

def subgroup_analysis(df, metabolite_cols):
    """分组分析：分别在HFnEF和HFsnEF组中分析"""
    print(f"\n正在进行分组分析...")
    
    # HFnEF组 (HFsnEF = 0)
    hfnef_df = df[df['HFsnEF'] == 0].copy()
    print(f"HFnEF组样本数: {len(hfnef_df)}")
    hfnef_results = univariate_analysis(hfnef_df, metabolite_cols, 'NonCV_death')
    
    # HFsnEF组 (HFsnEF = 1)
    hfsnef_df = df[df['HFsnEF'] == 1].copy()
    print(f"HFsnEF组样本数: {len(hfsnef_df)}")
    hfsnef_results = univariate_analysis(hfsnef_df, metabolite_cols, 'NonCV_death')
    
    return hfnef_results, hfsnef_results

def create_visualizations(df, results_df, lvef_results, top_n=10):
    """创建可视化图表"""
    print(f"\n正在创建可视化图表...")
    
    # 获取最显著的代谢物
    top_metabolites = results_df.head(top_n)['metabolite'].tolist()
    
    # 1. 火山图
    plt.figure(figsize=(12, 8))
    plt.scatter(results_df['cohens_d'], -np.log10(results_df['p_value']), 
                alpha=0.6, s=30)
    
    # 标记显著的点
    significant = results_df['p_adjusted'] < 0.05
    plt.scatter(results_df.loc[significant, 'cohens_d'], 
                -np.log10(results_df.loc[significant, 'p_value']), 
                color='red', alpha=0.8, s=50)
    
    plt.xlabel('Cohen\'s d (效应量)')
    plt.ylabel('-log10(P值)')
    plt.title('代谢物与非心血管死亡关联的火山图')
    plt.axhline(y=-np.log10(0.05), color='gray', linestyle='--', alpha=0.5)
    plt.axvline(x=0, color='gray', linestyle='--', alpha=0.5)
    plt.tight_layout()
    plt.savefig('volcano_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 热图显示top代谢物
    if len(top_metabolites) > 0:
        plt.figure(figsize=(15, 8))
        
        # 准备热图数据
        heatmap_data = []
        for metabolite in top_metabolites[:10]:  # 限制为前10个
            mask = ~df[metabolite].isna()
            values_0 = df.loc[mask & (df['NonCV_death'] == 0), metabolite]
            values_1 = df.loc[mask & (df['NonCV_death'] == 1), metabolite]
            
            if len(values_0) > 0 and len(values_1) > 0:
                heatmap_data.append([values_0.mean(), values_1.mean()])
        
        if heatmap_data:
            heatmap_df = pd.DataFrame(heatmap_data, 
                                    index=[m.split('_')[0][:30] for m in top_metabolites[:len(heatmap_data)]], 
                                    columns=['非心血管死亡=0', '非心血管死亡=1'])
            
            sns.heatmap(heatmap_df, annot=True, cmap='RdYlBu_r', center=0, 
                       fmt='.2e', cbar_kws={'label': '代谢物浓度'})
            plt.title('显著代谢物在不同组间的表达水平')
            plt.tight_layout()
            plt.savefig('heatmap_top_metabolites.png', dpi=300, bbox_inches='tight')
            plt.show()

def main():
    """主函数"""
    print("=== 代谢组学与非心血管死亡关联分析 ===")

    # 1. 加载数据
    df, metabolite_cols = load_and_prepare_data()

    # 2. 整体人群分析
    print("\n=== 整体人群分析 ===")
    overall_results = univariate_analysis(df, metabolite_cols, 'NonCV_death')

    if len(overall_results) > 0:
        print(f"完成单变量分析，共分析了 {len(overall_results)} 个代谢物")

        # 显示最显著的结果 - 使用不同的阈值
        significant_results_strict = overall_results[overall_results['p_adjusted'] < 0.05]
        significant_results_nominal = overall_results[overall_results['p_value'] < 0.05]

        print(f"显著相关的代谢物数量 (FDR < 0.05): {len(significant_results_strict)}")
        print(f"显著相关的代谢物数量 (名义P < 0.05): {len(significant_results_nominal)}")

        # 显示前20个最显著的代谢物（按名义p值）
        print("\n前20个最显著的代谢物（按名义P值）:")
        display_cols = ['metabolite', 'p_value', 'p_adjusted', 'cohens_d', 'mean_group0', 'mean_group1']
        top_20 = overall_results.head(20)
        for i, row in top_20.iterrows():
            metabolite_name = row['metabolite'].split('_')[0][:50]  # 截断长名称
            print(f"{metabolite_name:<50} P={row['p_value']:.4f} FDR={row['p_adjusted']:.4f} d={row['cohens_d']:.3f}")

        # 3. LVEF关系分析（使用名义显著的代谢物）
        if len(significant_results_nominal) > 0:
            print(f"\n=== LVEF关系分析 ===")
            significant_metabolites = significant_results_nominal['metabolite'].tolist()[:50]  # 限制前50个
            lvef_results = analyze_lvef_relationship(df, significant_metabolites)

            if len(lvef_results) > 0:
                print(f"完成LVEF关系分析，共分析了 {len(lvef_results)} 个代谢物")

                # 检查U型关系
                u_shape_metabolites = lvef_results[
                    (lvef_results['f_p'] < 0.05) & (lvef_results['u_shape'] == True)
                ]
                print(f"呈现U型关系的代谢物数量: {len(u_shape_metabolites)}")

                if len(u_shape_metabolites) > 0:
                    print("\n呈现U型关系的代谢物:")
                    for i, row in u_shape_metabolites.iterrows():
                        metabolite_name = row['metabolite'].split('_')[0][:40]
                        print(f"{metabolite_name:<40} 线性r={row['linear_corr']:.3f} 二次R²={row['quadratic_r2']:.3f} F_p={row['f_p']:.4f}")

                # 保存LVEF分析结果
                lvef_results.to_csv('lvef_relationship_results.csv', index=False)
                print("LVEF关系分析结果已保存到: lvef_relationship_results.csv")

        # 4. 分组分析
        print("\n=== 分组分析 ===")
        hfnef_results, hfsnef_results = subgroup_analysis(df, metabolite_cols)

        # HFnEF组结果
        if len(hfnef_results) > 0:
            hfnef_significant_strict = hfnef_results[hfnef_results['p_adjusted'] < 0.05]
            hfnef_significant_nominal = hfnef_results[hfnef_results['p_value'] < 0.05]
            print(f"HFnEF组显著相关的代谢物数量 (FDR < 0.05): {len(hfnef_significant_strict)}")
            print(f"HFnEF组显著相关的代谢物数量 (名义P < 0.05): {len(hfnef_significant_nominal)}")

            if len(hfnef_significant_nominal) > 0:
                print("\nHFnEF组前10个最显著的代谢物:")
                for i, row in hfnef_results.head(10).iterrows():
                    metabolite_name = row['metabolite'].split('_')[0][:40]
                    print(f"{metabolite_name:<40} P={row['p_value']:.4f} FDR={row['p_adjusted']:.4f}")

            hfnef_results.to_csv('hfnef_metabolite_results.csv', index=False)
            print("HFnEF组分析结果已保存到: hfnef_metabolite_results.csv")

        # HFsnEF组结果
        if len(hfsnef_results) > 0:
            hfsnef_significant_strict = hfsnef_results[hfsnef_results['p_adjusted'] < 0.05]
            hfsnef_significant_nominal = hfsnef_results[hfsnef_results['p_value'] < 0.05]
            print(f"HFsnEF组显著相关的代谢物数量 (FDR < 0.05): {len(hfsnef_significant_strict)}")
            print(f"HFsnEF组显著相关的代谢物数量 (名义P < 0.05): {len(hfsnef_significant_nominal)}")

            if len(hfsnef_significant_nominal) > 0:
                print("\nHFsnEF组前10个最显著的代谢物:")
                for i, row in hfsnef_results.head(10).iterrows():
                    metabolite_name = row['metabolite'].split('_')[0][:40]
                    print(f"{metabolite_name:<40} P={row['p_value']:.4f} FDR={row['p_adjusted']:.4f}")

            hfsnef_results.to_csv('hfsnef_metabolite_results.csv', index=False)
            print("HFsnEF组分析结果已保存到: hfsnef_metabolite_results.csv")

        # 5. 生存分析（使用名义显著的代谢物）
        if len(significant_results_nominal) > 0:
            print(f"\n=== 生存分析 ===")
            survival_metabolites = significant_results_nominal['metabolite'].tolist()[:20]  # 限制前20个
            survival_results = survival_analysis(df, survival_metabolites)

            if len(survival_results) > 0:
                print(f"完成生存分析，共分析了 {len(survival_results)} 个代谢物")
                survival_significant = survival_results[survival_results['cox_p'] < 0.05]
                print(f"生存分析显著的代谢物数量: {len(survival_significant)}")

                if len(survival_significant) > 0:
                    print("\n生存分析显著的代谢物:")
                    for i, row in survival_significant.iterrows():
                        metabolite_name = row['metabolite'].split('_')[0][:40]
                        print(f"{metabolite_name:<40} HR={row['hazard_ratio']:.3f} 95%CI=[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}] P={row['cox_p']:.4f}")

                survival_results.to_csv('survival_analysis_results.csv', index=False)
                print("生存分析结果已保存到: survival_analysis_results.csv")

        # 6. 创建可视化
        if len(significant_results_nominal) > 0:
            print(f"\n=== 创建可视化图表 ===")
            create_visualizations(df, overall_results,
                                lvef_results if 'lvef_results' in locals() else pd.DataFrame())

        # 保存整体结果
        overall_results.to_csv('overall_metabolite_results.csv', index=False)
        print("整体分析结果已保存到: overall_metabolite_results.csv")

    else:
        print("未找到可分析的代谢物数据")

    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
