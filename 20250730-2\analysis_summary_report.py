#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢组学分析结果总结报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_summary_report():
    """生成分析结果总结报告"""
    
    print("=" * 80)
    print("代谢组学与非心血管死亡关联分析 - 结果总结报告")
    print("=" * 80)
    
    # 1. 读取整体分析结果
    print("\n1. 整体人群分析结果")
    print("-" * 50)
    
    overall_results = pd.read_csv('overall_metabolite_results.csv')
    
    # 统计显著性结果
    fdr_significant = overall_results[overall_results['p_adjusted'] < 0.05]
    nominal_significant = overall_results[overall_results['p_value'] < 0.05]
    
    print(f"总分析代谢物数量: {len(overall_results)}")
    print(f"FDR校正后显著 (q < 0.05): {len(fdr_significant)} 个")
    print(f"名义显著 (p < 0.05): {len(nominal_significant)} 个")
    
    if len(nominal_significant) > 0:
        print(f"\n前10个最显著的代谢物:")
        print("-" * 30)
        for i, row in nominal_significant.head(10).iterrows():
            metabolite_name = row['metabolite'].split('_')[0]
            direction = "↑" if row['cohens_d'] > 0 else "↓"
            print(f"{i+1:2d}. {metabolite_name[:50]:<50} {direction} P={row['p_value']:.4f} d={row['cohens_d']:.3f}")
    
    # 2. LVEF关系分析结果
    print(f"\n2. LVEF关系分析结果")
    print("-" * 50)
    
    try:
        lvef_results = pd.read_csv('lvef_relationship_results.csv')
        u_shape_metabolites = lvef_results[(lvef_results['f_p'] < 0.05) & (lvef_results['u_shape'] == True)]
        
        print(f"分析的代谢物数量: {len(lvef_results)}")
        print(f"呈现U型关系的代谢物数量: {len(u_shape_metabolites)}")
        
        if len(u_shape_metabolites) > 0:
            print(f"\n呈现U型关系的代谢物:")
            print("-" * 30)
            for i, row in u_shape_metabolites.iterrows():
                metabolite_name = row['metabolite'].split('_')[0]
                print(f"• {metabolite_name[:60]}")
                print(f"  线性相关系数: {row['linear_corr']:.3f} (P={row['linear_p']:.4f})")
                print(f"  二次模型R²: {row['quadratic_r2']:.3f}")
                print(f"  U型检验P值: {row['f_p']:.4f}")
                print()
    except FileNotFoundError:
        print("LVEF关系分析结果文件未找到")
    
    # 3. 分组分析结果
    print(f"\n3. 分组分析结果")
    print("-" * 50)
    
    # HFnEF组
    try:
        hfnef_results = pd.read_csv('hfnef_metabolite_results.csv')
        hfnef_significant = hfnef_results[hfnef_results['p_value'] < 0.05]
        
        print(f"HFnEF组 (HFsnEF=0):")
        print(f"  分析代谢物数量: {len(hfnef_results)}")
        print(f"  名义显著代谢物: {len(hfnef_significant)} 个")
        
        if len(hfnef_significant) > 0:
            print(f"  前5个最显著的代谢物:")
            for i, row in hfnef_significant.head(5).iterrows():
                metabolite_name = row['metabolite'].split('_')[0]
                direction = "↑" if row['cohens_d'] > 0 else "↓"
                print(f"    {metabolite_name[:40]:<40} {direction} P={row['p_value']:.4f}")
    except FileNotFoundError:
        print("HFnEF组分析结果文件未找到")
    
    # HFsnEF组
    try:
        hfsnef_results = pd.read_csv('hfsnef_metabolite_results.csv')
        hfsnef_significant = hfsnef_results[hfsnef_results['p_value'] < 0.05]
        
        print(f"\nHFsnEF组 (HFsnEF=1):")
        print(f"  分析代谢物数量: {len(hfsnef_results)}")
        print(f"  名义显著代谢物: {len(hfsnef_significant)} 个")
        
        if len(hfsnef_significant) > 0:
            print(f"  前5个最显著的代谢物:")
            for i, row in hfsnef_significant.head(5).iterrows():
                metabolite_name = row['metabolite'].split('_')[0]
                direction = "↑" if row['cohens_d'] > 0 else "↓"
                print(f"    {metabolite_name[:40]:<40} {direction} P={row['p_value']:.4f}")
    except FileNotFoundError:
        print("HFsnEF组分析结果文件未找到")
    
    # 4. 生存分析结果
    print(f"\n4. 生存分析结果")
    print("-" * 50)
    
    try:
        survival_results = pd.read_csv('survival_analysis_results.csv')
        survival_significant = survival_results[survival_results['cox_p'] < 0.05]
        
        print(f"分析的代谢物数量: {len(survival_results)}")
        print(f"生存分析显著的代谢物: {len(survival_significant)} 个")
        
        if len(survival_significant) > 0:
            print(f"\n生存分析显著的代谢物:")
            print("-" * 30)
            for i, row in survival_significant.iterrows():
                metabolite_name = row['metabolite'].split('_')[0]
                print(f"• {metabolite_name[:50]}")
                print(f"  风险比 (HR): {row['hazard_ratio']:.3f}")
                print(f"  95%置信区间: [{row['ci_lower']:.3f} - {row['ci_upper']:.3f}]")
                print(f"  Cox回归P值: {row['cox_p']:.4f}")
                print()
    except FileNotFoundError:
        print("生存分析结果文件未找到")
    
    # 5. 关键发现总结
    print(f"\n5. 关键发现总结")
    print("-" * 50)
    
    print("主要发现:")
    print("• 在整体人群中，发现64个代谢物与非心血管死亡呈名义显著关联")
    print("• 经FDR多重检验校正后，未发现严格意义上的显著关联")
    print("• 发现1个代谢物与LVEF呈现U型关系:")
    print("  - (6-carboxy-3,4,5-trihydroxyoxan-2-yl)[2-(3,4-dihydroxyphenyl)...]")
    print("• HFnEF组和HFsnEF组分别发现58和60个名义显著的代谢物")
    
    print(f"\n临床意义:")
    print("• 代谢物Withaperuvin H在非心血管死亡组中显著降低 (P=0.0003)")
    print("• 多个溶血磷脂酰胆碱(LysoPC)类代谢物与非心血管死亡相关")
    print("• 氨基酸代谢相关的代谢物(如L-谷氨酸、L-正亮氨酸)显示关联性")
    
    print(f"\n研究局限性:")
    print("• 样本量相对较小(n=202)，非心血管死亡事件较少(n=15)")
    print("• 多重检验校正后未达到严格显著性标准")
    print("• 需要更大样本量的验证研究")
    
    # 6. 创建可视化总结
    create_summary_visualizations()
    
    print(f"\n6. 输出文件")
    print("-" * 50)
    print("已生成以下结果文件:")
    print("• overall_metabolite_results.csv - 整体分析结果")
    print("• hfnef_metabolite_results.csv - HFnEF组分析结果")
    print("• hfsnef_metabolite_results.csv - HFsnEF组分析结果")
    print("• lvef_relationship_results.csv - LVEF关系分析结果")
    print("• volcano_plot.png - 火山图")
    print("• heatmap_top_metabolites.png - 热图")
    print("• summary_plots.png - 总结图表")
    
    print("\n" + "=" * 80)
    print("分析报告生成完成")
    print("=" * 80)

def create_summary_visualizations():
    """创建总结性可视化图表"""
    
    # 读取数据
    overall_results = pd.read_csv('overall_metabolite_results.csv')
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('代谢组学分析结果总结', fontsize=16, fontweight='bold')
    
    # 1. P值分布直方图
    axes[0, 0].hist(overall_results['p_value'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(x=0.05, color='red', linestyle='--', label='P=0.05')
    axes[0, 0].set_xlabel('P值')
    axes[0, 0].set_ylabel('代谢物数量')
    axes[0, 0].set_title('P值分布')
    axes[0, 0].legend()
    
    # 2. 效应量分布
    axes[0, 1].hist(overall_results['cohens_d'], bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 1].axvline(x=0, color='red', linestyle='--', label='无效应')
    axes[0, 1].set_xlabel('Cohen\'s d (效应量)')
    axes[0, 1].set_ylabel('代谢物数量')
    axes[0, 1].set_title('效应量分布')
    axes[0, 1].legend()
    
    # 3. 火山图
    significant = overall_results['p_value'] < 0.05
    axes[1, 0].scatter(overall_results['cohens_d'], -np.log10(overall_results['p_value']), 
                      alpha=0.6, s=20, color='gray')
    axes[1, 0].scatter(overall_results.loc[significant, 'cohens_d'], 
                      -np.log10(overall_results.loc[significant, 'p_value']), 
                      color='red', alpha=0.8, s=30)
    axes[1, 0].axhline(y=-np.log10(0.05), color='blue', linestyle='--', alpha=0.7, label='P=0.05')
    axes[1, 0].axvline(x=0, color='gray', linestyle='--', alpha=0.5)
    axes[1, 0].set_xlabel('Cohen\'s d (效应量)')
    axes[1, 0].set_ylabel('-log10(P值)')
    axes[1, 0].set_title('火山图')
    axes[1, 0].legend()
    
    # 4. 显著性统计
    categories = ['总代谢物', '名义显著\n(P<0.05)', 'FDR显著\n(q<0.05)']
    counts = [len(overall_results), 
              len(overall_results[overall_results['p_value'] < 0.05]),
              len(overall_results[overall_results['p_adjusted'] < 0.05])]
    
    bars = axes[1, 1].bar(categories, counts, color=['lightblue', 'orange', 'red'], alpha=0.7)
    axes[1, 1].set_ylabel('代谢物数量')
    axes[1, 1].set_title('显著性统计')
    
    # 在柱状图上添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 5,
                        f'{count}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('summary_plots.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    generate_summary_report()
