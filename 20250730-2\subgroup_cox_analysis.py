#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HFnEF和HFsnEF亚组Cox回归分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import warnings
warnings.filterwarnings('ignore')

# 设置字体
matplotlib.rcParams['font.family'] = ['DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def perform_subgroup_cox_analysis():
    """对HFnEF和HFsnEF两组分别进行Cox回归分析"""
    
    print("=" * 80)
    print("HFnEF和HFsnEF亚组Cox回归分析")
    print("=" * 80)
    
    # 读取数据
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    
    # 获取所有代谢物列
    metabolite_cols = [col for col in df.columns if '_POS' in col or '_NEG' in col]
    
    print(f"数据概况:")
    print(f"总样本数: {len(df)}")
    print(f"代谢物总数: {len(metabolite_cols)}")
    
    # 清理数据
    analysis_df = df.dropna(subset=['NonCV_death', 'OS', 'HFsnEF'])
    print(f"有效样本数: {len(analysis_df)}")
    
    # 分组统计
    hfnef_df = analysis_df[analysis_df['HFsnEF'] == 0].copy()
    hfsnef_df = analysis_df[analysis_df['HFsnEF'] == 1].copy()
    
    print(f"HFnEF组样本数: {len(hfnef_df)} (事件数: {int(hfnef_df['NonCV_death'].sum())})")
    print(f"HFsnEF组样本数: {len(hfsnef_df)} (事件数: {int(hfsnef_df['NonCV_death'].sum())})")
    print()
    
    # 分别进行Cox回归分析
    print("正在进行HFnEF组Cox回归分析...")
    hfnef_results = perform_single_group_cox(hfnef_df, metabolite_cols, "HFnEF")
    
    print("正在进行HFsnEF组Cox回归分析...")
    hfsnef_results = perform_single_group_cox(hfsnef_df, metabolite_cols, "HFsnEF")
    
    return hfnef_results, hfsnef_results

def perform_single_group_cox(df, metabolite_cols, group_name):
    """对单个组进行Cox回归分析"""
    
    from lifelines import CoxPHFitter
    
    cox_results = []
    failed_metabolites = []
    
    print(f"进度: ", end="", flush=True)
    
    for i, metabolite in enumerate(metabolite_cols):
        # 显示进度
        if i % 50 == 0:
            print(f"{i}/{len(metabolite_cols)}", end=" ", flush=True)
        
        try:
            # 准备数据
            mask = ~(df[metabolite].isna() | df['OS'].isna() | df['NonCV_death'].isna())
            if mask.sum() < 10:  # 样本量太小跳过
                failed_metabolites.append((metabolite, "样本量不足"))
                continue
                
            cox_df = df.loc[mask, [metabolite, 'OS', 'NonCV_death']].copy()
            
            # 检查代谢物是否有变异
            if cox_df[metabolite].std() == 0:
                failed_metabolites.append((metabolite, "无变异"))
                continue
            
            # 检查事件数
            if cox_df['NonCV_death'].sum() < 2:
                failed_metabolites.append((metabolite, "事件数不足"))
                continue
            
            # 标准化代谢物浓度
            cox_df[f'{metabolite}_std'] = (cox_df[metabolite] - cox_df[metabolite].mean()) / cox_df[metabolite].std()
            
            # 准备Cox回归数据
            cox_data = cox_df[['OS', 'NonCV_death', f'{metabolite}_std']].copy()
            cox_data.columns = ['duration', 'event', 'metabolite_std']
            
            # 拟合Cox模型
            cph = CoxPHFitter()
            cph.fit(cox_data, duration_col='duration', event_col='event')
            
            # 提取结果
            coef = cph.params_['metabolite_std']
            hr = np.exp(coef)
            se = cph.standard_errors_['metabolite_std']
            ci_lower = np.exp(coef - 1.96 * se)
            ci_upper = np.exp(coef + 1.96 * se)
            p_value = cph.summary.loc['metabolite_std', 'p']
            z_score = cph.summary.loc['metabolite_std', 'z']
            
            # 计算C-index
            c_index = cph.concordance_index_
            
            cox_results.append({
                'metabolite': metabolite,
                'group': group_name,
                'coefficient': coef,
                'hazard_ratio': hr,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'p_value': p_value,
                'z_score': z_score,
                'c_index': c_index,
                'n_samples': len(cox_data),
                'n_events': int(cox_data['event'].sum()),
                'mean_metabolite': cox_df[metabolite].mean(),
                'std_metabolite': cox_df[metabolite].std(),
                'analysis_status': 'success'
            })
            
        except Exception as e:
            failed_metabolites.append((metabolite, f"分析错误: {str(e)}"))
            continue
    
    print(f"\n完成!")
    
    # 转换为DataFrame
    cox_results_df = pd.DataFrame(cox_results)
    
    print(f"\n{group_name}组分析结果:")
    print(f"成功分析的代谢物: {len(cox_results_df)}")
    print(f"失败的代谢物: {len(failed_metabolites)}")
    
    if len(cox_results_df) > 0:
        # 按p值排序
        cox_results_df = cox_results_df.sort_values('p_value')
        
        # 显著性统计
        significant_cox = cox_results_df[cox_results_df['p_value'] < 0.05]
        print(f"显著相关的代谢物 (P < 0.05): {len(significant_cox)}")
        
        # 保存结果
        filename = f'{group_name.lower()}_cox_results.csv'
        cox_results_df.to_csv(filename, index=False)
        print(f"{group_name}组Cox分析结果已保存到: {filename}")
        
        # 保存失败的代谢物信息
        if failed_metabolites:
            failed_filename = f'{group_name.lower()}_failed_metabolites.csv'
            failed_df = pd.DataFrame(failed_metabolites, columns=['metabolite', 'failure_reason'])
            failed_df.to_csv(failed_filename, index=False)
            print(f"失败的代谢物信息已保存到: {failed_filename}")
        
        # 显示前10个最显著的结果
        print(f"\n{group_name}组前10个最显著的代谢物:")
        print("=" * 100)
        print(f"{'代谢物名称':<40} {'HR':<8} {'95%CI':<20} {'P值':<10} {'方向':<6}")
        print("-" * 100)
        
        for i, row in cox_results_df.head(10).iterrows():
            metabolite_name = row['metabolite'].split('_')[0][:35]
            hr_str = f"{row['hazard_ratio']:.3f}"
            ci_str = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
            p_str = f"{row['p_value']:.4f}"
            direction = "保护" if row['hazard_ratio'] < 1 else "危险"
            
            print(f"{metabolite_name:<40} {hr_str:<8} {ci_str:<20} {p_str:<10} {direction:<6}")
        
        return cox_results_df
    
    else:
        print(f"未能完成{group_name}组任何代谢物的Cox回归分析")
        return pd.DataFrame()

def create_subgroup_volcano_plots(hfnef_results, hfsnef_results):
    """创建亚组火山图"""
    
    print(f"\n正在创建亚组火山图...")
    
    # 创建并排的子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 绘制HFnEF组火山图
    create_single_volcano_plot(hfnef_results, ax1, "HFnEF Group")
    
    # 绘制HFsnEF组火山图
    create_single_volcano_plot(hfsnef_results, ax2, "HFsnEF Group")
    
    plt.tight_layout()
    plt.savefig('subgroup_volcano_plots.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    print("亚组火山图已保存到: subgroup_volcano_plots.png")

def create_single_volcano_plot(cox_results, ax, title):
    """创建单个火山图"""
    
    if len(cox_results) == 0:
        ax.text(0.5, 0.5, 'No data available', ha='center', va='center', 
                transform=ax.transAxes, fontsize=14)
        ax.set_title(title, fontsize=14, fontweight='bold')
        return
    
    # 准备数据
    plot_data = cox_results.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义显著性阈值
    p_threshold = 0.05
    
    # 分类
    plot_data['significance'] = 'Non-significant'
    significant_protective = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] < 1)
    significant_risk = (plot_data['p_value'] < p_threshold) & (plot_data['hazard_ratio'] > 1)
    
    plot_data.loc[significant_protective, 'significance'] = 'Significant Protective'
    plot_data.loc[significant_risk, 'significance'] = 'Significant Risk'
    
    # 设置背景色
    ax.set_facecolor('white')
    
    # 定义颜色
    colors = {
        'Non-significant': '#CCCCCC',
        'Significant Protective': '#1E88E5',
        'Significant Risk': '#D32F2F'
    }
    
    # 绘制散点图
    for category in ['Non-significant', 'Significant Protective', 'Significant Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.6 if category == 'Non-significant' else 0.8
            size = 25 if category == 'Non-significant' else 40
            zorder = 1 if category == 'Non-significant' else 3
            
            ax.scatter(plot_data.loc[mask, 'log2_hr'], 
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category], 
                      alpha=alpha,
                      s=size,
                      zorder=zorder,
                      edgecolors='white' if category != 'Non-significant' else 'none',
                      linewidth=0.5)
    
    # 添加阈值线
    ax.axhline(y=-np.log10(p_threshold), color='black', linestyle='--', 
               alpha=0.8, linewidth=1.5, zorder=2)
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.6, linewidth=1, zorder=2)
    
    # 设置坐标轴范围 - 固定范围，缩窄两侧空白
    ax.set_xlim(-4.2, 4.2)

    if len(plot_data) > 0:
        y_max = plot_data['neg_log10_p'].max() * 1.05
        ax.set_ylim(0, max(y_max, 3))  # 确保最小高度
    else:
        ax.set_ylim(0, 3)
    
    # 设置刻度
    ax.set_xticks([-4, -2, 0, 2, 4])
    ax.set_xticklabels(['-4', '-2', '0', '2', '4'], fontsize=11)
    ax.tick_params(axis='y', labelsize=11)
    
    # 设置标签和标题
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=12, fontweight='bold')
    ax.set_ylabel('-log₁₀(P value)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=15)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 添加P=0.05标注
    if len(plot_data) > 0:
        ax.text(-3.8, -np.log10(p_threshold) + 0.1, 'P = 0.05',
                fontsize=10, color='black', fontweight='bold')

        # 添加保护性/危险性标注
        ax.text(-3.8, 0.2, 'Protective', fontsize=11, color='#1E88E5',
                fontweight='bold', ha='left')
        ax.text(3.5, 0.2, 'Risk', fontsize=11, color='#D32F2F',
                fontweight='bold', ha='right')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)
    
    # 设置刻度参数
    ax.tick_params(axis='both', which='major', labelsize=11, 
                   length=6, width=1.5, direction='out')
    
    # 添加统计信息
    if len(plot_data) > 0:
        stats_text = f"Total: {len(plot_data)}\n"
        stats_text += f"Protective: {significant_protective.sum()}\n"
        stats_text += f"Risk: {significant_risk.sum()}"

        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                verticalalignment='top', fontsize=9,
                bbox=dict(boxstyle='round,pad=0.4', facecolor='white',
                         alpha=0.9, edgecolor='gray'))

def create_individual_volcano_plots(hfnef_results, hfsnef_results):
    """创建单独的火山图"""
    
    print(f"\n正在创建单独的火山图...")
    
    # HFnEF组火山图
    if len(hfnef_results) > 0:
        fig, ax = plt.subplots(figsize=(10, 8))
        create_single_volcano_plot(hfnef_results, ax, "HFnEF Group - Cox Regression Analysis")
        plt.tight_layout()
        plt.savefig('hfnef_volcano_plot.png', dpi=300, bbox_inches='tight', 
                    facecolor='white', edgecolor='none')
        plt.show()
        print("HFnEF组火山图已保存到: hfnef_volcano_plot.png")
    
    # HFsnEF组火山图
    if len(hfsnef_results) > 0:
        fig, ax = plt.subplots(figsize=(10, 8))
        create_single_volcano_plot(hfsnef_results, ax, "HFsnEF Group - Cox Regression Analysis")
        plt.tight_layout()
        plt.savefig('hfsnef_volcano_plot.png', dpi=300, bbox_inches='tight', 
                    facecolor='white', edgecolor='none')
        plt.show()
        print("HFsnEF组火山图已保存到: hfsnef_volcano_plot.png")

def create_summary_report(hfnef_results, hfsnef_results):
    """创建总结报告"""
    
    print(f"\n正在创建亚组分析总结报告...")
    
    report = []
    report.append("# HFnEF和HFsnEF亚组Cox回归分析报告\n")
    
    # HFnEF组结果
    if len(hfnef_results) > 0:
        hfnef_sig = hfnef_results[hfnef_results['p_value'] < 0.05]
        hfnef_protective = hfnef_sig[hfnef_sig['hazard_ratio'] < 1]
        hfnef_risk = hfnef_sig[hfnef_sig['hazard_ratio'] > 1]
        
        report.append("## HFnEF组结果\n")
        report.append(f"- 总代谢物数: {len(hfnef_results)}\n")
        report.append(f"- 显著相关代谢物: {len(hfnef_sig)}\n")
        report.append(f"- 显著保护性: {len(hfnef_protective)}\n")
        report.append(f"- 显著危险性: {len(hfnef_risk)}\n\n")
        
        if len(hfnef_sig) > 0:
            report.append("### 前5个最显著的代谢物:\n")
            for i, row in hfnef_sig.head(5).iterrows():
                metabolite_name = row['metabolite'].split('_')[0][:40]
                direction = "保护性" if row['hazard_ratio'] < 1 else "危险性"
                report.append(f"{i+1}. {metabolite_name} - HR={row['hazard_ratio']:.3f} [{row['ci_lower']:.3f}-{row['ci_upper']:.3f}] P={row['p_value']:.4f} ({direction})\n")
            report.append("\n")
    
    # HFsnEF组结果
    if len(hfsnef_results) > 0:
        hfsnef_sig = hfsnef_results[hfsnef_results['p_value'] < 0.05]
        hfsnef_protective = hfsnef_sig[hfsnef_sig['hazard_ratio'] < 1]
        hfsnef_risk = hfsnef_sig[hfsnef_sig['hazard_ratio'] > 1]
        
        report.append("## HFsnEF组结果\n")
        report.append(f"- 总代谢物数: {len(hfsnef_results)}\n")
        report.append(f"- 显著相关代谢物: {len(hfsnef_sig)}\n")
        report.append(f"- 显著保护性: {len(hfsnef_protective)}\n")
        report.append(f"- 显著危险性: {len(hfsnef_risk)}\n\n")
        
        if len(hfsnef_sig) > 0:
            report.append("### 前5个最显著的代谢物:\n")
            for i, row in hfsnef_sig.head(5).iterrows():
                metabolite_name = row['metabolite'].split('_')[0][:40]
                direction = "保护性" if row['hazard_ratio'] < 1 else "危险性"
                report.append(f"{i+1}. {metabolite_name} - HR={row['hazard_ratio']:.3f} [{row['ci_lower']:.3f}-{row['ci_upper']:.3f}] P={row['p_value']:.4f} ({direction})\n")
            report.append("\n")
    
    # 保存报告
    with open('subgroup_cox_analysis_report.md', 'w', encoding='utf-8') as f:
        f.writelines(report)
    
    print("亚组分析报告已保存到: subgroup_cox_analysis_report.md")

def main():
    """主函数"""
    print("开始HFnEF和HFsnEF亚组Cox回归分析...")
    
    # 进行亚组Cox回归分析
    hfnef_results, hfsnef_results = perform_subgroup_cox_analysis()
    
    # 创建火山图
    if len(hfnef_results) > 0 or len(hfsnef_results) > 0:
        # 创建并排火山图
        create_subgroup_volcano_plots(hfnef_results, hfsnef_results)
        
        # 创建单独火山图
        create_individual_volcano_plots(hfnef_results, hfsnef_results)
        
        # 创建总结报告
        create_summary_report(hfnef_results, hfsnef_results)
        
        print(f"\n=== 亚组分析完成 ===")
        print(f"生成的文件:")
        print(f"• hfnef_cox_results.csv - HFnEF组Cox分析结果")
        print(f"• hfsnef_cox_results.csv - HFsnEF组Cox分析结果")
        print(f"• subgroup_volcano_plots.png - 并排火山图")
        print(f"• hfnef_volcano_plot.png - HFnEF组单独火山图")
        print(f"• hfsnef_volcano_plot.png - HFsnEF组单独火山图")
        print(f"• subgroup_cox_analysis_report.md - 分析报告")
    
    print(f"\n亚组Cox回归分析完成!")

if __name__ == "__main__":
    main()
