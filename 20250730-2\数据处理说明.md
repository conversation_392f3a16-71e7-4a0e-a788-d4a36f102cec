# 代谢组数据处理说明

## 概述
本文档说明了对合并后的代谢组数据文件 `merged_metabolomics_separate.tsv` 进行的数据清理和格式化处理。

## 处理目标
1. 删除质控样本（QC）列
2. 简化样本名称：去掉HFsnEF和HFnEF前缀，只保留下划线后的标识符
3. 删除统计分析列

## 原始数据特征

### 输入文件: `merged_metabolomics_separate.tsv`
- **数据维度**: 698行 × 223列
- **代谢物记录**: 698个（423个负离子模式 + 275个正离子模式）
- **样本列**: 203个
- **质控列**: 11个（QC1-QC11）
- **统计分析列**: 5个

### 列结构分析
- **基本信息列**: CPD_ID, CPD_Name, CPD_ID_with_mode, Ion_Mode
- **质控样本列**: QC1, QC2, QC3, QC4, QC5, QC6, QC7, QC8, QC9, QC10, QC11
- **样本数据列**: HFnEF_87, HFnEF_284, HFsnEF_165, HFsnEF_196, 等
- **统计分析列**: VIP(HFsnEF_vs_HFnEF), FC(HFsnEF_vs_HFnEF), pValue(HFsnEF_vs_HFnEF), FDR(HFsnEF_vs_HFnEF), Sig(HFsnEF_vs_HFnEF)

## 处理步骤

### 1. 删除质控样本列
删除所有包含"QC"文本的列：
- 删除列数: 11个
- 删除的列: `QC1`, `QC2`, `QC3`, `QC4`, `QC5`, `QC6`, `QC7`, `QC8`, `QC9`, `QC10`, `QC11`

### 2. 简化样本名称
对所有样本列进行重命名：

#### 重命名规则
- `HFnEF_87` → `87`
- `HFnEF_284` → `284`
- `HFnEF_F156` → `F156`
- `HFsnEF_165` → `165`
- `HFsnEF_C2` → `C2`
- `HFsnEF_F154` → `F154`

#### 处理逻辑
```python
# 去掉HFnEF_前缀
if column.startswith('HFnEF_'):
    new_name = column.replace('HFnEF_', '')

# 去掉HFsnEF_前缀  
elif column.startswith('HFsnEF_'):
    new_name = column.replace('HFsnEF_', '')
```

### 3. 删除统计分析列
删除以下5个统计分析列：
- `VIP(HFsnEF_vs_HFnEF)`
- `FC(HFsnEF_vs_HFnEF)`
- `pValue(HFsnEF_vs_HFnEF)`
- `FDR(HFsnEF_vs_HFnEF)`
- `Sig(HFsnEF_vs_HFnEF)`

## 处理结果

### 输出文件: `processed_metabolomics_data.tsv`
- **数据维度**: 698行 × 207列
- **保留的代谢物记录**: 698个（无数据丢失）
- **保留的样本列**: 203个
- **删除的列总数**: 16个（11个QC列 + 5个统计列）

### 最终列结构
1. **基本信息列** (4个):
   - `CPD_ID`: 代谢物编号
   - `CPD_Name`: 代谢物名称
   - `CPD_ID_with_mode`: 带离子模式标识的代谢物编号
   - `Ion_Mode`: 离子模式（NEG/POS）

2. **样本数据列** (203个):
   - 简化后的样本标识符：`87`, `284`, `299`, `616`, `664`, `F156`, `259`, `570`, 等
   - 包含数字标识符：`1`, `8`, `9`, `28`, `39`, `41`, 等
   - 包含字母标识符：`C1`, `C2`, `C4`, `C5`, `F105`, `F114`, 等

### 样本名称示例
| 原始名称 | 处理后名称 |
|---------|-----------|
| HFnEF_87 | 87 |
| HFnEF_284 | 284 |
| HFnEF_F156 | F156 |
| HFnEF_C1 | C1 |
| HFsnEF_165 | 165 |
| HFsnEF_196 | 196 |
| HFsnEF_C2 | C2 |
| HFsnEF_F154 | F154 |

## 数据质量验证

### ✅ 验证通过项目
1. **数据完整性**: 行数保持不变（698行）
2. **关键列保留**: CPD_ID, CPD_Name, CPD_ID_with_mode, Ion_Mode全部保留
3. **QC列删除**: 所有QC列已完全删除
4. **统计列删除**: 所有统计分析列已完全删除
5. **样本列重命名**: 所有HF前缀已去除
6. **离子模式分布**: 负离子模式423个，正离子模式275个

### 📊 处理统计
- **原始列数**: 223
- **处理后列数**: 207
- **删除列数**: 16
- **重命名列数**: 203
- **数据保留率**: 100%（无数据丢失）

## 使用建议

### 1. 数据分析
处理后的数据更适合进行：
- 样本间比较分析
- 代谢物表达模式分析
- 机器学习建模
- 统计分析

### 2. 样本识别
- 纯数字样本ID：如 `87`, `284`, `299` 等
- 字母数字组合：如 `F156`, `C1`, `F154` 等
- 可根据需要进一步分组或分类

### 3. 数据读取示例
```python
import pandas as pd

# 读取处理后的数据
df = pd.read_csv('processed_metabolomics_data.tsv', sep='\t')

# 获取样本数据（排除基本信息列）
sample_columns = [col for col in df.columns 
                 if col not in ['CPD_ID', 'CPD_Name', 'CPD_ID_with_mode', 'Ion_Mode']]
sample_data = df[sample_columns]

# 按离子模式分组
neg_data = df[df['Ion_Mode'] == 'NEG']
pos_data = df[df['Ion_Mode'] == 'POS']
```

## 技术实现

### 使用的脚本
- **主处理脚本**: `process_metabolomics_data.py`
- **功能**: 自动化数据清理和格式化
- **验证**: 内置数据质量检查

### 处理流程
1. 读取原始TSV文件
2. 识别并删除QC列
3. 识别并删除统计分析列
4. 重命名样本列（去除HF前缀）
5. 保存处理后的数据
6. 验证处理结果

## 结论

数据处理成功完成，生成了更简洁、更适合分析的代谢组数据集。处理后的数据保持了所有原始代谢物信息，同时去除了不必要的质控和统计列，简化了样本标识符，为后续的代谢组学分析提供了清洁的数据基础。

**推荐使用**: `processed_metabolomics_data.tsv` 进行后续的代谢组学数据分析。
