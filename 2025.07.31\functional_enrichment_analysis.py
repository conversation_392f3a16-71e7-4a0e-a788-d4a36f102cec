#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HFsnEF特异蛋白功能富集分析和网络分析
基于蛋白质簇进行功能注释和通路富集分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_clustered_data():
    """加载聚类后的数据"""
    print("正在加载聚类数据...")
    clustered_data = pd.read_csv('hfsnef_specific_proteins_clustered.csv')
    cluster_summary = pd.read_csv('cluster_summary.csv')
    
    print(f"加载了 {len(clustered_data)} 个HFsnEF特异蛋白")
    print(f"分为 {len(cluster_summary)} 个簇")
    
    return clustered_data, cluster_summary

def create_protein_functional_categories():
    """创建蛋白质功能分类字典（基于常见蛋白功能）"""
    
    functional_categories = {
        # 炎症和免疫
        'Inflammation_Immune': [
            'SAA1', 'SAA2', 'CRP', 'IL6ST', 'VCAM1', 'ICAM1', 'CCL18', 'CCL19', 
            'CXCL12', 'TNF', 'TNFAIP6', 'HMOX1', 'C2', 'C9', 'CFB', 'CFI',
            'IGHA1', 'IGHA2', 'IGHG2', 'IGHG3', 'IGHV', 'IGKV', 'IGLV'
        ],
        
        # 心血管和循环
        'Cardiovascular': [
            'TNC', 'POSTN', 'VCAN', 'COL18A1', 'COL6A3', 'LAMB1', 'LAMB2',
            'VWF', 'ANGPTL2', 'ANGPTL3', 'APOA2', 'APOC2', 'APOF', 'LPL',
            'MB', 'MYL2', 'MYH7', 'MYBPC2', 'TTN', 'TNNI1'
        ],
        
        # 代谢
        'Metabolism': [
            'ALDOA', 'GAPDHS', 'PFKL', 'PFKM', 'G6PD', 'FASN', 'ACLY',
            'DHCR7', 'MGAT1', 'MGAT5', 'MAN1A1', 'MAN2A1', 'FUCA1',
            'GC', 'HP', 'ALB', 'ADIPOQ', 'FABP1'
        ],
        
        # 蛋白质合成和折叠
        'Protein_Synthesis': [
            'EIF2S1', 'EIF4A1', 'EIF4E', 'EIF4G3', 'EEF1A2', 'EEF1E1', 'EEF2',
            'HSPA13', 'HSPH1', 'HSPB6', 'HSPE1', 'CRYAB'
        ],
        
        # 细胞骨架和运动
        'Cytoskeleton': [
            'ACTB', 'MYO18A', 'VCL', 'JUP', 'CDH2', 'CTNNA1', 'FERMT1', 'FERMT2'
        ],
        
        # 信号转导
        'Signal_Transduction': [
            'ROCK1', 'STK4', 'STK38', 'STK39', 'PAK4', 'PAK6', 'PRKACA',
            'PPP1R21', 'PPP2CB', 'PPP2R1A', 'PPP2R5A', 'STAT1', 'STAT3'
        ],
        
        # 血管生成和重塑
        'Angiogenesis': [
            'VEGFD', 'ANGPTL3', 'POSTN', 'THBS4', 'FBLN2', 'FBLN5', 'ELN',
            'MMP2', 'MMP7', 'MMP14', 'ADAM15', 'ADAMDEC1'
        ],
        
        # 凝血和纤溶
        'Coagulation': [
            'FGA', 'FGB', 'FGG', 'VWF', 'PLAT', 'D_dimer', 'SERPINA6'
        ]
    }
    
    return functional_categories

def annotate_proteins_with_functions(clustered_data):
    """为蛋白质添加功能注释"""
    print("正在进行功能注释...")
    
    functional_categories = create_protein_functional_categories()
    
    # 创建蛋白质到功能的映射
    protein_to_function = {}
    for function, proteins in functional_categories.items():
        for protein in proteins:
            if protein not in protein_to_function:
                protein_to_function[protein] = []
            protein_to_function[protein].append(function)
    
    # 为每个蛋白质添加功能注释
    clustered_data_annotated = clustered_data.copy()
    clustered_data_annotated['functions'] = clustered_data_annotated['protein'].apply(
        lambda x: protein_to_function.get(x, ['Unknown'])
    )
    clustered_data_annotated['primary_function'] = clustered_data_annotated['functions'].apply(
        lambda x: x[0] if x else 'Unknown'
    )
    
    return clustered_data_annotated, functional_categories

def perform_functional_enrichment_analysis(clustered_data_annotated):
    """进行功能富集分析"""
    print("正在进行功能富集分析...")
    
    enrichment_results = []
    
    for cluster_name in clustered_data_annotated['cluster_name'].unique():
        cluster_proteins = clustered_data_annotated[
            clustered_data_annotated['cluster_name'] == cluster_name
        ]
        
        # 统计每个功能类别的蛋白数量
        function_counts = Counter()
        total_proteins = len(cluster_proteins)
        
        for functions in cluster_proteins['functions']:
            for func in functions:
                function_counts[func] += 1
        
        # 计算富集度
        for function, count in function_counts.items():
            if function != 'Unknown':
                enrichment_score = count / total_proteins
                enrichment_results.append({
                    'cluster': cluster_name,
                    'function': function,
                    'protein_count': count,
                    'total_proteins': total_proteins,
                    'enrichment_score': enrichment_score,
                    'percentage': enrichment_score * 100
                })
    
    enrichment_df = pd.DataFrame(enrichment_results)
    return enrichment_df

def create_protein_interaction_network(clustered_data_annotated):
    """创建蛋白质相互作用网络"""
    print("正在创建蛋白质相互作用网络...")
    
    # 创建网络图
    G = nx.Graph()
    
    # 添加节点（蛋白质）
    for _, protein_data in clustered_data_annotated.iterrows():
        G.add_node(
            protein_data['protein'],
            cluster=protein_data['cluster_name'],
            hazard_ratio=protein_data['hazard_ratio'],
            p_value=protein_data['p_value'],
            primary_function=protein_data['primary_function']
        )
    
    # 基于功能相似性添加边
    proteins = clustered_data_annotated['protein'].tolist()
    functions = clustered_data_annotated['functions'].tolist()
    
    for i, protein1 in enumerate(proteins):
        for j, protein2 in enumerate(proteins[i+1:], i+1):
            # 计算功能相似性
            func1_set = set(functions[i])
            func2_set = set(functions[j])
            
            if func1_set & func2_set:  # 有共同功能
                similarity = len(func1_set & func2_set) / len(func1_set | func2_set)
                if similarity > 0.3:  # 相似性阈值
                    G.add_edge(protein1, protein2, weight=similarity)
    
    return G

def create_comprehensive_visualizations(clustered_data_annotated, enrichment_df, G):
    """创建综合可视化图表"""
    print("正在创建综合可视化...")
    
    fig = plt.figure(figsize=(24, 18))
    
    # 1. 功能富集热图
    ax1 = plt.subplot(3, 4, 1)
    
    # 准备热图数据
    pivot_data = enrichment_df.pivot(index='function', columns='cluster', values='percentage')
    pivot_data = pivot_data.fillna(0)
    
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='YlOrRd', ax=ax1)
    ax1.set_title('功能富集热图 (%)')
    ax1.set_xlabel('蛋白质簇')
    ax1.set_ylabel('功能类别')
    
    # 2. 各簇功能分布堆叠条形图
    ax2 = plt.subplot(3, 4, 2)
    
    cluster_function_data = []
    for cluster in clustered_data_annotated['cluster_name'].unique():
        cluster_proteins = clustered_data_annotated[
            clustered_data_annotated['cluster_name'] == cluster
        ]
        function_counts = Counter()
        for functions in cluster_proteins['functions']:
            for func in functions:
                if func != 'Unknown':
                    function_counts[func] += 1
        
        for func, count in function_counts.items():
            cluster_function_data.append({
                'cluster': cluster,
                'function': func,
                'count': count
            })
    
    cluster_func_df = pd.DataFrame(cluster_function_data)
    pivot_counts = cluster_func_df.pivot(index='cluster', columns='function', values='count')
    pivot_counts = pivot_counts.fillna(0)
    
    pivot_counts.plot(kind='bar', stacked=True, ax=ax2, colormap='Set3')
    ax2.set_title('各簇功能分布')
    ax2.set_xlabel('蛋白质簇')
    ax2.set_ylabel('蛋白数量')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.tick_params(axis='x', rotation=45)
    
    # 3. 风险比vs功能散点图
    ax3 = plt.subplot(3, 4, 3)
    
    function_colors = plt.cm.Set1(np.linspace(0, 1, len(enrichment_df['function'].unique())))
    function_color_map = dict(zip(enrichment_df['function'].unique(), function_colors))
    
    for function in clustered_data_annotated['primary_function'].unique():
        if function != 'Unknown':
            func_data = clustered_data_annotated[
                clustered_data_annotated['primary_function'] == function
            ]
            ax3.scatter(func_data['hazard_ratio'], -np.log10(func_data['p_value']),
                       label=function, alpha=0.7, s=50,
                       color=function_color_map.get(function, 'gray'))
    
    ax3.axvline(x=1, color='red', linestyle='--', alpha=0.5)
    ax3.axhline(y=-np.log10(0.05), color='red', linestyle='--', alpha=0.5)
    ax3.set_xlabel('风险比')
    ax3.set_ylabel('-Log10(p值)')
    ax3.set_title('功能-风险关联图')
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    # 4. 网络图（主要Hub蛋白）
    ax4 = plt.subplot(3, 4, 4)
    
    # 选择连接度最高的蛋白质
    degrees = dict(G.degree())
    top_proteins = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:20]
    
    if top_proteins:
        subgraph = G.subgraph([protein for protein, _ in top_proteins])
        
        pos = nx.spring_layout(subgraph, k=1, iterations=50)
        
        # 节点颜色基于簇
        cluster_colors = {'高风险簇_2': 'red', '高风险簇_3': 'orange', 
                         '轻度保护簇_1': 'lightblue', '保护性簇_5': 'blue',
                         '中等风险簇_4': 'yellow'}
        
        node_colors = [cluster_colors.get(G.nodes[node].get('cluster', ''), 'gray') 
                      for node in subgraph.nodes()]
        
        nx.draw(subgraph, pos, ax=ax4, with_labels=True, node_color=node_colors,
                node_size=300, font_size=6, font_weight='bold')
        ax4.set_title('蛋白质相互作用网络\n(Top 20 Hub蛋白)')
    
    # 5. 簇间功能相似性网络
    ax5 = plt.subplot(3, 4, 5)
    
    # 计算簇间功能相似性
    cluster_similarity = {}
    clusters = clustered_data_annotated['cluster_name'].unique()
    
    for i, cluster1 in enumerate(clusters):
        for j, cluster2 in enumerate(clusters[i:], i):
            if cluster1 != cluster2:
                # 获取两个簇的功能分布
                func1 = clustered_data_annotated[
                    clustered_data_annotated['cluster_name'] == cluster1
                ]['primary_function'].value_counts()
                func2 = clustered_data_annotated[
                    clustered_data_annotated['cluster_name'] == cluster2
                ]['primary_function'].value_counts()
                
                # 计算Jaccard相似性
                all_functions = set(func1.index) | set(func2.index)
                intersection = sum(min(func1.get(f, 0), func2.get(f, 0)) for f in all_functions)
                union = sum(max(func1.get(f, 0), func2.get(f, 0)) for f in all_functions)
                
                if union > 0:
                    similarity = intersection / union
                    cluster_similarity[(cluster1, cluster2)] = similarity
    
    # 创建簇网络
    cluster_G = nx.Graph()
    for cluster in clusters:
        cluster_G.add_node(cluster)
    
    for (c1, c2), sim in cluster_similarity.items():
        if sim > 0.1:  # 相似性阈值
            cluster_G.add_edge(c1, c2, weight=sim)
    
    if cluster_G.edges():
        pos_cluster = nx.spring_layout(cluster_G)
        edge_weights = [cluster_G[u][v]['weight'] * 5 for u, v in cluster_G.edges()]
        
        nx.draw(cluster_G, pos_cluster, ax=ax5, with_labels=True,
                node_color='lightcoral', node_size=1000, font_size=8,
                width=edge_weights, font_weight='bold')
        ax5.set_title('簇间功能相似性网络')
    
    # 6. 关键通路蛋白分布
    ax6 = plt.subplot(3, 4, 6)
    
    # 定义关键通路
    key_pathways = {
        '炎症反应': ['SAA1', 'SAA2', 'CRP', 'HMOX1', 'VCAM1', 'ICAM1'],
        '心肌重塑': ['TNC', 'POSTN', 'VCAN', 'COL18A1', 'MMP2', 'MMP14'],
        '代谢异常': ['ALDOA', 'PFKL', 'FASN', 'ADIPOQ', 'HP', 'ALB'],
        '凝血异常': ['FGA', 'FGB', 'FGG', 'VWF', 'PLAT', 'D_dimer']
    }
    
    pathway_data = []
    for pathway, pathway_proteins in key_pathways.items():
        for cluster in clusters:
            cluster_proteins = clustered_data_annotated[
                clustered_data_annotated['cluster_name'] == cluster
            ]['protein'].tolist()
            
            overlap = len(set(pathway_proteins) & set(cluster_proteins))
            if overlap > 0:
                pathway_data.append({
                    'pathway': pathway,
                    'cluster': cluster,
                    'count': overlap
                })
    
    if pathway_data:
        pathway_df = pd.DataFrame(pathway_data)
        pathway_pivot = pathway_df.pivot(index='pathway', columns='cluster', values='count')
        pathway_pivot = pathway_pivot.fillna(0)
        
        sns.heatmap(pathway_pivot, annot=True, fmt='.0f', cmap='Blues', ax=ax6)
        ax6.set_title('关键通路蛋白分布')
        ax6.set_xlabel('蛋白质簇')
        ax6.set_ylabel('通路')
    
    # 7-12. 各簇详细分析（选择前6个簇）
    for idx, cluster_name in enumerate(clusters[:6]):
        ax = plt.subplot(3, 4, 7 + idx)
        
        cluster_data = clustered_data_annotated[
            clustered_data_annotated['cluster_name'] == cluster_name
        ]
        
        # 创建该簇的风险比分布图
        ax.hist(cluster_data['hazard_ratio'], bins=15, alpha=0.7, 
                color=plt.cm.Set3(idx), edgecolor='black')
        ax.axvline(x=1, color='red', linestyle='--', alpha=0.7)
        ax.set_xlabel('风险比')
        ax.set_ylabel('蛋白数量')
        ax.set_title(f'{cluster_name}\n({len(cluster_data)} 个蛋白)')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('comprehensive_functional_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def generate_detailed_functional_report(clustered_data_annotated, enrichment_df, G):
    """生成详细的功能分析报告"""
    print("正在生成详细功能分析报告...")
    
    report = """
# HFsnEF特异蛋白功能富集和网络分析报告

## 1. 功能富集分析结果

### 1.1 各簇主要功能特征
"""
    
    for cluster_name in clustered_data_annotated['cluster_name'].unique():
        cluster_enrichment = enrichment_df[enrichment_df['cluster'] == cluster_name]
        cluster_enrichment = cluster_enrichment.sort_values('enrichment_score', ascending=False)
        
        report += f"""
#### {cluster_name}
"""
        
        if not cluster_enrichment.empty:
            top_functions = cluster_enrichment.head(3)
            for _, func_data in top_functions.iterrows():
                report += f"- **{func_data['function']}**: {func_data['protein_count']} 个蛋白 ({func_data['percentage']:.1f}%)\n"
        
        # 添加代表性蛋白
        cluster_proteins = clustered_data_annotated[
            clustered_data_annotated['cluster_name'] == cluster_name
        ].nsmallest(5, 'p_value')
        
        report += f"\n**代表性蛋白**:\n"
        for _, protein in cluster_proteins.iterrows():
            report += f"- {protein['protein']} (HR={protein['hazard_ratio']:.2f}, 功能={protein['primary_function']})\n"
    
    # 网络分析结果
    degrees = dict(G.degree())
    top_hubs = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:10]
    
    report += f"""
## 2. 网络分析结果

### 2.1 关键Hub蛋白
网络中连接度最高的10个蛋白质：
"""
    
    for protein, degree in top_hubs:
        protein_data = clustered_data_annotated[
            clustered_data_annotated['protein'] == protein
        ].iloc[0]
        report += f"- **{protein}**: 连接度={degree}, 簇={protein_data['cluster_name']}, HR={protein_data['hazard_ratio']:.2f}\n"
    
    report += f"""
### 2.2 网络统计
- 总节点数: {G.number_of_nodes()}
- 总边数: {G.number_of_edges()}
- 网络密度: {nx.density(G):.4f}
- 平均聚类系数: {nx.average_clustering(G):.4f}
"""
    
    # 保存报告
    with open('functional_enrichment_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    return report

def main():
    """主函数"""
    print("=== HFsnEF特异蛋白功能富集和网络分析 ===")
    
    # 1. 加载数据
    clustered_data, cluster_summary = load_clustered_data()
    
    # 2. 功能注释
    clustered_data_annotated, functional_categories = annotate_proteins_with_functions(clustered_data)
    
    # 3. 功能富集分析
    enrichment_df = perform_functional_enrichment_analysis(clustered_data_annotated)
    
    # 4. 创建蛋白质相互作用网络
    G = create_protein_interaction_network(clustered_data_annotated)
    
    # 5. 创建可视化
    fig = create_comprehensive_visualizations(clustered_data_annotated, enrichment_df, G)
    
    # 6. 生成报告
    report = generate_detailed_functional_report(clustered_data_annotated, enrichment_df, G)
    
    # 7. 保存结果
    clustered_data_annotated.to_csv('hfsnef_proteins_functional_annotated.csv', 
                                   index=False, encoding='utf-8')
    enrichment_df.to_csv('functional_enrichment_results.csv', 
                        index=False, encoding='utf-8')
    
    print(f"\n=== 功能分析完成 ===")
    print(f"功能注释了 {len(clustered_data_annotated)} 个蛋白")
    print(f"识别了 {len(enrichment_df)} 个功能富集项")
    print(f"构建了包含 {G.number_of_nodes()} 个节点的蛋白质网络")
    print(f"结果已保存到:")
    print(f"- hfsnef_proteins_functional_annotated.csv")
    print(f"- functional_enrichment_results.csv")
    print(f"- functional_enrichment_report.md")
    print(f"- comprehensive_functional_analysis.png")
    
    return clustered_data_annotated, enrichment_df, G

if __name__ == "__main__":
    clustered_data_annotated, enrichment_df, G = main()
