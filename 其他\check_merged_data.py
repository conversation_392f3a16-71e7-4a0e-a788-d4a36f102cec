#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查合并后数据的基本信息
"""

import pandas as pd
import numpy as np

def check_merged_data():
    """
    检查合并后数据的基本信息
    """
    # 读取合并后的数据
    print("正在读取合并后的数据...")
    df = pd.read_csv("merged_protein_clinical_data.csv")
    
    print(f"数据形状: {df.shape}")
    print(f"行数: {df.shape[0]} (样本数)")
    print(f"列数: {df.shape[1]} (变量数)")
    
    # 检查Record_ID列
    print(f"\nRecord_ID信息:")
    print(f"Record_ID数量: {len(df['Record_ID'].unique())}")
    print(f"Record_ID示例: {list(df['Record_ID'].unique())[:10]}")
    
    # 检查列名结构
    print(f"\n列名结构:")
    columns = list(df.columns)
    print(f"前10个列名: {columns[:10]}")
    print(f"后10个列名: {columns[-10:]}")
    
    # 检查临床变量数量（假设蛋白列在临床变量之后）
    # 找到第一个蛋白列的位置
    protein_start_idx = None
    for i, col in enumerate(columns):
        if col not in ['Record_ID', 'HFsnEF', 'Plasma_sapmle', 'Sex', 'Age', 'Birthday', 
                      'Nationnality', 'IN_type', 'IN_DATE', 'OUT_DATE', 'IN_DAYS', 'Dyspnea', 
                      'Edema', 'Lung_rales', 'Fatigue', 'SBP ', 'DBP ', 'PR', 'Height', 'Weight', 
                      'BMI', 'BSA', 'HF_type_onset ', 'NYHA_class', 'CHD', 'PCI ', 'CABG', 
                      'Hypertension', 'Atrial_fibrillation', 'Atrial_flutter', 'HF_history', 
                      'HF_duration_month', 'Pacemaker', 'Heart_valve_surgery', 'Stroke', 'Diabetes', 
                      'Hyperlipedemia', 'Anemia', 'CKD', 'COPD', 'Asthma', 'Interstitial_pulmonary_fibrosis', 
                      'Malignancy', 'Thyroid_disease', 'Smoke', ' Alcoholic_drinking', 'Blood_type']:
            # 这里简化处理，假设从某个位置开始是蛋白列
            if i > 50:  # 假设前50列大部分是临床变量
                protein_start_idx = i
                break
    
    if protein_start_idx:
        clinical_vars = protein_start_idx - 1  # 减去Record_ID
        protein_vars = len(columns) - protein_start_idx
        print(f"\n变量分布:")
        print(f"临床变量数量: {clinical_vars}")
        print(f"蛋白变量数量: {protein_vars}")
    
    # 检查缺失值情况
    print(f"\n缺失值统计:")
    missing_counts = df.isnull().sum()
    total_missing = missing_counts.sum()
    cols_with_missing = (missing_counts > 0).sum()
    
    print(f"总缺失值数量: {total_missing}")
    print(f"有缺失值的列数: {cols_with_missing}")
    print(f"缺失值比例: {total_missing / (df.shape[0] * df.shape[1]) * 100:.2f}%")
    
    # 检查每行的缺失值情况
    row_missing = df.isnull().sum(axis=1)
    print(f"\n每行缺失值统计:")
    print(f"平均每行缺失值: {row_missing.mean():.2f}")
    print(f"最大每行缺失值: {row_missing.max()}")
    print(f"最小每行缺失值: {row_missing.min()}")
    
    # 检查完全匹配的样本数量
    complete_rows = df.dropna().shape[0]
    print(f"\n完全无缺失值的样本数: {complete_rows}")
    
    # 显示前几行数据概览
    print(f"\n数据概览 (前5行, 前10列):")
    print(df.iloc[:5, :10])
    
    # 检查蛋白数据的分布
    if protein_start_idx:
        protein_cols = columns[protein_start_idx:]
        protein_data = df[protein_cols]
        
        print(f"\n蛋白数据统计:")
        print(f"蛋白数量: {len(protein_cols)}")
        print(f"蛋白数据缺失值: {protein_data.isnull().sum().sum()}")
        print(f"蛋白数据非零值比例: {(protein_data > 0).sum().sum() / (protein_data.shape[0] * protein_data.shape[1]) * 100:.2f}%")
        
        # 显示一些蛋白的统计信息
        print(f"\n前5个蛋白的统计信息:")
        for col in protein_cols[:5]:
            values = protein_data[col].dropna()
            if len(values) > 0:
                print(f"{col}: 均值={values.mean():.4f}, 标准差={values.std():.4f}, 范围=[{values.min():.4f}, {values.max():.4f}]")

if __name__ == "__main__":
    check_merged_data()
