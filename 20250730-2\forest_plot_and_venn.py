#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
森林图和韦恩图分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_forest_plot(data, title, filename, top_n=10):
    """创建森林图"""
    
    print(f"正在创建{title}...")
    
    # 选择前N个最显著的结果
    plot_data = data.head(top_n).copy()
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, max(8, len(plot_data) * 0.8)))
    
    # 设置y轴位置
    y_positions = range(len(plot_data))
    
    # 绘制森林图
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i  # 倒序排列，最显著的在顶部
        
        # 确定颜色
        if row['hazard_ratio'] < 1:
            color = '#1E88E5'  # 蓝色 - 保护性
            marker = 's'  # 方形
        else:
            color = '#D32F2F'  # 红色 - 危险性
            marker = 'o'  # 圆形
        
        # 绘制HR点
        ax.scatter(row['hazard_ratio'], y, s=120, color=color, alpha=0.8, 
                  zorder=3, marker=marker, edgecolors='white', linewidth=1)
        
        # 绘制置信区间
        ax.plot([row['ci_lower'], row['ci_upper']], [y, y], 
                color=color, linewidth=3, alpha=0.7, zorder=2)
        
        # 绘制置信区间端点
        ax.plot([row['ci_lower'], row['ci_lower']], [y-0.1, y+0.1], 
                color=color, linewidth=3, alpha=0.7, zorder=2)
        ax.plot([row['ci_upper'], row['ci_upper']], [y-0.1, y+0.1], 
                color=color, linewidth=3, alpha=0.7, zorder=2)
    
    # 添加HR=1的参考线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.7, linewidth=2, zorder=1)
    
    # 设置y轴标签 - 代谢物名称
    metabolite_names = []
    for _, row in plot_data.iterrows():
        name = row['metabolite'].split('_')[0]
        # 截断过长的名称
        if len(name) > 40:
            name = name[:37] + '...'
        metabolite_names.append(name)
    
    ax.set_yticks(range(len(plot_data)))
    ax.set_yticklabels(reversed(metabolite_names), fontsize=10)
    
    # 设置x轴 - 使用对数刻度
    ax.set_xscale('log')
    
    # 设置x轴范围
    all_values = list(plot_data['hazard_ratio']) + list(plot_data['ci_lower']) + list(plot_data['ci_upper'])
    x_min = max(0.01, min(all_values) * 0.5)
    x_max = max(all_values) * 2
    ax.set_xlim(x_min, x_max)
    
    # 设置x轴标签
    ax.set_xlabel('Hazard Ratio (95% CI)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    # 在右侧添加HR值和P值
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i
        
        # HR和置信区间文本
        hr_text = f"{row['hazard_ratio']:.3f}"
        ci_text = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
        p_text = f"P={row['p_value']:.4f}"
        
        # 在图的右侧添加文本
        ax.text(x_max * 0.95, y, f"{hr_text} {ci_text}", 
                ha='right', va='center', fontsize=9, fontweight='bold')
        ax.text(x_max * 0.95, y-0.2, p_text, 
                ha='right', va='center', fontsize=8, style='italic', color='gray')
    
    # 添加保护性/危险性标注
    ax.text(x_min * 1.1, len(plot_data) + 0.5, 'Protective', 
            fontsize=11, color='#1E88E5', fontweight='bold')
    ax.text(x_max * 0.9, len(plot_data) + 0.5, 'Risk', 
            fontsize=11, color='#D32F2F', fontweight='bold')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_linewidth(1.5)
    ax.spines['left'].set_linewidth(1.5)
    
    # 调整布局
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def create_venn_diagram():
    """创建韦恩图显示两组的差异代谢物"""

    print("正在创建韦恩图...")

    # 读取数据
    hfnef_results = pd.read_csv('hfnef_cox_results.csv')
    hfsnef_results = pd.read_csv('hfsnef_cox_results.csv')

    # 获取显著代谢物
    hfnef_significant = set(hfnef_results[hfnef_results['p_value'] < 0.05]['metabolite'])
    hfsnef_significant = set(hfsnef_results[hfsnef_results['p_value'] < 0.05]['metabolite'])

    print(f"HFnEF组显著代谢物: {len(hfnef_significant)}")
    print(f"HFsnEF组显著代谢物: {len(hfsnef_significant)}")
    print(f"共同显著代谢物: {len(hfnef_significant & hfsnef_significant)}")

    # 计算各部分数量
    hfnef_only = len(hfnef_significant - hfsnef_significant)
    hfsnef_only = len(hfsnef_significant - hfnef_significant)
    common = len(hfnef_significant & hfsnef_significant)

    # 创建自定义韦恩图
    fig, ax = plt.subplots(figsize=(10, 8))

    # 绘制两个圆
    circle1 = patches.Circle((0.35, 0.5), 0.3, facecolor='#E3F2FD', alpha=0.7, edgecolor='#1976D2', linewidth=2)
    circle2 = patches.Circle((0.65, 0.5), 0.3, facecolor='#FFEBEE', alpha=0.7, edgecolor='#D32F2F', linewidth=2)

    ax.add_patch(circle1)
    ax.add_patch(circle2)

    # 添加标签
    ax.text(0.2, 0.5, str(hfnef_only), fontsize=16, fontweight='bold', ha='center', va='center')
    ax.text(0.5, 0.5, str(common), fontsize=16, fontweight='bold', ha='center', va='center')
    ax.text(0.8, 0.5, str(hfsnef_only), fontsize=16, fontweight='bold', ha='center', va='center')

    # 添加组名
    ax.text(0.35, 0.85, 'HFnEF Group', fontsize=14, fontweight='bold', ha='center', color='#1976D2')
    ax.text(0.65, 0.85, 'HFsnEF Group', fontsize=14, fontweight='bold', ha='center', color='#D32F2F')

    # 添加总数标签
    ax.text(0.35, 0.15, f'Total: {len(hfnef_significant)}', fontsize=12, ha='center', color='#1976D2')
    ax.text(0.65, 0.15, f'Total: {len(hfsnef_significant)}', fontsize=12, ha='center', color='#D32F2F')

    # 设置坐标轴
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_aspect('equal')
    ax.axis('off')

    ax.set_title('Significant Metabolites in HFnEF vs HFsnEF Groups',
                fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('venn_diagram_significant_metabolites.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("韦恩图已保存到: venn_diagram_significant_metabolites.png")

    # 返回HFsnEF组特异的代谢物
    hfsnef_specific = hfsnef_significant - hfnef_significant
    print(f"HFsnEF组特异显著代谢物: {len(hfsnef_specific)}")

    return hfsnef_specific, hfsnef_results

def get_hfsnef_specific_top5(hfsnef_specific, hfsnef_results):
    """获取HFsnEF组特异的前5个显著代谢物"""
    
    # 筛选HFsnEF组特异的代谢物结果
    hfsnef_specific_results = hfsnef_results[
        hfsnef_results['metabolite'].isin(hfsnef_specific) & 
        (hfsnef_results['p_value'] < 0.05)
    ].copy()
    
    # 按p值排序，取前5个
    hfsnef_top5 = hfsnef_specific_results.sort_values('p_value').head(5)
    
    print(f"\nHFsnEF组特异的前5个显著代谢物:")
    for i, row in hfsnef_top5.iterrows():
        metabolite_name = row['metabolite'].split('_')[0][:50]
        direction = "保护性" if row['hazard_ratio'] < 1 else "危险性"
        print(f"{metabolite_name} - HR={row['hazard_ratio']:.3f} [{row['ci_lower']:.3f}-{row['ci_upper']:.3f}] P={row['p_value']:.4f} ({direction})")
    
    return hfsnef_top5

def create_summary_table():
    """创建结果总结表"""
    
    print("\n正在创建结果总结表...")
    
    # 读取所有数据
    overall_results = pd.read_csv('all_metabolites_cox_results.csv')
    hfnef_results = pd.read_csv('hfnef_cox_results.csv')
    hfsnef_results = pd.read_csv('hfsnef_cox_results.csv')
    
    # 统计信息
    summary_data = {
        'Group': ['Overall', 'HFnEF', 'HFsnEF'],
        'Total_Metabolites': [len(overall_results), len(hfnef_results), len(hfsnef_results)],
        'Significant_P005': [
            len(overall_results[overall_results['p_value'] < 0.05]),
            len(hfnef_results[hfnef_results['p_value'] < 0.05]),
            len(hfsnef_results[hfsnef_results['p_value'] < 0.05])
        ],
        'Protective_HR_lt_1': [
            len(overall_results[(overall_results['p_value'] < 0.05) & (overall_results['hazard_ratio'] < 1)]),
            len(hfnef_results[(hfnef_results['p_value'] < 0.05) & (hfnef_results['hazard_ratio'] < 1)]),
            len(hfsnef_results[(hfsnef_results['p_value'] < 0.05) & (hfsnef_results['hazard_ratio'] < 1)])
        ],
        'Risk_HR_gt_1': [
            len(overall_results[(overall_results['p_value'] < 0.05) & (overall_results['hazard_ratio'] > 1)]),
            len(hfnef_results[(hfnef_results['p_value'] < 0.05) & (hfnef_results['hazard_ratio'] > 1)]),
            len(hfsnef_results[(hfsnef_results['p_value'] < 0.05) & (hfsnef_results['hazard_ratio'] > 1)])
        ]
    }
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('cox_analysis_summary_table.csv', index=False)
    
    print("结果总结表已保存到: cox_analysis_summary_table.csv")
    print("\n总结统计:")
    print(summary_df.to_string(index=False))

def main():
    """主函数"""
    
    print("=" * 80)
    print("森林图和韦恩图分析")
    print("=" * 80)
    
    # 1. 读取整体人群数据，创建森林图
    print("\n1. 整体人群最显著的10个代谢物森林图")
    overall_results = pd.read_csv('all_metabolites_cox_results.csv')
    overall_significant = overall_results[overall_results['p_value'] < 0.05].sort_values('p_value')
    
    if len(overall_significant) >= 10:
        create_forest_plot(overall_significant, 
                          'Top 10 Most Significant Metabolites - Overall Population',
                          'overall_top10_forest_plot.png', 10)
    else:
        print(f"整体人群只有{len(overall_significant)}个显著代谢物，将全部展示")
        create_forest_plot(overall_significant, 
                          f'Top {len(overall_significant)} Significant Metabolites - Overall Population',
                          'overall_significant_forest_plot.png', len(overall_significant))
    
    # 2. 创建韦恩图
    print(f"\n2. HFnEF和HFsnEF组差异代谢物韦恩图")
    hfsnef_specific, hfsnef_results = create_venn_diagram()
    
    # 3. HFsnEF组特异的前5个代谢物森林图
    print(f"\n3. HFsnEF组特异的前5个显著代谢物森林图")
    if len(hfsnef_specific) >= 5:
        hfsnef_top5 = get_hfsnef_specific_top5(hfsnef_specific, hfsnef_results)
        create_forest_plot(hfsnef_top5, 
                          'Top 5 HFsnEF-Specific Significant Metabolites',
                          'hfsnef_specific_top5_forest_plot.png', 5)
    else:
        print(f"HFsnEF组特异代谢物只有{len(hfsnef_specific)}个")
        if len(hfsnef_specific) > 0:
            hfsnef_specific_all = get_hfsnef_specific_top5(hfsnef_specific, hfsnef_results)
            create_forest_plot(hfsnef_specific_all, 
                              f'All {len(hfsnef_specific)} HFsnEF-Specific Significant Metabolites',
                              'hfsnef_specific_all_forest_plot.png', len(hfsnef_specific))
    
    # 4. 创建总结表
    create_summary_table()
    
    print(f"\n=" * 80)
    print("分析完成！生成的文件:")
    print("• overall_top10_forest_plot.png - 整体人群前10个代谢物森林图")
    print("• venn_diagram_significant_metabolites.png - 韦恩图")
    print("• hfsnef_specific_top5_forest_plot.png - HFsnEF组特异前5个代谢物森林图")
    print("• cox_analysis_summary_table.csv - 结果总结表")
    print("=" * 80)

if __name__ == "__main__":
    main()
