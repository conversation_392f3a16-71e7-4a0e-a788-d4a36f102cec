import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import AgglomerativeClustering
from scipy.stats import pearsonr
from scipy.cluster.hierarchy import dendrogram, linkage
import networkx as nx
from statsmodels.stats.multitest import multipletests
import os
from matplotlib.colors import LinearSegmentedColormap

# 1. 加载数据
hfsnef_results = pd.read_csv('hfsnef_protein_cox_results.csv')
hfnef_results = pd.read_csv('hfnef_protein_cox_results.csv')

# 2. 识别HFsnEF中显著的蛋白质 (p < 0.05)
hfsnef_significant = hfsnef_results[hfsnef_results.iloc[:, 4] < 0.05]

# 3. 找出HFnEF中的显著蛋白
hfnef_significant = hfnef_results[hfnef_results.iloc[:, 4] < 0.05]
hfnef_significant_proteins = set(hfnef_significant.iloc[:, 0])

# 4. 找出HFsnEF特异的显著蛋白 (不在HFnEF显著集合中)
hfsnef_specific = hfsnef_significant[~hfsnef_significant.iloc[:, 0].isin(hfnef_significant_proteins)]

print(f"HFsnEF特异性显著蛋白数量: {len(hfsnef_specific)}")
print("前10个蛋白质:")
print(hfsnef_specific.iloc[:10, [0, 1, 4]].to_string(index=False))

# 5. 保存HFsnEF特异蛋白结果
hfsnef_specific.to_csv('hfsnef_specific_proteins.csv', index=False)

# 6. 获取原始蛋白表达数据
# 注意：这里假设我们能访问原始数据，如果没有，需要使用其他方法
try:
    # 尝试读取蛋白质表达数据
    protein_data = pd.read_csv('merged_protein_clinical_data.csv')
    print("成功加载蛋白质表达数据")
    has_expression_data = True
    
    # 只保留HFsnEF特异蛋白
    specific_proteins = list(hfsnef_specific.iloc[:, 0])
    
    # 假设第一列是样本ID，之后的列是蛋白表达
    # 需要根据实际数据格式调整
    protein_cols = [col for col in protein_data.columns if col in specific_proteins]
    expression_data = protein_data[protein_cols]
    
except FileNotFoundError:
    print("未找到蛋白质表达数据，将使用Cox分析结果进行聚类")
    has_expression_data = False
    # 使用Cox系数作为特征
    expression_data = hfsnef_specific.set_index(hfsnef_specific.iloc[:, 0])

# 7. 聚类分析（简化的WGCNA替代方案）
if len(hfsnef_specific) >= 3:  # 确保有足够的蛋白进行聚类
    # 计算蛋白间相关性矩阵
    if has_expression_data:
        corr_matrix = expression_data.corr(method='pearson')
    else:
        # 如果没有表达数据，使用Cox结果构建简单相似度矩阵
        cox_features = hfsnef_specific.iloc[:, [1, 4]]  # HR和p值
        # 标准化HR (取对数)
        log_hr = np.log(cox_features.iloc[:, 0])
        # 转换p值 (-log10(p))
        neg_log_p = -np.log10(cox_features.iloc[:, 1])
        
        combined_features = pd.DataFrame({
            'log_hr': log_hr.values,
            'neg_log_p': neg_log_p.values
        }, index=hfsnef_specific.iloc[:, 0])
        
        # 使用这些特征计算欧氏距离，然后转换为相似度
        from sklearn.metrics.pairwise import euclidean_distances
        dist_matrix = euclidean_distances(combined_features)
        similarity = 1 / (1 + dist_matrix)
        corr_matrix = pd.DataFrame(
            similarity, 
            index=hfsnef_specific.iloc[:, 0], 
            columns=hfsnef_specific.iloc[:, 0]
        )
    
    # 分层聚类
    Z = linkage(corr_matrix, method='ward')
    
    # 确定最佳聚类数
    from scipy.cluster.hierarchy import fcluster
    max_clusters = min(8, len(hfsnef_specific))  # 最多8个聚类
    
    # 可视化聚类结果
    plt.figure(figsize=(12, 8))
    dendrogram(Z)
    plt.title('HFsnEF特异蛋白分层聚类')
    plt.xlabel('蛋白质')
    plt.ylabel('距离')
    plt.tight_layout()
    plt.savefig('hfsnef_specific_protein_clustering.png', dpi=300)
    
    # 选择聚类数量 (这里选择4个聚类作为示例，可根据上面的图形调整)
    n_clusters = min(4, len(hfsnef_specific))
    cluster_labels = fcluster(Z, n_clusters, criterion='maxclust')
    
    # 将聚类结果添加到数据中
    hfsnef_specific['Cluster'] = cluster_labels
    
    # 8. 识别每个簇中的Hub蛋白
    hub_proteins = {}
    cluster_info = {}
    
    for cluster in range(1, n_clusters + 1):
        cluster_proteins = hfsnef_specific[hfsnef_specific['Cluster'] == cluster]
        
        if len(cluster_proteins) > 1:
            # 计算每个蛋白在簇内的连接度
            cluster_protein_ids = list(cluster_proteins.iloc[:, 0])
            connectivity = {}
            
            for protein in cluster_protein_ids:
                # 计算与其他蛋白的连接强度总和
                connectivity[protein] = sum([abs(corr_matrix.loc[protein, other]) 
                                           for other in cluster_protein_ids if other != protein])
            
            # 找出连接度最高的蛋白作为Hub
            hub = max(connectivity.items(), key=lambda x: x[1])[0]
            hub_proteins[cluster] = hub
            
            # 收集簇的信息
            hazard_ratio = cluster_proteins.iloc[:, 1].mean()
            p_value = cluster_proteins.iloc[:, 4].min()
            num_proteins = len(cluster_proteins)
            
            cluster_info[cluster] = {
                'Hub蛋白': hub,
                '蛋白数量': num_proteins,
                '平均风险比': hazard_ratio,
                '最小P值': p_value,
                '蛋白列表': ', '.join(cluster_protein_ids[:5]) + (', ...' if num_proteins > 5 else '')
            }
    
    # 9. 创建网络可视化
    plt.figure(figsize=(14, 10))
    
    # 创建图
    G = nx.Graph()
    
    # 添加节点和聚类信息
    for idx, row in hfsnef_specific.iterrows():
        protein = row.iloc[0]
        cluster = row['Cluster']
        hr = row.iloc[1]
        pval = row.iloc[4]
        
        # 节点大小基于-log10(p-value)
        node_size = -np.log10(pval) * 20
        
        # 添加节点
        G.add_node(protein, 
                   cluster=cluster, 
                   hr=hr, 
                   pval=pval, 
                   size=node_size,
                   hub=(protein in hub_proteins.values()))
    
    # 添加边（如果相关性大于阈值）
    threshold = 0.3  # 相关性阈值
    
    for i, protein1 in enumerate(corr_matrix.index):
        for j, protein2 in enumerate(corr_matrix.columns):
            if i < j:  # 避免重复边
                correlation = corr_matrix.loc[protein1, protein2]
                if abs(correlation) > threshold:
                    G.add_edge(protein1, protein2, weight=abs(correlation))
    
    # 设置节点颜色（按簇）
    colors = plt.cm.rainbow(np.linspace(0, 1, n_clusters))
    node_colors = [colors[G.nodes[node]['cluster']-1] for node in G.nodes()]
    
    # 节点大小
    node_sizes = [G.nodes[node]['size'] * 10 for node in G.nodes()]
    
    # 边的粗细
    edge_weights = [G[u][v]['weight'] * 2 for u, v in G.edges()]
    
    # 布局
    pos = nx.spring_layout(G, seed=42, k=0.5)  # k控制节点间距离
    
    # 绘制网络
    plt.figure(figsize=(16, 12))
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, width=edge_weights, alpha=0.4, edge_color='gray')
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos, 
                          node_size=node_sizes, 
                          node_color=node_colors, 
                          alpha=0.8)
    
    # 添加Hub蛋白标签
    hub_labels = {node: node for node in G.nodes() if G.nodes[node]['hub']}
    nx.draw_networkx_labels(G, pos, labels=hub_labels, font_size=12, font_weight='bold')
    
    # 添加图例
    for i in range(n_clusters):
        plt.scatter([], [], color=colors[i], label=f'簇 {i+1} (Hub: {hub_proteins.get(i+1, "无")})')
    
    plt.legend(loc='upper right', bbox_to_anchor=(1.15, 1.0))
    plt.title('HFsnEF特异蛋白网络', fontsize=16)
    plt.axis('off')
    plt.tight_layout()
    plt.savefig('hfsnef_specific_protein_network.png', dpi=300, bbox_inches='tight')
    
    # 10. 输出簇信息表
    cluster_df = pd.DataFrame.from_dict(cluster_info, orient='index')
    print("\n蛋白质簇信息:")
    print(cluster_df)
    cluster_df.to_csv('hfsnef_specific_protein_clusters.csv')
    
    # 11. 热图可视化
    if has_expression_data:
        # 按聚类排序
        ordered_proteins = []
        for cluster in range(1, n_clusters + 1):
            cluster_proteins = hfsnef_specific[hfsnef_specific['Cluster'] == cluster].iloc[:, 0]
            ordered_proteins.extend(list(cluster_proteins))
            
        # 绘制热图
        plt.figure(figsize=(14, 10))
        sns.clustermap(
            corr_matrix.loc[ordered_proteins, ordered_proteins],
            cmap='RdBu_r',
            vmin=-1, 
            vmax=1,
            row_cluster=False,
            col_cluster=False,
            yticklabels=True,
            xticklabels=True
        )
        plt.title('HFsnEF特异蛋白相关性热图', fontsize=16)
        plt.savefig('hfsnef_specific_protein_heatmap.png', dpi=300, bbox_inches='tight')

    # 12. 创建森林图展示HFsnEF特异蛋白的风险比
    top_proteins = hfsnef_specific.sort_values(by=hfsnef_specific.columns[4]).iloc[:10]  # 按p值排序取前10
    
    plt.figure(figsize=(10, 8))
    
    # 绘制森林图
    y_pos = range(len(top_proteins))
    hr = top_proteins.iloc[:, 1].values
    lower_ci = top_proteins.iloc[:, 2].values
    upper_ci = top_proteins.iloc[:, 3].values
    protein_names = top_proteins.iloc[:, 0].values
    
    # 计算置信区间长度
    ci_lengths = upper_ci - lower_ci
    
    plt.figure(figsize=(10, 8))
    plt.errorbar(hr, y_pos, xerr=ci_lengths/2, fmt='o', capsize=5, capthick=1.5)
    plt.axvline(x=1, color='gray', linestyle='--')
    
    plt.yticks(y_pos, protein_names)
    plt.xlabel('风险比 (Hazard Ratio)', fontsize=12)
    plt.title('HFsnEF特异显著蛋白的风险比森林图', fontsize=14)
    
    plt.xlim(0, max(3, max(upper_ci) * 1.1))
    plt.grid(axis='x', linestyle='--', alpha=0.7)
    
    for i, (name, hr_val, p_val) in enumerate(zip(protein_names, hr, top_proteins.iloc[:, 4])):
        plt.text(hr_val + 0.1, i, f'HR={hr_val:.2f}, p={p_val:.4f}', va='center')
    
    plt.tight_layout()
    plt.savefig('hfsnef_specific_protein_forest_plot.png', dpi=300)

else:
    print("HFsnEF特异显著蛋白数量太少，无法进行聚类分析")

print("\n分析完成，结果已保存。")

# 13. 尝试进行功能富集分析
try:
    # 这里需要安装额外的包，如果环境中有gseapy，可以使用它进行快速的GO富集分析
    import gseapy as gp
    
    if len(hfsnef_specific) >= 5:  # 确保有足够的蛋白进行富集分析
        # 提取蛋白质名称列表
        protein_list = list(hfsnef_specific.iloc[:, 0])
        
        # 进行GO富集分析
        print("进行GO功能富集分析...")
        try:
            # 进行GO BP富集分析
            enr_bp = gp.enrichr(gene_list=protein_list,
                            gene_sets=['GO_Biological_Process_2021'],
                            organism='Human',
                            outdir='enrichment_results',
                            cutoff=0.05)
            
            # 保存结果
            bp_results = enr_bp.results
            bp_results.to_csv('hfsnef_specific_protein_go_bp.csv', index=False)
            
            # 绘制GO条形图 - BP
            plt.figure(figsize=(12, 8))
            gp.barplot(enr_bp.results, title='HFsnEF特异蛋白GO生物过程富集分析', 
                     color='salmon', top_term=10, figsize=(12, 6))
            plt.tight_layout()
            plt.savefig('hfsnef_specific_protein_go_bp.png', dpi=300)
            
            # 进行GO CC富集分析
            enr_cc = gp.enrichr(gene_list=protein_list,
                            gene_sets=['GO_Cellular_Component_2021'],
                            organism='Human',
                            outdir='enrichment_results',
                            cutoff=0.05)
                            
            # 保存结果
            cc_results = enr_cc.results
            cc_results.to_csv('hfsnef_specific_protein_go_cc.csv', index=False)
            
            # 绘制GO条形图 - CC
            plt.figure(figsize=(12, 8))
            gp.barplot(enr_cc.results, title='HFsnEF特异蛋白GO细胞组分富集分析', 
                     color='lightblue', top_term=10, figsize=(12, 6))
            plt.tight_layout()
            plt.savefig('hfsnef_specific_protein_go_cc.png', dpi=300)
            
            # 进行KEGG通路富集分析
            enr_kegg = gp.enrichr(gene_list=protein_list,
                               gene_sets=['KEGG_2021_Human'],
                               organism='Human',
                               outdir='enrichment_results',
                               cutoff=0.05)
                               
            # 保存结果
            kegg_results = enr_kegg.results
            kegg_results.to_csv('hfsnef_specific_protein_kegg.csv', index=False)
            
            # 绘制KEGG条形图
            plt.figure(figsize=(12, 8))
            gp.barplot(enr_kegg.results, title='HFsnEF特异蛋白KEGG通路富集分析', 
                     color='lightgreen', top_term=10, figsize=(12, 6))
            plt.tight_layout()
            plt.savefig('hfsnef_specific_protein_kegg.png', dpi=300)
            
            print("功能富集分析完成，结果已保存。")
            
        except Exception as e:
            print(f"功能富集分析失败: {e}")
            print("使用替代方法生成简单的功能分析报告...")
            
            # 创建简单的功能分析报告
            with open('hfsnef_specific_protein_functional_report.txt', 'w') as f:
                f.write("HFsnEF特异蛋白功能分析报告\n")
                f.write("="*40 + "\n\n")
                f.write(f"共分析 {len(protein_list)} 个蛋白质\n\n")
                
                # 输出每个蛋白及其风险比和p值
                f.write("蛋白质列表及Cox回归结果：\n")
                for idx, row in hfsnef_specific.iterrows():
                    protein = row.iloc[0]
                    hr = row.iloc[1]
                    pval = row.iloc[4]
                    f.write(f"- {protein}: HR={hr:.2f}, p={pval:.4f}\n")
                
                f.write("\n蛋白质簇分析结果：\n")
                for cluster, info in cluster_info.items():
                    f.write(f"\n簇 {cluster}:\n")
                    f.write(f"  Hub蛋白: {info['Hub蛋白']}\n")
                    f.write(f"  蛋白数量: {info['蛋白数量']}\n")
                    f.write(f"  平均风险比: {info['平均风险比']:.2f}\n")
                    f.write(f"  最小P值: {info['最小P值']:.4f}\n")
                    f.write(f"  蛋白列表: {info['蛋白列表']}\n")
                
    else:
        print("蛋白数量过少，无法进行有效的功能富集分析")
        
except ImportError:
    print("未安装gseapy，无法进行GO和KEGG富集分析") 