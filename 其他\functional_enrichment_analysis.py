#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能富集分析和可视化脚本
基于显著差异蛋白质进行GO功能分析和通路富集分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 检查当前工作目录
print(f"当前工作目录: {os.getcwd()}")
print(f"目录中的文件: {os.listdir('.')[:10]}")  # 显示前10个文件

def load_and_filter_data():
    """加载数据并筛选显著差异蛋白质"""
    
    print("正在加载数据...")
    
    # 加载Cox回归结果
    cox_results = pd.read_csv('overall_protein_cox_results.csv')
    
    # 加载蛋白质功能注释
    protein_annotations = pd.read_csv('Proteins_all_diff.csv')
    
    # 筛选FDR < 0.05的显著蛋白质
    significant_proteins = cox_results[cox_results['fdr_value'] < 0.05]
    
    print(f"总蛋白质数量: {len(cox_results)}")
    print(f"FDR < 0.05的显著蛋白质数量: {len(significant_proteins)}")
    
    # 合并数据
    # 使用蛋白质名称进行匹配
    merged_data = significant_proteins.merge(
        protein_annotations, 
        left_on='protein', 
        right_on='Gene Name', 
        how='inner'
    )
    
    print(f"成功匹配功能注释的蛋白质数量: {len(merged_data)}")
    
    return merged_data, significant_proteins

def parse_go_terms(go_string):
    """解析GO术语字符串"""
    if pd.isna(go_string) or go_string == '':
        return []
    
    terms = []
    for term in go_string.split(';'):
        if ',' in term:
            go_id, description = term.split(',', 1)
            terms.append({
                'go_id': go_id.strip(),
                'description': description.strip()
            })
    return terms

def create_go_enrichment_heatmap(merged_data, go_category, title, filename):
    """创建GO富集热图"""
    
    print(f"正在创建{title}...")
    
    # 解析GO术语
    all_terms = []
    protein_go_matrix = []
    
    for _, row in merged_data.iterrows():
        go_terms = parse_go_terms(row[go_category])
        protein_terms = [term['description'] for term in go_terms]
        all_terms.extend(protein_terms)
        protein_go_matrix.append(protein_terms)
    
    # 统计术语频率
    term_counts = Counter(all_terms)
    
    # 选择前20个最常见的术语
    top_terms = [term for term, count in term_counts.most_common(20)]
    
    if len(top_terms) == 0:
        print(f"警告: {go_category}类别没有找到GO术语")
        return
    
    # 创建蛋白质-GO术语矩阵
    protein_names = merged_data['protein'].tolist()
    matrix = np.zeros((len(protein_names), len(top_terms)))
    
    for i, protein_terms in enumerate(protein_go_matrix):
        for j, term in enumerate(top_terms):
            if term in protein_terms:
                matrix[i, j] = 1
    
    # 创建热图
    fig, ax = plt.subplots(figsize=(15, max(8, len(protein_names) * 0.3)))
    
    # 绘制热图
    sns.heatmap(matrix, 
                xticklabels=[term[:50] + '...' if len(term) > 50 else term for term in top_terms],
                yticklabels=protein_names,
                cmap='RdYlBu_r',
                cbar_kws={'label': '蛋白质参与该功能'},
                ax=ax)
    
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('GO功能术语', fontsize=12)
    ax.set_ylabel('显著差异蛋白质', fontsize=12)
    
    # 旋转x轴标签
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")
    
    # 返回统计信息
    return {
        'total_terms': len(all_terms),
        'unique_terms': len(set(all_terms)),
        'top_terms': top_terms[:10],
        'term_counts': dict(term_counts.most_common(10))
    }

def create_functional_category_barplot(merged_data):
    """创建功能分类柱状图"""
    
    print("正在创建功能分类统计图...")
    
    categories = ['CC', 'MF', 'BP']
    category_names = ['细胞组分 (CC)', '分子功能 (MF)', '生物过程 (BP)']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    category_stats = {}
    
    for i, (cat, cat_name) in enumerate(zip(categories, category_names)):
        # 解析GO术语
        all_terms = []
        for _, row in merged_data.iterrows():
            go_terms = parse_go_terms(row[cat])
            terms = [term['description'] for term in go_terms]
            all_terms.extend(terms)
        
        # 统计术语频率
        term_counts = Counter(all_terms)
        top_terms = term_counts.most_common(15)
        
        if len(top_terms) > 0:
            terms, counts = zip(*top_terms)
            
            # 创建柱状图
            bars = axes[i].barh(range(len(terms)), counts, 
                               color=plt.cm.Set3(np.linspace(0, 1, len(terms))))
            
            axes[i].set_yticks(range(len(terms)))
            axes[i].set_yticklabels([term[:40] + '...' if len(term) > 40 else term for term in terms])
            axes[i].set_xlabel('蛋白质数量')
            axes[i].set_title(f'{cat_name} - 前15个功能', fontweight='bold')
            axes[i].grid(axis='x', alpha=0.3)
            
            # 添加数值标签
            for j, bar in enumerate(bars):
                width = bar.get_width()
                axes[i].text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                           f'{int(width)}', ha='left', va='center', fontsize=9)
        
        category_stats[cat] = {
            'total_terms': len(all_terms),
            'unique_terms': len(set(all_terms)),
            'top_terms': top_terms[:5] if top_terms else []
        }
    
    # 创建总体统计图
    total_counts = [category_stats[cat]['unique_terms'] for cat in categories]
    bars = axes[3].bar(category_names, total_counts, 
                      color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    
    axes[3].set_title('各功能类别的唯一术语数量', fontweight='bold')
    axes[3].set_ylabel('唯一GO术语数量')
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        axes[3].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('functional_categories_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print("功能分类统计图已保存到: functional_categories_analysis.png")
    
    return category_stats

def create_protein_function_network(merged_data):
    """创建蛋白质-功能网络图（简化版）"""
    
    print("正在创建蛋白质功能关联矩阵...")
    
    # 选择前20个最显著的蛋白质
    top_proteins = merged_data.head(20)
    
    # 收集所有BP术语
    all_bp_terms = []
    for _, row in top_proteins.iterrows():
        bp_terms = parse_go_terms(row['BP'])
        terms = [term['description'] for term in bp_terms]
        all_bp_terms.extend(terms)
    
    # 选择最常见的术语
    term_counts = Counter(all_bp_terms)
    top_terms = [term for term, count in term_counts.most_common(15)]
    
    if len(top_terms) == 0:
        print("警告: 没有找到足够的BP术语")
        return
    
    # 创建关联矩阵
    protein_names = top_proteins['protein'].tolist()
    matrix = np.zeros((len(protein_names), len(top_terms)))
    
    for i, (_, row) in enumerate(top_proteins.iterrows()):
        bp_terms = parse_go_terms(row['BP'])
        protein_terms = [term['description'] for term in bp_terms]
        
        for j, term in enumerate(top_terms):
            if term in protein_terms:
                matrix[i, j] = 1
    
    # 创建热图
    fig, ax = plt.subplots(figsize=(16, 10))
    
    sns.heatmap(matrix,
                xticklabels=[term[:30] + '...' if len(term) > 30 else term for term in top_terms],
                yticklabels=protein_names,
                cmap='YlOrRd',
                cbar_kws={'label': '蛋白质参与该生物过程'},
                ax=ax)
    
    ax.set_title('前20个显著蛋白质的生物过程关联', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('生物过程 (BP)', fontsize=12)
    ax.set_ylabel('显著差异蛋白质', fontsize=12)
    
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    plt.savefig('protein_function_network.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print("蛋白质功能关联图已保存到: protein_function_network.png")

def main():
    """主函数"""
    
    print("=" * 80)
    print("功能富集分析和可视化")
    print("=" * 80)
    
    # 1. 加载和筛选数据
    merged_data, significant_proteins = load_and_filter_data()
    
    if len(merged_data) == 0:
        print("错误: 没有找到匹配的蛋白质功能注释数据")
        return
    
    print(f"\n成功匹配的显著蛋白质: {len(merged_data)}")
    print("前10个最显著的蛋白质:")
    for i, row in merged_data.head(10).iterrows():
        print(f"  {i+1}. {row['protein']} (FDR: {row['fdr_value']:.4f})")
    
    # 2. 创建功能分类统计图
    print(f"\n" + "="*50)
    print("1. 功能分类分析")
    print("="*50)
    
    category_stats = create_functional_category_barplot(merged_data)
    
    # 3. 创建GO富集热图
    print(f"\n" + "="*50)
    print("2. GO富集热图分析")
    print("="*50)
    
    # 细胞组分热图
    cc_stats = create_go_enrichment_heatmap(
        merged_data, 'CC', 
        'Cellular Component (CC) - 显著蛋白质功能热图',
        'cellular_component_heatmap.png'
    )
    
    # 分子功能热图
    mf_stats = create_go_enrichment_heatmap(
        merged_data, 'MF',
        'Molecular Function (MF) - 显著蛋白质功能热图', 
        'molecular_function_heatmap.png'
    )
    
    # 生物过程热图
    bp_stats = create_go_enrichment_heatmap(
        merged_data, 'BP',
        'Biological Process (BP) - 显著蛋白质功能热图',
        'biological_process_heatmap.png'
    )
    
    # 4. 创建蛋白质功能网络图
    print(f"\n" + "="*50)
    print("3. 蛋白质功能关联分析")
    print("="*50)
    
    create_protein_function_network(merged_data)
    
    # 5. 输出总结报告
    print(f"\n" + "="*80)
    print("分析完成！生成的文件:")
    print("="*80)
    print("• functional_categories_analysis.png - 功能分类统计图")
    print("• cellular_component_heatmap.png - 细胞组分功能热图")
    print("• molecular_function_heatmap.png - 分子功能热图")
    print("• biological_process_heatmap.png - 生物过程热图")
    print("• protein_function_network.png - 蛋白质功能关联图")
    print("="*80)

if __name__ == "__main__":
    main()
