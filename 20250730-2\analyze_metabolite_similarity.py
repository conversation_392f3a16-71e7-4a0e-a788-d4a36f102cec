#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析正负离子模式代谢物名称的相似性
检查是否有相似但不完全相同的代谢物名称
"""

import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re

def similarity(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a, b).ratio()

def clean_metabolite_name(name):
    """清理代谢物名称，去除一些可能的差异"""
    # 转换为小写
    name = name.lower()
    # 去除多余的空格
    name = re.sub(r'\s+', ' ', name).strip()
    # 去除一些常见的前缀/后缀差异
    name = re.sub(r'\([+-]\)', '', name)  # 去除 (+) 或 (-)
    name = re.sub(r'\s*\([+-]\)\s*', ' ', name)
    return name

def find_similar_metabolites(neg_df, pos_df, threshold=0.8):
    """
    寻找相似的代谢物名称
    """
    print("正在分析代谢物名称相似性...")
    
    neg_names = neg_df['CPD_Name'].tolist()
    pos_names = pos_df['CPD_Name'].tolist()
    
    # 清理名称
    neg_names_clean = [clean_metabolite_name(name) for name in neg_names]
    pos_names_clean = [clean_metabolite_name(name) for name in pos_names]
    
    similar_pairs = []
    
    for i, neg_name in enumerate(neg_names):
        neg_clean = neg_names_clean[i]
        for j, pos_name in enumerate(pos_names):
            pos_clean = pos_names_clean[j]
            
            # 计算原始名称和清理后名称的相似度
            sim_original = similarity(neg_name, pos_name)
            sim_clean = similarity(neg_clean, pos_clean)
            
            if sim_original >= threshold or sim_clean >= threshold:
                similar_pairs.append({
                    'neg_cpd_id': neg_df.iloc[i]['CPD_ID'],
                    'neg_name': neg_name,
                    'pos_cpd_id': pos_df.iloc[j]['CPD_ID'],
                    'pos_name': pos_name,
                    'similarity_original': sim_original,
                    'similarity_clean': sim_clean,
                    'max_similarity': max(sim_original, sim_clean)
                })
    
    return similar_pairs

def analyze_name_patterns(neg_df, pos_df):
    """
    分析代谢物名称的模式
    """
    print("\n=== 代谢物名称模式分析 ===")
    
    neg_names = neg_df['CPD_Name'].tolist()
    pos_names = pos_df['CPD_Name'].tolist()
    
    # 分析名称长度
    neg_lengths = [len(name) for name in neg_names]
    pos_lengths = [len(name) for name in pos_names]
    
    print(f"负离子模式名称长度: 平均 {np.mean(neg_lengths):.1f}, 范围 {min(neg_lengths)}-{max(neg_lengths)}")
    print(f"正离子模式名称长度: 平均 {np.mean(pos_lengths):.1f}, 范围 {min(pos_lengths)}-{max(pos_lengths)}")
    
    # 分析常见的开头
    neg_prefixes = {}
    pos_prefixes = {}
    
    for name in neg_names:
        prefix = name[:20] if len(name) >= 20 else name
        neg_prefixes[prefix] = neg_prefixes.get(prefix, 0) + 1
    
    for name in pos_names:
        prefix = name[:20] if len(name) >= 20 else name
        pos_prefixes[prefix] = pos_prefixes.get(prefix, 0) + 1
    
    print(f"\n负离子模式常见前缀 (前5个):")
    for prefix, count in sorted(neg_prefixes.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  '{prefix}': {count}次")
    
    print(f"\n正离子模式常见前缀 (前5个):")
    for prefix, count in sorted(pos_prefixes.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  '{prefix}': {count}次")

def main():
    """
    主函数
    """
    # 读取数据
    neg_df = pd.read_csv("data_all.diff_NEG.tsv", sep='\t')
    pos_df = pd.read_csv("data_all.diff_POS.tsv", sep='\t')
    
    print(f"负离子模式代谢物数: {len(neg_df)}")
    print(f"正离子模式代谢物数: {len(pos_df)}")
    
    # 分析名称模式
    analyze_name_patterns(neg_df, pos_df)
    
    # 寻找相似的代谢物
    similar_pairs = find_similar_metabolites(neg_df, pos_df, threshold=0.7)
    
    print(f"\n=== 相似代谢物分析结果 ===")
    print(f"找到 {len(similar_pairs)} 对相似的代谢物 (相似度 >= 0.7)")
    
    if similar_pairs:
        # 按相似度排序
        similar_pairs.sort(key=lambda x: x['max_similarity'], reverse=True)
        
        print(f"\n前10个最相似的代谢物对:")
        for i, pair in enumerate(similar_pairs[:10]):
            print(f"\n{i+1}. 相似度: {pair['max_similarity']:.3f}")
            print(f"   NEG: {pair['neg_cpd_id']} - {pair['neg_name'][:80]}...")
            print(f"   POS: {pair['pos_cpd_id']} - {pair['pos_name'][:80]}...")
        
        # 保存结果
        similar_df = pd.DataFrame(similar_pairs)
        similar_df.to_csv('similar_metabolites.csv', index=False, encoding='utf-8-sig')
        print(f"\n相似代谢物列表已保存到: similar_metabolites.csv")
    else:
        print("没有找到相似度 >= 0.7 的代谢物对")
    
    # 检查一些具体的例子
    print(f"\n=== 随机样本检查 ===")
    print("负离子模式前5个代谢物:")
    for i in range(min(5, len(neg_df))):
        print(f"  {neg_df.iloc[i]['CPD_ID']}: {neg_df.iloc[i]['CPD_Name'][:60]}...")
    
    print("\n正离子模式前5个代谢物:")
    for i in range(min(5, len(pos_df))):
        print(f"  {pos_df.iloc[i]['CPD_ID']}: {pos_df.iloc[i]['CPD_Name'][:60]}...")

if __name__ == "__main__":
    main()
