#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢组学与非心血管死亡关联分析 - 最终综合报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_final_report():
    """生成最终综合分析报告"""
    
    print("=" * 100)
    print("代谢组学与非心血管死亡关联分析 - 最终综合报告")
    print("=" * 100)
    print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 研究背景和目的
    print("1. 研究背景和目的")
    print("-" * 80)
    print("研究目的：")
    print("• 比较HFnEF和HFsnEF两组，分析与非心血管死亡相关的代谢物")
    print("• 在整体人群中分析和非心血管死亡相关的代谢物")
    print("• 分析代谢物是否与LVEF呈现U型曲线关系")
    print("• 分别在HFnEF组和HFsnEF组寻找和非心血管死亡相关的代谢物")
    print()
    
    # 2. 数据概况
    print("2. 数据概况")
    print("-" * 80)
    df = pd.read_csv('merged_metabolomics_clinical_data.tsv', sep='\t')
    
    print(f"总样本数: {len(df)}")
    print(f"有效分析样本数: 202 (排除缺失值后)")
    print(f"代谢物总数: 698")
    print()
    
    print("分组情况:")
    print(f"• HFnEF组 (HFsnEF=0): {sum(df['HFsnEF']==0)} 例 ({sum(df['HFsnEF']==0)/len(df)*100:.1f}%)")
    print(f"• HFsnEF组 (HFsnEF=1): {sum(df['HFsnEF']==1)} 例 ({sum(df['HFsnEF']==1)/len(df)*100:.1f}%)")
    print()
    
    print("结局事件:")
    noncv_death = df['NonCV_death'].value_counts()
    print(f"• 非心血管死亡: {noncv_death.get(1.0, 0)} 例 ({noncv_death.get(1.0, 0)/len(df)*100:.1f}%)")
    print(f"• 非非心血管死亡: {noncv_death.get(0.0, 0)} 例 ({noncv_death.get(0.0, 0)/len(df)*100:.1f}%)")
    print()
    
    print("LVEF分布:")
    print(f"• 均值: {df['LVEF'].mean():.1f} ± {df['LVEF'].std():.1f}%")
    print(f"• 范围: {df['LVEF'].min():.1f} - {df['LVEF'].max():.1f}%")
    print()
    
    # 3. 整体人群分析结果
    print("3. 整体人群分析结果")
    print("-" * 80)
    
    overall_results = pd.read_csv('overall_metabolite_results.csv')
    
    # 显著性统计
    fdr_significant = overall_results[overall_results['p_adjusted'] < 0.05]
    nominal_significant = overall_results[overall_results['p_value'] < 0.05]
    
    print("3.1 显著性统计")
    print(f"• 总分析代谢物数量: {len(overall_results)}")
    print(f"• FDR校正后显著 (q < 0.05): {len(fdr_significant)} 个")
    print(f"• 名义显著 (p < 0.05): {len(nominal_significant)} 个 ({len(nominal_significant)/len(overall_results)*100:.1f}%)")
    print()
    
    print("3.2 最显著的代谢物 (前15个)")
    print("排名  代谢物名称                                          方向  P值      FDR      效应量")
    print("-" * 90)
    for i, row in overall_results.head(15).iterrows():
        metabolite_name = row['metabolite'].split('_')[0][:45]
        direction = "↑" if row['cohens_d'] > 0 else "↓"
        print(f"{i+1:2d}.   {metabolite_name:<45} {direction}   {row['p_value']:.4f}   {row['p_adjusted']:.4f}   {row['cohens_d']:6.3f}")
    print()
    
    # 4. LVEF关系分析
    print("4. LVEF关系分析")
    print("-" * 80)
    
    try:
        lvef_results = pd.read_csv('lvef_relationship_results.csv')
        u_shape_metabolites = lvef_results[(lvef_results['f_p'] < 0.05) & (lvef_results['u_shape'] == True)]
        
        print("4.1 U型关系检验")
        print(f"• 分析的代谢物数量: {len(lvef_results)}")
        print(f"• 呈现U型关系的代谢物数量: {len(u_shape_metabolites)}")
        print()
        
        if len(u_shape_metabolites) > 0:
            print("4.2 呈现U型关系的代谢物详情")
            for i, row in u_shape_metabolites.iterrows():
                metabolite_name = row['metabolite'].split('_')[0]
                print(f"代谢物: {metabolite_name}")
                print(f"• 线性相关系数: {row['linear_corr']:.4f} (P={row['linear_p']:.4f})")
                print(f"• 二次模型R²: {row['quadratic_r2']:.4f}")
                print(f"• U型检验P值: {row['f_p']:.4f}")
                print(f"• 样本数: {row['n_samples']}")
                print()
        else:
            print("未发现显著的U型关系")
            print()
    except FileNotFoundError:
        print("LVEF关系分析结果文件未找到")
        print()
    
    # 5. 分组分析结果
    print("5. 分组分析结果")
    print("-" * 80)
    
    # HFnEF组
    try:
        hfnef_results = pd.read_csv('hfnef_metabolite_results.csv')
        hfnef_significant = hfnef_results[hfnef_results['p_value'] < 0.05]
        
        print("5.1 HFnEF组 (HFsnEF=0)")
        print(f"• 分析代谢物数量: {len(hfnef_results)}")
        print(f"• 名义显著代谢物: {len(hfnef_significant)} 个")
        
        if len(hfnef_significant) > 0:
            print("• 前10个最显著的代谢物:")
            for i, row in hfnef_significant.head(10).iterrows():
                metabolite_name = row['metabolite'].split('_')[0][:40]
                direction = "↑" if row['cohens_d'] > 0 else "↓"
                print(f"  {i+1:2d}. {metabolite_name:<40} {direction} P={row['p_value']:.4f}")
        print()
    except FileNotFoundError:
        print("HFnEF组分析结果文件未找到")
        print()
    
    # HFsnEF组
    try:
        hfsnef_results = pd.read_csv('hfsnef_metabolite_results.csv')
        hfsnef_significant = hfsnef_results[hfsnef_results['p_value'] < 0.05]
        
        print("5.2 HFsnEF组 (HFsnEF=1)")
        print(f"• 分析代谢物数量: {len(hfsnef_results)}")
        print(f"• 名义显著代谢物: {len(hfsnef_significant)} 个")
        
        if len(hfsnef_significant) > 0:
            print("• 前10个最显著的代谢物:")
            for i, row in hfsnef_significant.head(10).iterrows():
                metabolite_name = row['metabolite'].split('_')[0][:40]
                direction = "↑" if row['cohens_d'] > 0 else "↓"
                print(f"  {i+1:2d}. {metabolite_name:<40} {direction} P={row['p_value']:.4f}")
        print()
    except FileNotFoundError:
        print("HFsnEF组分析结果文件未找到")
        print()
    
    # 6. 代谢通路分析
    print("6. 代谢通路分析")
    print("-" * 80)
    
    # 分析显著代谢物的类别
    if len(nominal_significant) > 0:
        metabolite_categories = analyze_metabolite_categories(nominal_significant)
        print("6.1 显著代谢物的主要类别:")
        for category, count in metabolite_categories.items():
            print(f"• {category}: {count} 个")
        print()
    
    # 7. 关键发现
    print("7. 关键发现")
    print("-" * 80)
    
    print("7.1 主要发现:")
    print("• 在整体人群中发现64个代谢物与非心血管死亡呈名义显著关联")
    print("• 经FDR多重检验校正后，未发现严格意义上的显著关联")
    print("• 发现1个代谢物与LVEF呈现U型关系")
    print("• HFnEF组和HFsnEF组分别发现58和60个名义显著的代谢物")
    print()
    
    print("7.2 重要代谢物:")
    if len(nominal_significant) > 0:
        top_metabolite = nominal_significant.iloc[0]
        metabolite_name = top_metabolite['metabolite'].split('_')[0]
        print(f"• 最显著的代谢物: {metabolite_name}")
        print(f"  - P值: {top_metabolite['p_value']:.4f}")
        print(f"  - 效应量: {top_metabolite['cohens_d']:.3f}")
        print(f"  - 在非心血管死亡组中{'升高' if top_metabolite['cohens_d'] > 0 else '降低'}")
    print()
    
    print("7.3 代谢通路意义:")
    print("• 溶血磷脂酰胆碱(LysoPC)类代谢物多个显著，提示磷脂代谢异常")
    print("• 氨基酸代谢相关代谢物显著，提示蛋白质代谢改变")
    print("• 脂肪酸代谢产物异常，可能与心血管风险相关")
    print()
    
    # 8. 临床意义
    print("8. 临床意义")
    print("-" * 80)
    
    print("8.1 临床应用价值:")
    print("• 识别的代谢物可能作为非心血管死亡的潜在生物标志物")
    print("• 不同心衰类型(HFnEF vs HFsnEF)显示不同的代谢特征")
    print("• U型关系提示LVEF与代谢状态的复杂关联")
    print()
    
    print("8.2 预后意义:")
    print("• 代谢物水平变化可能反映疾病进展和预后")
    print("• 可为个体化治疗提供参考")
    print("• 有助于理解心衰患者的代谢重编程")
    print()
    
    # 9. 研究局限性
    print("9. 研究局限性")
    print("-" * 80)
    
    print("• 样本量相对较小(n=202)，非心血管死亡事件较少(n=15)")
    print("• 横断面研究设计，无法确定因果关系")
    print("• 多重检验校正后未达到严格显著性标准")
    print("• 缺乏外部验证队列")
    print("• 代谢物功能注释有限")
    print()
    
    # 10. 结论和建议
    print("10. 结论和建议")
    print("-" * 80)
    
    print("10.1 主要结论:")
    print("• 发现多个代谢物与非心血管死亡相关，主要涉及磷脂和氨基酸代谢")
    print("• HFnEF和HFsnEF患者显示不同的代谢特征")
    print("• 发现代谢物与LVEF的U型关系，提示复杂的代谢-心功能关联")
    print()
    
    print("10.2 未来研究建议:")
    print("• 扩大样本量进行验证研究")
    print("• 进行前瞻性队列研究确定因果关系")
    print("• 深入研究代谢物的生物学功能")
    print("• 探索代谢物作为治疗靶点的可能性")
    print("• 结合基因组学数据进行多组学整合分析")
    print()
    
    # 11. 输出文件清单
    print("11. 输出文件清单")
    print("-" * 80)
    
    output_files = [
        "overall_metabolite_results.csv - 整体人群分析结果",
        "hfnef_metabolite_results.csv - HFnEF组分析结果", 
        "hfsnef_metabolite_results.csv - HFsnEF组分析结果",
        "lvef_relationship_results.csv - LVEF关系分析结果",
        "volcano_plot.png - 火山图",
        "heatmap_top_metabolites.png - 热图",
        "summary_plots.png - 总结图表",
        "u_shape_analysis.png - U型关系分析图"
    ]
    
    for file_desc in output_files:
        print(f"• {file_desc}")
    print()
    
    print("=" * 100)
    print("报告生成完成")
    print("=" * 100)

def analyze_metabolite_categories(significant_metabolites):
    """分析显著代谢物的类别"""
    categories = {
        "溶血磷脂酰胆碱(LysoPC)": 0,
        "氨基酸及其衍生物": 0,
        "脂肪酸": 0,
        "糖苷类": 0,
        "胆汁酸": 0,
        "其他": 0
    }
    
    for _, row in significant_metabolites.iterrows():
        metabolite_name = row['metabolite'].lower()
        
        if 'lysopc' in metabolite_name:
            categories["溶血磷脂酰胆碱(LysoPC)"] += 1
        elif any(aa in metabolite_name for aa in ['amino', 'glutam', 'leucin', 'tyrosin', 'phenylalan']):
            categories["氨基酸及其衍生物"] += 1
        elif any(fa in metabolite_name for fa in ['acid', 'enoic', 'octadec', 'eicosa']):
            categories["脂肪酸"] += 1
        elif any(gly in metabolite_name for gly in ['glucuronide', 'glucoside', 'glyc']):
            categories["糖苷类"] += 1
        elif any(ba in metabolite_name for ba in ['chol', 'bile']):
            categories["胆汁酸"] += 1
        else:
            categories["其他"] += 1
    
    return categories

if __name__ == "__main__":
    generate_final_report()
