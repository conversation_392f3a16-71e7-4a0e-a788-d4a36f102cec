import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib_venn import venn2
import os
from scipy import stats

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    df = pd.read_csv("merged_protein_clinical_data.csv", low_memory=False)
    
    # 查看前几行数据了解结构
    print(f"数据维度: {df.shape}")
    
    # 找出临床变量的最后一列（Time_to first_all_casue_hospitalization）
    clinical_columns = []
    protein_columns = []
    
    # 查找临床变量与蛋白质变量的分界
    for i, col in enumerate(df.columns):
        if col == "Time_to first_all_casue_hospitalization":
            clinical_end_idx = i
            break
    
    # 分离临床变量和蛋白质变量
    clinical_columns = df.columns[:clinical_end_idx+1]
    protein_columns = df.columns[clinical_end_idx+1:]
    
    print(f"临床变量数量: {len(clinical_columns)}")
    print(f"蛋白质变量数量: {len(protein_columns)}")
    
    # 根据HFsnEF分组（1表示HFsnEF，0表示HFnEF）
    hfsnef_df = df[df['HFsnEF'] == 1]
    hfnef_df = df[df['HFsnEF'] == 0]
    
    print(f"HFsnEF组样本数: {hfsnef_df.shape[0]}")
    print(f"HFnEF组样本数: {hfnef_df.shape[0]}")
    
    # 设置p值阈值
    p_threshold = 0.05
    # 设置倍数变化阈值
    fold_change_threshold = 1.5
    
    # 存储差异表达蛋白质
    hfsnef_specific = []  # HFsnEF组特异高表达的蛋白质
    hfnef_specific = []   # HFnEF组特异高表达的蛋白质
    shared = []           # 两组表达相似的蛋白质
    
    # 统计结果
    significant_proteins = 0
    non_significant_proteins = 0
    
    # 对每个蛋白质进行统计分析
    for protein in protein_columns:
        try:
            # 获取两组该蛋白质的表达值
            hfsnef_values = hfsnef_df[protein].values
            hfnef_values = hfnef_df[protein].values
            
            # 清除极小值，将其替换为0
            min_threshold = 0.001
            hfsnef_values = np.where(hfsnef_values < min_threshold, 0, hfsnef_values)
            hfnef_values = np.where(hfnef_values < min_threshold, 0, hfnef_values)
            
            # 计算每组的均值
            hfsnef_mean = np.mean(hfsnef_values)
            hfnef_mean = np.mean(hfnef_values)
            
            # 计算倍数变化
            if hfsnef_mean > 0 and hfnef_mean > 0:
                fold_change = hfsnef_mean / hfnef_mean if hfsnef_mean > hfnef_mean else hfnef_mean / hfsnef_mean
            else:
                # 如果其中一组均值为0，设置一个大的倍数变化值
                fold_change = 999 if (hfsnef_mean > 0 or hfnef_mean > 0) else 1
            
            # 进行t检验
            t_stat, p_value = stats.ttest_ind(hfsnef_values, hfnef_values, equal_var=False)
            
            # 确定蛋白质分类
            if p_value < p_threshold and fold_change >= fold_change_threshold:
                significant_proteins += 1
                if hfsnef_mean > hfnef_mean:
                    hfsnef_specific.append(protein)
                else:
                    hfnef_specific.append(protein)
            else:
                non_significant_proteins += 1
                shared.append(protein)
        
        except Exception as e:
            print(f"处理蛋白质 {protein} 时出错: {e}")
            continue
    
    print(f"HFsnEF特异性高表达蛋白质数量: {len(hfsnef_specific)}")
    print(f"HFnEF特异性高表达蛋白质数量: {len(hfnef_specific)}")
    print(f"两组表达相似蛋白质数量: {len(shared)}")
    
    # 创建韦恩图
    plt.figure(figsize=(8, 6))
    venn = venn2(
        subsets=(len(hfnef_specific), len(hfsnef_specific), len(shared)),
        set_labels=('HFnEF', 'HFsnEF')
    )
    
    # 设置韦恩图样式
    venn.get_patch_by_id('10').set_color('lightblue')
    venn.get_patch_by_id('01').set_color('lightgreen')
    venn.get_patch_by_id('11').set_color('lightgray')
    
    # 设置字体大小
    for label in venn.set_labels:
        label.set_fontsize(12)
    for text in venn.subset_labels:
        if text is not None:
            text.set_fontsize(12)
    
    plt.title('HFsnEF组与HFnEF组蛋白质表达韦恩图', fontsize=14)
    
    # 保存图像
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(os.path.join(output_dir, "hfsnef_hfnef_venn_diagram.png"), dpi=300, bbox_inches='tight')
    plt.savefig(os.path.join(output_dir, "hfsnef_hfnef_venn_diagram.svg"), format='svg', bbox_inches='tight')
    
    print(f"韦恩图已保存至 {output_dir} 目录")
    
    # 保存各组特异蛋白质列表到CSV文件
    pd.DataFrame(hfsnef_specific, columns=['Protein']).to_csv(
        os.path.join(output_dir, "hfsnef_specific_proteins.csv"), index=False
    )
    
    pd.DataFrame(hfnef_specific, columns=['Protein']).to_csv(
        os.path.join(output_dir, "hfnef_specific_proteins.csv"), index=False
    )
    
    pd.DataFrame(shared, columns=['Protein']).to_csv(
        os.path.join(output_dir, "shared_proteins.csv"), index=False
    )
    
    print("蛋白质列表已保存至CSV文件")

if __name__ == "__main__":
    main() 