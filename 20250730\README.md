# 非心血管死亡差异代谢物和临床变量的单因素Cox分析

## 项目描述

本项目针对合并数据集`merged_metabolomics_clinical_final.csv`，根据是否发生非心血管死亡，寻找差异代谢物和临床变量，进行单因素Cox比例风险分析，并导出结果。

## 功能特点

- **自动变量识别**：自动识别代谢物变量（CPD_开头）和临床变量
- **单因素Cox回归**：对每个变量单独进行Cox比例风险分析
- **结果格式化**：提供风险比（HR）、95%置信区间和p值
- **显著性标记**：自动标记统计显著性（*p<0.05, **p<0.01, ***p<0.001）
- **结果排序**：按p值排序，显著变量优先显示
- **错误处理**：处理缺失值、样本量不足等情况

## 环境要求

- Python 3.7+
- 所需包见 `requirements.txt`

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保数据文件 `merged_metabolomics_clinical_final.csv` 在当前目录下
2. 运行分析脚本：

```bash
python cox_analysis_script.py
```

## 输入数据要求

数据文件必须包含以下关键列：
- `NonCV_death`：非心血管死亡事件（0=未发生，1=发生）
- `OS`：总生存时间（数值型）
- `CPD_*`：代谢物变量（以CPD_开头的列）
- 其他临床变量（数值型）

## 输出结果

脚本会生成一个CSV文件 `cox_analysis_results_YYYYMMDD_HHMMSS.csv`，包含以下列：

- `Variable`：变量名称
- `Variable_Type`：变量类型（代谢物/临床）
- `N`：分析样本数
- `Events`：事件数
- `HR`：风险比
- `HR_95CI`：风险比及95%置信区间
- `P_value`：p值
- `P_value_formatted`：格式化的p值
- `Significance`：显著性标记
- `Status`：分析状态

## 结果解读

- **HR > 1**：该变量增加非心血管死亡风险
- **HR < 1**：该变量降低非心血管死亡风险
- **p < 0.05**：统计学显著相关

## 注意事项

1. 脚本会自动处理缺失值和异常值
2. 样本量少于10的变量会被标记为"样本量不足"
3. 无变异的变量会被标记为"变量无变异"
4. 分析失败的变量会显示具体错误信息

## 示例输出

```
非心血管死亡差异代谢物和临床变量的单因素Cox分析
============================================================
正在加载数据...
数据加载完成，共有 204 行，477 列
预处理后数据：204 行，477 列
非心血管死亡事件数：25
生存时间范围：6.00 - 810.00

正在识别变量类型...
识别到 276 个代谢物变量
识别到 150 个临床变量

开始Cox回归分析...
正在进行代谢物变量的单因素Cox回归分析...
已完成 50/276 个代谢物变量的分析
...

分析完成！结果摘要：
============================================================
总变量数：426
成功分析的变量数：380
显著相关的变量数（p<0.05）：45

前10个最显著的变量：
  Age (临床): HR=1.045, p=0.012
  NT_proBNPx (临床): HR=1.000, p=0.023
  ...

详细结果已保存到：cox_analysis_results_20250730_143022.csv
```

## 完整工作流程

### 步骤1：运行主分析脚本
```bash
python cox_analysis_script.py
```

### 步骤2：生成摘要报告
```bash
python generate_summary_report.py
```

### 步骤3：创建可视化图表（可选）
```bash
python visualize_results.py
```

## 输出文件说明

1. **cox_analysis_results_YYYYMMDD_HHMMSS.csv** - 完整的分析结果
2. **cox_analysis_summary_report_YYYYMMDD_HHMMSS.txt** - 易读的摘要报告
3. **cox_analysis_visualization_YYYYMMDD_HHMMSS.png** - 可视化图表
4. **significant_variables_summary_YYYYMMDD_HHMMSS.csv** - 显著变量摘要表

## 分析结果解读

### 关键指标说明
- **HR (Hazard Ratio)**：风险比
  - HR > 1：增加非心血管死亡风险
  - HR < 1：降低非心血管死亡风险（保护性因子）
  - HR = 1：无影响

- **95%置信区间**：风险比的可信范围
  - 不包含1：统计学显著
  - 包含1：统计学不显著

- **p值**：统计显著性
  - p < 0.001：极显著（***）
  - p < 0.01：高度显著（**）
  - p < 0.05：显著（*）

### 临床意义解读
1. **保护性因子**（HR < 1）可能具有治疗潜力
2. **危险因子**（HR > 1）需要重点关注和干预
3. **极显著变量**（p < 0.001）建议进行生物学验证

## 技术支持

如有问题，请检查：
1. 数据文件格式是否正确
2. 必需的列是否存在
3. Python环境和依赖包是否正确安装
4. 数据中是否有足够的事件数（建议≥10个事件）
